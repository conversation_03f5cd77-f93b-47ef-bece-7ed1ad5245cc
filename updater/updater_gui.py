#!/usr/bin/env python3
"""Splash-style updater & launcher for Nexus-Crucible.

Usage (systemd ExecStart or manual):
    /path/to/repo/updater/updater_gui.py  [<extra args passed to main app>]

Features
--------
1. Opens a small Tkinter window so PXE-booted devices don’t sit on a blank screen.
2. Updates the local git repo (fast-forward only).
3. Upgrades Python deps when requirements.txt changed.
4. Falls back to headless (console) mode automatically if X/Wayland or tkinter
   isn’t available.
5. Replaces its own process with the real application, so there is only a single
   long-lived PID and no orphaned child processes.

Adjust APP_MODULE below if the entry point of your main program changes.
"""
from __future__ import annotations

import os
import queue
import subprocess
import sys
import threading
from pathlib import Path
import time

# ---------------------------------------------------------------------------
# SETTINGS — tweak if your repo layout changes
# ---------------------------------------------------------------------------
REPO_DIR = Path(__file__).resolve().parent.parent  # <repo root>
# Command to launch the main application (update path if entry point moves)
APP_CMD = [sys.executable, str(REPO_DIR / "run_web_ui.py")] + sys.argv[1:]
DISPLAY = bool(os.environ.get("DISPLAY") or os.environ.get("WAYLAND_DISPLAY"))
# Number of seconds to display the "up to date" message before closing
UPDATER_DISPLAY_DELAY = 3.0  # Adjust this value to change how long the updater stays visible

# ---------------------------------------------------------------------------
# VERSIONING
# ---------------------------------------------------------------------------
# Ensure repo root is on sys.path so we can import the shared version module.
if str(REPO_DIR) not in sys.path:
    sys.path.insert(0, str(REPO_DIR))

try:
    from version import __version__  # noqa: E402  (import after sys.path tweak)
except ImportError:
    __version__ = "unknown"

# ---------------------------------------------------------------------------
# UPDATE LOGIC (runs in a background thread)
# ---------------------------------------------------------------------------

def _run(cmd: list[str], **kw):
    """Run command, raising if non-zero exit code."""
    subprocess.run(cmd, check=True, text=True, **kw)


def updater(status_q: queue.Queue[str]) -> None:
    """Background worker that force-syncs to origin/main for kiosk determinism."""

    def log(msg: str):
        status_q.put(msg)

    # Guard against restart loops: if we've already restarted due to an update
    # once in this process tree, don't attempt to restart again.
    already_restarted = os.environ.get("CRUCIBLE_UPDATED_ONCE") == "1"

    # Kiosk/VM intent: ALWAYS enforce clean state that matches origin/main.
    # We remove the conditional and always reset/clean to avoid merge conflicts forever.

    updated = False
    log("Checking for updates …")
    try:
        # Capture current commit before any network changes
        old_sha = subprocess.check_output(
            ["git", "rev-parse", "HEAD"], cwd=REPO_DIR, text=True
        ).strip()

        # Fetch latest from origin/main quietly
        _run(["git", "fetch", "--quiet", "origin", "main"], cwd=REPO_DIR)

        # Resolve local and remote SHAs deterministically
        local = subprocess.check_output(
            ["git", "rev-parse", "HEAD"], cwd=REPO_DIR, text=True
        ).strip()
        remote = subprocess.check_output(
            ["git", "rev-parse", "origin/main"], cwd=REPO_DIR, text=True
        ).strip()

        # Always ensure determinism on kiosk/VM: discard any local changes/untracked files up-front.
        porcelain = subprocess.check_output(
            ["git", "status", "--porcelain"], cwd=REPO_DIR, text=True
        )
        if porcelain.strip():
            log("Local changes detected; discarding to match origin/main …")
            _run(["git", "reset", "--hard", "origin/main"], cwd=REPO_DIR)
            _run(["git", "clean", "-fdx"], cwd=REPO_DIR)
            local = subprocess.check_output(
                ["git", "rev-parse", "HEAD"], cwd=REPO_DIR, text=True
            ).strip()

        if local != remote:
            # In kiosk/VM we always want a clean, deterministic image that matches origin/main,
            # never a merge. Avoid 'git pull' entirely to prevent merge warnings and failures.
            log("Synchronizing to origin/main …")
            _run(["git", "reset", "--hard", "origin/main"], cwd=REPO_DIR)
            _run(["git", "clean", "-fdx"], cwd=REPO_DIR)

            # Capture new commit after sync
            new_sha = subprocess.check_output(
                ["git", "rev-parse", "HEAD"], cwd=REPO_DIR, text=True
            ).strip()

            updated = new_sha != old_sha

            if updated:
                # Determine file-level changes between old and new
                changed = subprocess.check_output(
                    ["git", "diff", "--name-only", old_sha, new_sha],
                    cwd=REPO_DIR,
                    text=True,
                ).splitlines()

                if "requirements.txt" in changed:
                    log("Upgrading Python deps …")
                    _run(
                        [
                            sys.executable,
                            "-m",
                            "pip",
                            "install",
                            "-r",
                            "requirements.txt",
                            "--upgrade",
                            "--quiet",
                        ],
                        cwd=REPO_DIR,
                    )
            else:
                log("No effective changes after sync.")
        else:
            log("Already up-to-date.")
    except subprocess.CalledProcessError as exc:
        # If sync fails, do not attempt to restart; run the last known version.
        log(f"Update failed (code {exc.returncode}); running last known version …")
        updated = False
    finally:
        # If we detected an update but already restarted once, avoid infinite loop
        if updated and already_restarted:
            status_q.put("__DONE__")
        else:
            status_q.put("__UPDATED__" if updated else "__DONE__")

# ---------------------------------------------------------------------------
# GUI HELPERS
# ---------------------------------------------------------------------------

def start_gui(status_q: queue.Queue[str]) -> None:
    """Render splash window and live-update status label."""
    import tkinter as tk
    from tkinter import ttk

    root = tk.Tk()
    root.overrideredirect(True)
    window_width = 420
    window_height = 160
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width // 2) - (window_width // 2)
    y = (screen_height // 2) - (window_height // 2)
    root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    root.configure(bg="black")

    ttk.Style().configure("Splash.TLabel", foreground="white", background="black")

    ttk.Label(
        root,
        text="Nexus",
        font=("Helvetica", 26, "bold"),
        style="Splash.TLabel",
    ).pack(pady=(24, 4))
    # Show current application version just below the title
    ttk.Label(
        root,
        text=f"Version {__version__}",
        font=("Helvetica", 12),
        style="Splash.TLabel",
    ).pack(pady=(0, 8))
    status_lbl = ttk.Label(root, text="Starting …", style="Splash.TLabel")
    status_lbl.pack()

    def pump():
        try:
            while True:
                msg: str = status_q.get_nowait()
                if msg == "__UPDATED__":
                    status_lbl.configure(text="Update installed. Restarting …")
                    root.update()
                    time.sleep(1.5)
                    # Set guard to avoid restart loops if git continues to report diffs
                    os.environ["CRUCIBLE_UPDATED_ONCE"] = "1"
                    os.execvp(sys.executable, [sys.executable] + sys.argv)
                if msg == "__DONE__":
                    status_lbl.configure(text="Ready to launch...")
                    root.update()
                    time.sleep(UPDATER_DISPLAY_DELAY)  # Add delay before closing
                    root.destroy()
                    launch_server_and_browser()
                    return  # stop pump after launching
        except queue.Empty:
            pass
        if root.winfo_exists():
            root.after(120, pump)

    root.after(120, pump)
    root.mainloop()

# ---------------------------------------------------------------------------
# LAUNCH WEB UI + FIREFOX
# ---------------------------------------------------------------------------

def launch_server_and_browser():
    """Start the web UI (via sudo) then open Firefox kiosk."""
    try:
        env = os.environ.copy()
        env["FLASK_DEBUG"] = "0"  # disable debug/reloader so it binds once
        # Ensure repo root is importable by any child process (server and visual tests)
        existing_pp = env.get("PYTHONPATH", "")
        env["PYTHONPATH"] = f"{str(REPO_DIR)}{os.pathsep}{existing_pp}" if existing_pp else str(REPO_DIR)
        server_proc = subprocess.Popen(["sudo"] + APP_CMD, env=env)
    except FileNotFoundError as exc:
        print(f"Failed to start server: {exc}", file=sys.stderr)
        sys.exit(1)

    # Wait until the HTTP endpoint returns 200 OK (max 10 s)
    import urllib.request, urllib.error
    start_time = time.time()
    while time.time() - start_time < 10:
        try:
            with urllib.request.urlopen("http://127.0.0.1:5000", timeout=2) as resp:
                if resp.status == 200:
                    time.sleep(2)  # give server a moment to finish startup tasks
                    break
        except Exception:
            time.sleep(0.5)
    else:
        print("Warning: Server did not respond with HTTP 200 within 10 s.", file=sys.stderr)

    try:
        subprocess.Popen([
            "chromium",
            "--kiosk",
            "--incognito",
            "--noerrdialogs",
            "--disable-infobars",
            "--password-store=basic",
            "--no-first-run",
            "--disable-translate",
            "--disable-gpu",
            "--disable-software-rasterizer",
            "--disable-features=PushMessaging",
            "http://localhost:5000",
        ], env=os.environ)
    except FileNotFoundError:
        print("Warning: Chromium not found; kiosk window not opened.", file=sys.stderr)

    # Wait so this wrapper process stays alive alongside the server
    try:
        server_proc.wait()
    except KeyboardInterrupt:
        server_proc.terminate()
        server_proc.wait()

# ---------------------------------------------------------------------------
# ENTRY POINT
# ---------------------------------------------------------------------------

def main() -> None:
    status_q: queue.Queue[str] = queue.Queue()

    # Start updater thread
    threading.Thread(target=updater, args=(status_q,), daemon=True).start()

    try:
        start_gui(status_q)
    except (ModuleNotFoundError, RuntimeError):  # e.g. tkinter not installed
        pass  # fall back to CLI

    # CLI fallback: just print status lines until done
    while True:
        msg = status_q.get()
        if msg == "__UPDATED__":
            print("Update installed. Restarting …", flush=True)
            time.sleep(0.5)
            os.environ["CRUCIBLE_UPDATED_ONCE"] = "1"
            os.execvp(sys.executable, [sys.executable] + sys.argv)
        if msg == "__DONE__":
            break
        print(msg, flush=True)

    launch_server_and_browser()


if __name__ == "__main__":
    main()
