#!/usr/bin/env python3
"""
Quick manual test to consume a credit with Nexus website API from Crucible.

Usage:
  export NEXUS_WEBSITE_BASE_URL="https://your-nexus-site"
  export NEXUS_API_KEY="<your_api_key>"
  python3 scripts/test_consume_credit.py
"""
from __future__ import annotations
import json
import sys
import os

# Make local 'agent' package importable when running this script directly
REPO_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

from agent.core import nexus_credits
from agent.hardware import system_info


def main() -> int:
    serial = system_info.get_machine_serial_number() or "UNKNOWN"
    model = system_info.get_model() or system_info.get_system_product_name() or "UNKNOWN"

    print(f"Device serial={serial} model={model}")

    op_id = nexus_credits.start_operation(
        operation_type=nexus_credits.OperationType.DIAGNOSTICS_ONLY,
        device_serial=serial,
        device_model=model,
        metadata={"invoker": "scripts/test_consume_credit.py"},
    )

    if op_id:
        print(json.dumps({"success": True, "operationId": op_id}))
        return 0
    else:
        print(json.dumps({"success": False}))
        return 1


if __name__ == "__main__":
    sys.exit(main())
