#!/usr/bin/env node
/*
  Seed a test user for Stripe guest checkouts.
  - Uses TEST_USER_ID and TEST_CHECKOUT_EMAIL from .env if present
  - Otherwise generates a UUID for id and uses a default email
*/

require('dotenv/config');
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function main() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL is not set. Please add it to your .env');
    process.exit(1);
  }

  const id = process.env.TEST_USER_ID || crypto.randomUUID();
  const email = process.env.TEST_CHECKOUT_EMAIL || '<EMAIL>';

  // Ensure unique email string
  const firstName = 'Test';
  const lastName = 'User';
  const password = 'placeholder';

  console.log('Seeding test user...');
  console.log({ id, email });

  try {
    // Upsert by id to guarantee the chosen id exists
    const user = await prisma.user.upsert({
      where: { id },
      update: { email, firstName, lastName },
      create: {
        id,
        email,
        password,
        firstName,
        lastName,
      },
    });

    console.log('Seeded user:', { id: user.id, email: user.email });

    if (!process.env.TEST_USER_ID) {
      console.log('\nIMPORTANT: Add this to your .env to link guest checkouts to this user:');
      console.log(`TEST_USER_ID=${id}`);
    }
  } catch (e) {
    console.error('Failed to seed user:', e);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
