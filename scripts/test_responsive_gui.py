#!/usr/bin/env python3
"""
Test script for responsive GUI design
This script tests the responsive design improvements to the main window.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agent.gui.theme import COLORS, FONTS, SIZES, get_font_size_for_resolution, get_padding_for_resolution, get_log_height_for_resolution

def test_responsive_functions():
    """Test the responsive design functions."""
    print("Testing responsive design functions...")
    
    # Test different resolutions
    test_resolutions = [
        (1024, 768),    # Small
        (1440, 900),    # Medium
        (1920, 1080),   # Standard
        (2560, 1440),   # Large
        (3840, 2160),   # 4K
    ]
    
    for width, height in test_resolutions:
        font_size = get_font_size_for_resolution(width, height)
        padding = get_padding_for_resolution(width, height)
        log_height = get_log_height_for_resolution(width, height)
        
        print(f"Resolution {width}x{height}:")
        print(f"  Font size: {font_size}")
        print(f"  Padding: {padding}")
        print(f"  Log height: {log_height}")
        print()

class ResponsiveTestWindow(tk.Tk):
    """Test window to demonstrate responsive design."""
    
    def __init__(self):
        super().__init__()
        
        self.title("Responsive Design Test")
        self.geometry("800x600")
        
        # Test different font sizes
        self.current_font_size = COLORS["normal_font_size"]
        
        self.setup_ui()
        self.create_controls()
    
    def setup_ui(self):
        """Set up the test UI."""
        # Main frame
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        self.title_label = ttk.Label(
            main_frame, 
            text="Responsive Design Test", 
            font=(FONTS["normal_font"], self.current_font_size + 4, "bold")
        )
        self.title_label.pack(pady=(0, 10))
        
        # Test labels with different sizes
        self.test_labels = []
        for i, text in enumerate(["Small Text", "Medium Text", "Large Text"]):
            label = ttk.Label(
                main_frame,
                text=text,
                font=(FONTS["normal_font"], self.current_font_size + i)
            )
            label.pack(pady=2)
            self.test_labels.append(label)
        
        # Test entry
        self.test_entry = ttk.Entry(
            main_frame,
            font=(FONTS["normal_font"], self.current_font_size),
            width=30
        )
        self.test_entry.pack(pady=5)
        self.test_entry.insert(0, "Test entry field")
        
        # Test button
        self.test_button = ttk.Button(
            main_frame,
            text="Test Button",
            font=(FONTS["normal_font"], self.current_font_size)
        )
        self.test_button.pack(pady=5)
        
        # Test text area
        self.test_text = tk.Text(
            main_frame,
            font=(FONTS["log_font"], self.current_font_size - 1),
            height=8,
            width=50
        )
        self.test_text.pack(pady=5, fill=tk.BOTH, expand=True)
        self.test_text.insert("1.0", "This is a test text area.\nIt should resize with the font size.\nTesting responsive design...")
    
    def create_controls(self):
        """Create control buttons for testing."""
        control_frame = ttk.Frame(self)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Font size controls
        ttk.Label(control_frame, text="Font Size:").pack(side=tk.LEFT, padx=5)
        
        for size_name, size_value in [
            ("Tiny", COLORS["tiny_font_size"]),
            ("Small", COLORS["small_font_size"]),
            ("Medium", COLORS["medium_font_size"]),
            ("Normal", COLORS["normal_font_size"]),
            ("Large", COLORS["large_font_size"]),
            ("XLarge", COLORS["xlarge_font_size"])
        ]:
            btn = ttk.Button(
                control_frame,
                text=size_name,
                command=lambda s=size_value: self.update_font_size(s)
            )
            btn.pack(side=tk.LEFT, padx=2)
        
        # Resolution simulation controls
        ttk.Separator(control_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=10, fill=tk.Y)
        ttk.Label(control_frame, text="Simulate Resolution:").pack(side=tk.LEFT, padx=5)
        
        for res_name, (width, height) in [
            ("1024x768", (1024, 768)),
            ("1440x900", (1440, 900)),
            ("1920x1080", (1920, 1080)),
            ("2560x1440", (2560, 1440))
        ]:
            btn = ttk.Button(
                control_frame,
                text=res_name,
                command=lambda w=width, h=height: self.simulate_resolution(w, h)
            )
            btn.pack(side=tk.LEFT, padx=2)
    
    def update_font_size(self, new_size):
        """Update all fonts to the new size."""
        self.current_font_size = new_size
        
        # Update title
        self.title_label.config(font=(FONTS["normal_font"], self.current_font_size + 4, "bold"))
        
        # Update test labels
        for i, label in enumerate(self.test_labels):
            label.config(font=(FONTS["normal_font"], self.current_font_size + i))
        
        # Update entry
        self.test_entry.config(font=(FONTS["normal_font"], self.current_font_size))
        
        # Update button
        self.test_button.config(font=(FONTS["normal_font"], self.current_font_size))
        
        # Update text area
        self.test_text.config(font=(FONTS["log_font"], self.current_font_size - 1))
        
        print(f"Updated font size to: {new_size}")
    
    def simulate_resolution(self, width, height):
        """Simulate a specific resolution and apply responsive settings."""
        font_size = get_font_size_for_resolution(width, height)
        padding = get_padding_for_resolution(width, height)
        log_height = get_log_height_for_resolution(width, height)
        
        self.update_font_size(font_size)
        
        # Update text area height
        self.test_text.config(height=log_height)
        
        print(f"Simulated resolution {width}x{height}:")
        print(f"  Font size: {font_size}")
        print(f"  Padding: {padding}")
        print(f"  Log height: {log_height}")

def main():
    """Main function to run the test."""
    print("Starting responsive design test...")
    
    # Test the responsive functions
    test_responsive_functions()
    
    # Create and run the test window
    app = ResponsiveTestWindow()
    app.mainloop()

if __name__ == "__main__":
    main()
