#!/usr/bin/env python3
"""
Test script for responsive keyboard test design
This script tests the responsive design improvements to the keyboard test.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_keyboard_responsive():
    """Test the responsive keyboard test window."""
    print("Testing responsive keyboard test...")
    
    # Create a simple root window
    root = tk.Tk()
    root.title("Keyboard Test - Responsive Design Test")
    root.geometry("400x300")
    
    def log_callback(message, level="info"):
        print(f"[{level.upper()}] {message}")
    
    def run_keyboard_test():
        """Run the keyboard test."""
        try:
            from agent.tests.keyboard_test import KeyboardTestWindow
            
            # Create the keyboard test window
            keyboard_test = KeyboardTestWindow(root, log_callback)
            
            # Wait for the test window to close
            root.wait_window(keyboard_test)
            
            print("Keyboard test completed!")
            
        except Exception as e:
            print(f"Error running keyboard test: {e}")
            import traceback
            traceback.print_exc()
    
    # Create a simple UI to launch the test
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(
        main_frame,
        text="Keyboard Test - Responsive Design",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    info_label = ttk.Label(
        main_frame,
        text="This test will open a fullscreen keyboard test window.\n"
             "The buttons should now be clearly visible and properly sized\n"
             "for your screen resolution.",
        justify=tk.CENTER
    )
    info_label.pack(pady=(0, 20))
    
    test_button = ttk.Button(
        main_frame,
        text="Run Keyboard Test",
        command=run_keyboard_test,
        style="TButton"
    )
    test_button.pack(pady=10)
    
    close_button = ttk.Button(
        main_frame,
        text="Close",
        command=root.quit,
        style="TButton"
    )
    close_button.pack(pady=5)
    
    # Show resolution info
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    resolution_label = ttk.Label(
        main_frame,
        text=f"Detected screen resolution: {screen_width}x{screen_height}",
        font=("Arial", 10)
    )
    resolution_label.pack(pady=(20, 0))
    
    # Test responsive functions
    try:
        from agent.gui.theme import get_font_size_for_resolution, get_padding_for_resolution
        
        font_size = get_font_size_for_resolution(screen_width, screen_height)
        padding = get_padding_for_resolution(screen_width, screen_height)
        
        responsive_info = ttk.Label(
            main_frame,
            text=f"Calculated font size: {font_size}\n"
                 f"Calculated padding: {padding}",
            font=("Arial", 10),
            justify=tk.CENTER
        )
        responsive_info.pack(pady=5)
        
    except Exception as e:
        error_label = ttk.Label(
            main_frame,
            text=f"Error testing responsive functions: {e}",
            foreground="red"
        )
        error_label.pack(pady=5)
    
    print(f"Screen resolution: {screen_width}x{screen_height}")
    print("Click 'Run Keyboard Test' to test the responsive design improvements.")
    
    root.mainloop()

if __name__ == "__main__":
    test_keyboard_responsive()
