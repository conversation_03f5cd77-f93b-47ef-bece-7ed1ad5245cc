#!/usr/bin/env python3
"""
Test script for enhanced system information formatting
This script tests the human-readable CPU and GPU formatting improvements.
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_cpu_formatting():
    """Test the CPU formatting function with various inputs."""
    print("Testing CPU formatting...")
    
    try:
        from agent.hardware.system_info import _format_cpu_brand
        
        # Test cases for CPU formatting
        test_cases = [
            # Intel Core series
            "Intel(R) Core(TM) i7-1185G7 CPU @ 3.00GHz",
            "Intel Core i5-11400H @ 2.70GHz",
            "Intel(R) Core(TM) i9-12900K CPU @ 3.20GHz",
            "Intel Core i3-10100 CPU @ 3.60GHz",
            
            # AMD Ryzen series
            "AMD Ryzen 7 5800X 8-Core Processor",
            "AMD Ryzen 9 5900X 12-Core Processor",
            "AMD Ryzen 5 3600 6-Core Processor",
            
            # Other processors
            "Intel Xeon E5-2680 v4 @ 2.40GHz",
            "Intel Pentium Gold G6400 @ 4.00GHz",
            "AMD EPYC 7742 64-Core Processor",
            
            # Edge cases
            "Unknown Processor",
            "",
            None
        ]
        
        print("\nCPU Formatting Results:")
        print("-" * 50)
        for i, test_case in enumerate(test_cases):
            try:
                if test_case is None:
                    result = "None input"
                else:
                    result = _format_cpu_brand(test_case)
                print(f"{i+1:2d}. Input:  {test_case}")
                print(f"    Output: {result}")
                print()
            except Exception as e:
                print(f"{i+1:2d}. Input:  {test_case}")
                print(f"    Error:  {e}")
                print()
                
    except Exception as e:
        print(f"Error testing CPU formatting: {e}")
        import traceback
        traceback.print_exc()

def test_gpu_formatting():
    """Test the GPU formatting function with various inputs."""
    print("Testing GPU formatting...")
    
    try:
        from agent.hardware.system_info import _format_gpu_name
        
        # Test cases for GPU formatting
        test_cases = [
            # NVIDIA GeForce series
            "NVIDIA GeForce GTX 1080 Ti",
            "GeForce RTX 3080 10GB",
            "NVIDIA GeForce RTX 4090 24GB",
            "GeForce GTX 1650 SUPER 4GB",
            
            # NVIDIA Quadro series
            "NVIDIA Quadro A5000 24GB",
            "Quadro RTX 6000 48GB",
            "NVIDIA Quadro P2000 5GB",
            
            # NVIDIA Tesla series
            "NVIDIA Tesla V100 32GB",
            "Tesla K80 24GB",
            
            # AMD Radeon series
            "AMD Radeon RX 6800 XT 16GB",
            "Radeon RX 580 8GB",
            "AMD Radeon Pro W6800 32GB",
            "Radeon HD 7970 3GB",
            
            # Intel integrated graphics
            "Intel UHD Graphics 630",
            "Intel HD Graphics 4000",
            "Intel Iris Pro Graphics 5200",
            
            # Raw/messy inputs
            "NVIDIA Corporation GeForce GTX 1070 (TM) Graphics Family 8GB",
            "Advanced Micro Devices, Inc. Radeon RX 6700 XT Graphics",
            "Intel(R) UHD Graphics 620",
            
            # Edge cases
            "Unknown GPU",
            "No GPU detected",
            "",
            None
        ]
        
        print("\nGPU Formatting Results:")
        print("-" * 50)
        for i, test_case in enumerate(test_cases):
            try:
                if test_case is None:
                    result = "None input"
                else:
                    result = _format_gpu_name(test_case)
                print(f"{i+1:2d}. Input:  {test_case}")
                print(f"    Output: {result}")
                print()
            except Exception as e:
                print(f"{i+1:2d}. Input:  {test_case}")
                print(f"    Error:  {e}")
                print()
                
    except Exception as e:
        print(f"Error testing GPU formatting: {e}")
        import traceback
        traceback.print_exc()

def test_actual_system_info():
    """Test the actual system information functions."""
    print("Testing actual system information...")
    
    try:
        from agent.hardware.system_info import get_cpu_info_detailed, get_gpu_info, get_system_info
        
        print("\nActual System Information:")
        print("=" * 50)
        
        # Test CPU info
        print("CPU Information:")
        cpu_info = get_cpu_info_detailed()
        print(f"  {cpu_info}")
        print()
        
        # Test GPU info
        print("GPU Information:")
        gpu_info = get_gpu_info()
        if isinstance(gpu_info, list):
            for i, gpu in enumerate(gpu_info):
                print(f"  GPU {i+1}: {gpu}")
        else:
            print(f"  {gpu_info}")
        print()
        
        # Test full system info (just the relevant parts)
        print("Full System Info (CPU & GPU only):")
        sys_info = get_system_info()
        print(f"  CPU: {sys_info.get('cpu', 'N/A')}")
        
        gpus = sys_info.get('gpus', [])
        if isinstance(gpus, list):
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i+1}: {gpu}")
        else:
            print(f"  GPU: {gpus}")
        
    except Exception as e:
        print(f"Error testing actual system info: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function to run all tests."""
    print("System Information Formatting Test")
    print("=" * 50)
    
    # Test CPU formatting
    test_cpu_formatting()
    
    print("\n" + "=" * 50)
    
    # Test GPU formatting
    test_gpu_formatting()
    
    print("\n" + "=" * 50)
    
    # Test actual system information
    test_actual_system_info()
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    main()
