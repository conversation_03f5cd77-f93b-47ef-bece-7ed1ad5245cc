#!/usr/bin/env python3
"""
Dev helper: start Flask on 127.0.0.1:5000 and launch a Chromium-based browser (Chromium or Google Chrome) in true kiosk/app mode.

Usage (no flags needed):
  python3 scripts/dev.py
  # or
  ./scripts/dev.py

This script:
- Ensures the repo root is on sys.path
- Starts the Flask app (run_web_ui.py: app) bound to 127.0.0.1:5000 in a background thread
- Waits until the port is listening
- Autodetects a Chromium-based browser in this order:
    CHROMIUM_BIN env -> chromium-browser -> chromium -> google-chrome -> /usr/bin/chromium-browser -> /usr/bin/google-chrome
- For Google Chrome, uses an isolated profile by default: --user-data-dir=/tmp/nexus_kiosk_profile
- Launches with --kiosk --app=http://127.0.0.1:5000/ and recommended flags
- Detects "Opening in existing browser session" and retries once with a unique temp user-data-dir
- Ctrl+C will terminate the browser; Flask runs in a daemon thread
"""

import os
import sys
import time
import socket
import shutil
import subprocess
import threading
import tempfile
import signal

REPO_ROOT = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if REPO_ROOT not in sys.path:
    sys.path.insert(0, REPO_ROOT)

HOST = os.environ.get('FLASK_RUN_HOST', '127.0.0.1')
PORT = int(os.environ.get('FLASK_RUN_PORT', '5000'))

APP_URL = f'http://{HOST}:{PORT}/'

def is_google_chrome(path: str) -> bool:
    base = os.path.basename(path or "").lower()
    return base.startswith("google-chrome")

def autodetect_browser() -> str:
    env = os.environ.get("CHROMIUM_BIN")
    if env and (shutil.which(env) or os.path.isabs(env)):
        return env
    for cand in ("chromium-browser", "chromium", "google-chrome", "/usr/bin/chromium-browser", "/usr/bin/google-chrome"):
        if shutil.which(cand) or os.path.exists(cand):
            return cand
    # last resort
    return "/usr/bin/chromium-browser"

def build_browser_cmd(browser_bin: str, app_url: str, force_profile: str | None = None) -> list[str]:
    flags = [
        "--kiosk",
        f"--app={app_url}",
        "--incognito",
        "--noerrdialogs",
        "--disable-infobars",
        "--overscroll-history-navigation=0",
        "--autoplay-policy=no-user-gesture-required",
        "--disable-session-crashed-bubble",
        "--no-first-run",
        "--disable-features=TranslateUI",
        "--no-default-browser-check",
        "--new-window",
    ]
    if force_profile:
        flags.append(f"--user-data-dir={force_profile}")
    else:
        if is_google_chrome(browser_bin):
            flags.append(f"--user-data-dir={os.path.join(tempfile.gettempdir(), 'nexus_kiosk_profile')}")
    return [browser_bin] + flags

def is_port_open(host: str, port: int, timeout: float = 0.25) -> bool:
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.settimeout(timeout)
        try:
            s.connect((host, port))
            return True
        except Exception:
            return False

def wait_for_server(host: str, port: int, max_wait: float = 15.0) -> bool:
    start = time.time()
    while time.time() - start < max_wait:
        if is_port_open(host, port):
            return True
        time.sleep(0.2)
    return is_port_open(host, port)

def run_flask_background():
    # Import here to avoid side effects before we set sys.path
    from run_web_ui import app, server_logger  # type: ignore
    # Run Flask in this thread; mark thread as daemon from caller
    server_logger.info(f"Starting Flask on {HOST}:{PORT} (debug=False)")
    app.run(host=HOST, port=PORT, debug=False, use_reloader=False)

def launch_and_capture(cmd: list[str]) -> tuple[int, str]:
    print(f"[dev_kiosk_launcher] Exec: {' '.join(cmd)}")
    proc = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
    out_chunks: list[str] = []
    try:
        if proc.stdout:
            for line in proc.stdout:
                out_chunks.append(line)
    except Exception:
        pass
    finally:
        proc.wait()
    return proc.returncode, "".join(out_chunks)

def main():
    # Start Flask in a daemon thread
    flask_thread = threading.Thread(target=run_flask_background, daemon=True)
    flask_thread.start()

    print(f"[dev_kiosk_launcher] Waiting for Flask to listen at {APP_URL} ...")
    if not wait_for_server(HOST, PORT, max_wait=25.0):
        print("[dev_kiosk_launcher] ERROR: Flask did not start listening in time.", file=sys.stderr)
        sys.exit(1)
    print("[dev_kiosk_launcher] Flask is up. Launching kiosk browser...")

    browser_bin = autodetect_browser()
    if not (shutil.which(browser_bin) or os.path.exists(browser_bin)):
        print(f"[dev_kiosk_launcher] ERROR: Browser binary not found: {browser_bin}. "
              f"Set CHROMIUM_BIN to an absolute path.", file=sys.stderr)
        sys.exit(2)

    # First attempt
    code, out = launch_and_capture(build_browser_cmd(browser_bin, APP_URL))
    reused = ("Opening in existing browser session" in out)

    print(f"[dev_kiosk_launcher] Browser exited with code {code}.")
    tail = "\n".join(out.strip().splitlines()[-20:]) if out else ""
    if tail:
        print("[dev_kiosk_launcher] Browser output (tail):\n" + tail)

    # Retry once with a unique temp profile if session was reused or immediate exit
    if reused or code == 0:
        unique_dir = tempfile.mkdtemp(prefix="nexus_kiosk_profile_")
        print(f"[dev_kiosk_launcher] Retrying with isolated profile: {unique_dir}")
        code2, out2 = launch_and_capture(build_browser_cmd(browser_bin, APP_URL, force_profile=unique_dir))
        print(f"[dev_kiosk_launcher] Browser exited with code {code2} on retry.")
        tail2 = "\n".join(out2.strip().splitlines()[-20:]) if out2 else ""
        if tail2:
            print("[dev_kiosk_launcher] Browser output (tail, retry):\n" + tail2)

    # No foreground wait; process will end when browser exits; Flask daemon thread ends with process.

if __name__ == '__main__':
    main()
# The remainder of the file previously duplicated a more complex launcher.
# It has been consolidated into the single flow above to provide:
# - Browser autodetection (Chromium/Chrome)
# - Default isolated profile for Google Chrome
# - One retry with unique profile when an existing session is reused
# - No flags required for typical local development