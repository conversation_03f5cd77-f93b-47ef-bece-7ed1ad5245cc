#!/bin/bash
set -e

# Navigate to project directory
cd /home/<USER>/DEV/Crucible

# Pull latest repo changes via updater GUI
/home/<USER>/DEV/Crucible/venv/bin/python /home/<USER>/DEV/Crucible/updater/updater_gui.py

# Start the web UI in background
/home/<USER>/DEV/Crucible/venv/bin/python /home/<USER>/DEV/Crucible/run_web_ui.py &
WEB_UI_PID=$!

# Give server time to start
sleep 5

# Launch Firefox in kiosk mode pointing at local server
firefox --kiosk --private-window http://localhost:5000 &

# Wait for web UI process to exit
wait $WEB_UI_PID
exit 0

