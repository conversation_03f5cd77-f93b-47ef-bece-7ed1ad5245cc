#!/usr/bin/env bash
set -euo pipefail

# Nexus one-click start script
# - Brings up Docker stack
# - Waits briefly
# - Opens browser to the UI

REPO_DIR="/home/<USER>/cruciblenexus/Crucible"
COMPOSE_FILE="$REPO_DIR/Nexus/infra/docker-compose.yml"
UI_URL="http://localhost:8080"

# Find docker and compose command
if ! command -v docker &>/dev/null; then
  echo "Docker is required but not found in PATH." >&2
  exit 1
fi

COMPOSE_CMD=""
if docker compose version &>/dev/null; then
  COMPOSE_CMD="docker compose"
elif command -v docker-compose &>/dev/null; then
  COMPOSE_CMD="docker-compose"
else
  echo "Neither 'docker compose' nor 'docker-compose' found. Please install Docker Compose." >&2
  exit 1
fi

# Bring up the stack
echo "Starting Nexus stack via $COMPOSE_CMD..."
$COMPOSE_CMD -f "$COMPOSE_FILE" up -d

# Optional: show brief status
echo "\nContainers status:"
$COMPOSE_CMD -f "$COMPOSE_FILE" ps || true

# Give services a moment
sleep 2

# Open browser (Linux desktop)
if command -v xdg-open &>/dev/null; then
  xdg-open "$UI_URL" >/dev/null 2>&1 || true
else
  echo "Open $UI_URL in your browser." 
fi

echo "Nexus is starting. UI should be available shortly at $UI_URL"
