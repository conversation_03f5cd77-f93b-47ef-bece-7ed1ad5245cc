import { prisma } from './db.js';
import crypto from 'crypto';

/**
 * Generate a new API key
 * Returns the raw key (to show once) and the hashed version (to store)
 */
export function generateApiKey() {
  // Generate a 32-byte random key
  const rawKey = crypto.randomBytes(32).toString('base64url');
  // Add a prefix for easy identification
  const fullKey = `nxs_${rawKey}`;
  // Hash it for storage
  const keyHash = hashApiKey(fullKey);
  // Get last 4 chars for identification
  const lastFourChars = fullKey.slice(-4);
  
  return {
    rawKey: fullKey,
    keyHash,
    lastFourChars
  };
}

/**
 * Hash an API key for storage
 */
export function hashApiKey(apiKey) {
  return crypto.createHash('sha256').update(apiKey).digest('hex');
}

/**
 * Authenticate an API request using Bearer token
 * Returns the API key record and user if valid, null otherwise
 */
export async function authenticateApiRequest(request) {
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const apiKey = authHeader.substring(7);
  const keyHash = hashApiKey(apiKey);

  // Find the API key and include user
  const apiKeyRecord = await prisma.apiKey.findUnique({
    where: { keyHash },
    include: { user: true }
  });

  if (!apiKeyRecord || !apiKeyRecord.isActive) {
    return null;
  }

  // Check expiration
  if (apiKeyRecord.expiresAt && apiKeyRecord.expiresAt < new Date()) {
    return null;
  }

  // Update last used timestamp (non-blocking)
  prisma.apiKey.update({
    where: { id: apiKeyRecord.id },
    data: { lastUsedAt: new Date() }
  }).catch(console.error);

  return apiKeyRecord;
}

/**
 * Log API usage for audit trail
 */
export async function logApiUsage({
  apiKeyId,
  action,
  operationId = null,
  creditsUsed = 0,
  deviceSerial = null,
  deviceModel = null,
  metadata = null,
  ipAddress = null,
  userAgent = null
}) {
  return await prisma.usageLog.create({
    data: {
      apiKeyId,
      action,
      operationId,
      creditsUsed,
      deviceSerial,
      deviceModel,
      metadata,
      ipAddress,
      userAgent
    }
  });
}

/**
 * Get client IP address from request
 */
export function getClientIp(request) {
  return request.headers.get('x-forwarded-for')?.split(',')[0] || 
         request.headers.get('x-real-ip') || 
         'unknown';
}
