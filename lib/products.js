// Product catalog configuration for ITAD software
export const CREDIT_PACKS = [
  {
    id: 'credits_100',
    name: '100 Credits',
    credits: 100,
    price: 450,
    pricePerCredit: 4.50,
    description: 'Perfect for small operations or trying out the service',
    popular: false,
    stripePriceId: 'price_credits_100' // Replace with actual Stripe price ID
  },
  {
    id: 'credits_500',
    name: '500 Credits',
    credits: 500,
    price: 2000,
    pricePerCredit: 4.00,
    description: 'Best value for growing ITAD operations',
    popular: true,
    stripePriceId: 'price_credits_500' // Replace with actual Stripe price ID
  },
  {
    id: 'credits_1000',
    name: '1000 Credits',
    credits: 1000,
    price: 3500,
    pricePerCredit: 3.50,
    description: 'Maximum savings for high-volume operations',
    popular: false,
    stripePriceId: 'price_credits_1000' // Replace with actual Stripe price ID
  }
];

export const SUBSCRIPTION_PLANS = [
  {
    id: 'pro',
    name: 'Pro',
    price: 249,
    workstations: 5,
    description: 'Perfect for small to medium ITAD operations',
    features: [
      'Up to 5 workstations',
      'Unlimited wipes & diagnostics',
      'Cloud reporting dashboard',
      'Email support',
      'NIST 800-88 compliance',
      'Certificate generation'
    ],
    popular: false,
    stripePriceId: 'price_subscription_pro' // Replace with actual Stripe price ID
  },
  {
    id: 'business',
    name: 'Business',
    price: 499,
    workstations: 15,
    description: 'Ideal for growing teams with advanced workflow needs',
    features: [
      'Up to 15 workstations',
      'Unlimited wipes & diagnostics',
      'Advanced cloud reporting',
      'Priority support',
      'API access',
      'ERP integration',
      'Custom workflows',
      'Advanced analytics'
    ],
    popular: true,
    stripePriceId: 'price_subscription_business' // Replace with actual Stripe price ID
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 'Custom',
    workstations: 'Unlimited',
    description: 'Complete solution for large-scale operations',
    features: [
      'Unlimited workstations',
      'White-label options',
      'On-premise deployment',
      '24/7 dedicated support',
      'Custom development',
      'SLA guarantee',
      'Dedicated account manager',
      'Custom compliance certifications'
    ],
    popular: false,
    stripePriceId: null // Contact sales
  }
];

export const PERPETUAL_LICENSES = [
  {
    id: 'industrial_server',
    name: 'Industrial Server License',
    price: 4999,
    description: 'One-time purchase for unlimited server use',
    features: [
      'Unlimited server operations',
      'Bulk hard drive processing',
      'Network deployment',
      'Certificate generation',
      'NIST 800-88 compliance',
      'Basic support included'
    ],
    stripePriceId: 'price_license_industrial' // Replace with actual Stripe price ID
  },
  {
    id: 'maintenance',
    name: 'Support & Maintenance',
    price: 499,
    period: 'per year',
    description: 'Optional ongoing support and updates',
    features: [
      'Software updates',
      'New compliance standards',
      'Priority technical support',
      'Feature requests',
      'Bug fixes'
    ],
    stripePriceId: 'price_maintenance_annual' // Replace with actual Stripe price ID
  }
];

export const OPERATION_COSTS = {
  STANDARD_WIPE: 1, // credits
  WIPE_WITH_DIAGNOSTICS: 2, // credits
  DIAGNOSTICS_ONLY: 1 // credits
};

export function getCreditPackById(id) {
  return CREDIT_PACKS.find(pack => pack.id === id);
}

export function getSubscriptionPlanById(id) {
  return SUBSCRIPTION_PLANS.find(plan => plan.id === id);
}

export function getPerpetualLicenseById(id) {
  return PERPETUAL_LICENSES.find(license => license.id === id);
}
