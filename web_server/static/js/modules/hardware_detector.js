/**
 * Hardware Detection Module for Crucible
 * Provides real-time hardware information and compatibility checks
 * Works across diverse PXE-booted hardware (768p to 4K, 2GB+ RAM)
 */

class HardwareDetector {
    constructor() {
        this.hardwareInfo = {};
        this.capabilities = {};
        this.updateInterval = null;
        this.listeners = [];
    }

    /**
     * Initialize hardware detection
     */
    async initialize() {
        await this.detectBasicInfo();
        await this.detectCapabilities();
        await this.detectPerformanceProfile();
        
        this.startMonitoring();
        this.notifyListeners('initialized', this.hardwareInfo);
        
        return this.hardwareInfo;
    }

    /**
     * Detect basic hardware information
     */
    async detectBasicInfo() {
        // Screen information
        this.hardwareInfo.screen = {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            devicePixelRatio: window.devicePixelRatio || 1,
            orientation: screen.orientation ? screen.orientation.type : 'unknown'
        };

        // Viewport information
        this.hardwareInfo.viewport = {
            width: window.innerWidth,
            height: window.innerHeight,
            aspectRatio: (window.innerWidth / window.innerHeight).toFixed(2)
        };

        // Browser and platform
        this.hardwareInfo.platform = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine
        };

        // Memory information (if available)
        if ('memory' in performance) {
            this.hardwareInfo.memory = {
                usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }

        // Hardware concurrency (CPU cores)
        this.hardwareInfo.cpu = {
            logicalProcessors: navigator.hardwareConcurrency || 'unknown'
        };

        // Network information (if available)
        if ('connection' in navigator) {
            this.hardwareInfo.network = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }
    }

    /**
     * Detect input and display capabilities
     */
    async detectCapabilities() {
        this.capabilities = {
            touch: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
            mouse: matchMedia('(pointer: fine)').matches,
            keyboard: true, // Assume keyboard available
            webgl: this.detectWebGL(),
            canvas: !!document.createElement('canvas').getContext,
            localStorage: this.testLocalStorage(),
            sessionStorage: this.testSessionStorage(),
            webSocket: 'WebSocket' in window,
            eventSource: 'EventSource' in window,
            geolocation: 'geolocation' in navigator,
            deviceMotion: 'DeviceMotionEvent' in window,
            fullscreen: !!(document.fullscreenEnabled || document.webkitFullscreenEnabled),
            vibration: 'vibrate' in navigator
        };

        // Test graphics performance
        this.capabilities.graphics = await this.testGraphicsPerformance();
        
        this.hardwareInfo.capabilities = this.capabilities;
    }

    /**
     * Detect performance profile for optimization
     */
    async detectPerformanceProfile() {
        const profile = {
            category: 'unknown',
            optimizations: [],
            limitations: []
        };

        // Categorize based on screen resolution and capabilities
        const totalPixels = this.hardwareInfo.screen.width * this.hardwareInfo.screen.height;
        const hasWebGL = this.capabilities.webgl;
        const cpuCores = this.hardwareInfo.cpu.logicalProcessors;

        if (totalPixels >= 3840 * 2160) { // 4K+
            profile.category = 'high-end';
            profile.optimizations = ['high-quality-graphics', 'smooth-animations', 'detailed-logging'];
        } else if (totalPixels >= 1920 * 1080) { // Full HD
            profile.category = 'standard';
            profile.optimizations = ['standard-graphics', 'normal-animations'];
        } else if (totalPixels >= 1366 * 768) { // HD
            profile.category = 'basic';
            profile.optimizations = ['reduced-graphics', 'minimal-animations'];
            profile.limitations = ['limited-concurrent-tests'];
        } else { // Below HD
            profile.category = 'low-spec';
            profile.optimizations = ['minimal-graphics', 'no-animations', 'reduced-logging'];
            profile.limitations = ['sequential-tests-only', 'basic-ui-only'];
        }

        // Add memory-based optimizations
        if (this.hardwareInfo.memory && this.hardwareInfo.memory.jsHeapSizeLimit < 512) {
            profile.limitations.push('memory-constrained');
            profile.optimizations.push('aggressive-cleanup');
        }

        // Graphics optimizations
        if (!hasWebGL) {
            profile.limitations.push('no-webgl');
            profile.optimizations.push('canvas-fallback');
        }

        // CPU optimizations
        if (cpuCores && cpuCores < 4) {
            profile.limitations.push('limited-cpu');
            profile.optimizations.push('reduced-concurrency');
        }

        this.hardwareInfo.performanceProfile = profile;
    }

    /**
     * Test WebGL availability
     */
    detectWebGL() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return !!gl;
        } catch (e) {
            return false;
        }
    }

    /**
     * Test graphics performance
     */
    async testGraphicsPerformance() {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            canvas.width = 100;
            canvas.height = 100;
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
                resolve({ score: 0, category: 'no-canvas' });
                return;
            }

            const startTime = performance.now();
            
            // Simple graphics performance test
            for (let i = 0; i < 1000; i++) {
                ctx.fillStyle = `hsl(${i % 360}, 50%, 50%)`;
                ctx.fillRect(Math.random() * 100, Math.random() * 100, 10, 10);
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            
            let category = 'low';
            if (duration < 50) category = 'high';
            else if (duration < 100) category = 'medium';
            
            resolve({
                score: Math.round(1000 / duration),
                duration: Math.round(duration),
                category
            });
        });
    }

    /**
     * Test localStorage availability
     */
    testLocalStorage() {
        try {
            const test = '__test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * Test sessionStorage availability
     */
    testSessionStorage() {
        try {
            const test = '__test__';
            sessionStorage.setItem(test, test);
            sessionStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * Start monitoring for changes
     */
    startMonitoring() {
        // Monitor viewport changes
        window.addEventListener('resize', () => {
            this.hardwareInfo.viewport = {
                width: window.innerWidth,
                height: window.innerHeight,
                aspectRatio: (window.innerWidth / window.innerHeight).toFixed(2)
            };
            this.notifyListeners('viewport-changed', this.hardwareInfo.viewport);
        });

        // Monitor orientation changes
        if (screen.orientation) {
            screen.orientation.addEventListener('change', () => {
                this.hardwareInfo.screen.orientation = screen.orientation.type;
                this.notifyListeners('orientation-changed', this.hardwareInfo.screen);
            });
        }

        // Monitor network changes
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                this.hardwareInfo.network = {
                    effectiveType: navigator.connection.effectiveType,
                    downlink: navigator.connection.downlink,
                    rtt: navigator.connection.rtt
                };
                this.notifyListeners('network-changed', this.hardwareInfo.network);
            });
        }

        // Monitor online/offline status
        window.addEventListener('online', () => {
            this.hardwareInfo.platform.onLine = true;
            this.notifyListeners('online-status-changed', true);
        });

        window.addEventListener('offline', () => {
            this.hardwareInfo.platform.onLine = false;
            this.notifyListeners('online-status-changed', false);
        });

        // Periodic memory monitoring (if available)
        if ('memory' in performance) {
            this.updateInterval = setInterval(() => {
                this.hardwareInfo.memory = {
                    usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                    totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                    jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
                };
                this.notifyListeners('memory-updated', this.hardwareInfo.memory);
            }, 5000); // Update every 5 seconds
        }
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    /**
     * Add event listener
     */
    on(event, callback) {
        this.listeners.push({ event, callback });
    }

    /**
     * Remove event listener
     */
    off(event, callback) {
        this.listeners = this.listeners.filter(
            listener => !(listener.event === event && listener.callback === callback)
        );
    }

    /**
     * Notify listeners
     */
    notifyListeners(event, data) {
        this.listeners
            .filter(listener => listener.event === event)
            .forEach(listener => {
                try {
                    listener.callback(data);
                } catch (error) {
                    console.error(`Error in hardware detector listener for ${event}:`, error);
                }
            });
    }

    /**
     * Get hardware compatibility recommendations
     */
    getCompatibilityRecommendations() {
        const recommendations = [];
        const profile = this.hardwareInfo.performanceProfile;

        if (!profile) return recommendations;

        // Resolution recommendations
        if (profile.category === 'low-spec') {
            recommendations.push({
                type: 'warning',
                message: 'Low resolution detected. Some UI elements may be cramped.',
                action: 'Consider using simplified test interface'
            });
        }

        // Memory recommendations
        if (profile.limitations.includes('memory-constrained')) {
            recommendations.push({
                type: 'warning',
                message: 'Limited memory available. Run tests sequentially.',
                action: 'Avoid running multiple visual tests simultaneously'
            });
        }

        // Graphics recommendations
        if (!this.capabilities.webgl) {
            recommendations.push({
                type: 'info',
                message: 'WebGL not available. Using canvas fallback.',
                action: 'Visual tests will use basic graphics'
            });
        }

        // Input recommendations
        if (!this.capabilities.touch && this.capabilities.mouse) {
            recommendations.push({
                type: 'info',
                message: 'Touch input not detected. Using mouse/keyboard interface.',
                action: 'Touch screen test will be skipped'
            });
        }

        return recommendations;
    }

    /**
     * Get current hardware summary
     */
    getSummary() {
        return {
            resolution: `${this.hardwareInfo.screen.width}x${this.hardwareInfo.screen.height}`,
            category: this.hardwareInfo.performanceProfile?.category || 'unknown',
            capabilities: Object.entries(this.capabilities)
                .filter(([key, value]) => value)
                .map(([key]) => key),
            recommendations: this.getCompatibilityRecommendations()
        };
    }
}

// Global instance
window.CrucibleHardwareDetector = new HardwareDetector();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.CrucibleHardwareDetector.initialize();
    });
} else {
    window.CrucibleHardwareDetector.initialize();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = HardwareDetector;
}
