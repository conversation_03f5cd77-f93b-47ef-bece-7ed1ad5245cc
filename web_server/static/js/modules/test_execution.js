// test_execution.js
import { launchVisualTest, runVisualTestsFromProfile } from './visual_tests.js';
// This module handles running test profiles, polling for logs, and managing the UI state during tests.

let logPollInterval = null; // Legacy fallback polling (not used with SSE)
let logEventSource = null;   // Active EventSource instance
let lastLogIndex = -1;       // Used only for fallback polling
let currentPollingAssetNumber = null;

function stopLogStreaming() {
    if (logEventSource) {
        logEventSource.close();
        logEventSource = null;
        console.log("Closed log EventSource stream.");
    }
    if (logPollInterval) {
        clearInterval(logPollInterval);
        logPollInterval = null;
        console.log("Stopped legacy log polling.");
    }
}

async function pollLogs() {
    if (!currentPollingAssetNumber) {
        stopLogStreaming();
        return;
    }
    const testStatusLog = document.getElementById('test-status-log');
    if (!testStatusLog) {
        stopLogStreaming();
        return;
    }

    try {
        const response = await fetch(`/api/test_log/${currentPollingAssetNumber}?since_index=${lastLogIndex}`);
        if (!response.ok) {
            const errorData = await response.json().catch(() => null);
            const errorMsg = errorData ? (errorData.error || `HTTP error ${response.status}`) : `HTTP error ${response.status}`;
            console.error("Error polling logs:", errorMsg);
            testStatusLog.innerHTML += `<p style="color: orange;">Could not fetch further logs: ${errorMsg}. Polling stopped.</p>`;
            stopLogStreaming();
            checkRunTestButtonState();
            return;
        }

        const data = await response.json();
        if (data.logs && data.logs.length > 0) {
            data.logs.forEach(log => {
                const p = document.createElement('p');
                p.textContent = log;
                if (log.startsWith('[ERROR]')) p.style.color = 'red';
                if (log.startsWith('[WARNING]')) p.style.color = 'orange';
                if (log.startsWith('[SUCCESS]')) p.style.color = 'green';
                testStatusLog.appendChild(p);
            });
            lastLogIndex = data.last_index;
            testStatusLog.scrollTop = testStatusLog.scrollHeight;
        }

        if (data.logs && data.logs.some(log => log.includes("All tests completed"))) {
            testStatusLog.innerHTML += `<p style="color: green;">Test sequence finished. Polling stopped.</p>`;
            stopLogStreaming();
            checkRunTestButtonState();
        }

    } catch (error) {
        console.error("Error during log polling:", error);
        testStatusLog.innerHTML += `<p style="color: red;">Error polling logs: ${error.message}. Polling stopped.</p>`;
        stopLogStreaming();
        checkRunTestButtonState();
    }
}

export function checkRunTestButtonState() {
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');

    const currentOperatorId = document.getElementById('operator-id').value;
    const currentAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;

    const shouldDisable = (logEventSource || logPollInterval) || !currentOperatorId || !currentAssetNumber || !currentProfileName;

    if (runTestsBtn) runTestsBtn.disabled = shouldDisable;
    if (runTestsBtnQuick) runTestsBtnQuick.disabled = shouldDisable;
}

async function runTests() {
    console.log('=== runTests() called ===');
    const testStatusLog = document.getElementById('test-status-log');
    const testStatusSection = document.getElementById('test-status-section');

    if (!testStatusLog) return;

    const currentOperatorId = document.getElementById('operator-id').value;
    currentPollingAssetNumber = document.getElementById('asset-number').value;
    const currentProfileName = document.getElementById('profile-select-quick')?.value ||
                              document.getElementById('profile-select')?.value;
    
    // Trigger enhanced test monitor if available
    if (window.enhancedTestMonitor) {
        const event = new CustomEvent('testExecutionStarted', {
            detail: {
                assetNumber: currentPollingAssetNumber,
                operatorId: currentOperatorId,
                profileName: currentProfileName
            }
        });
        document.dispatchEvent(event);
        console.log('Enhanced Test Monitor triggered');
    }

    console.log(`runTests: operator=${currentOperatorId}, asset=${currentPollingAssetNumber}, profile=${currentProfileName}`);

    if (!currentOperatorId || !currentPollingAssetNumber || !currentProfileName) {
        alert('Please ensure Operator ID, Asset Number are filled and a Profile is selected.');
        currentPollingAssetNumber = null;
        return;
    }

    if (testStatusSection) {
        testStatusSection.style.display = 'block';
        testStatusSection.scrollIntoView({ behavior: 'smooth' });
    }

    testStatusLog.innerHTML = `<p>Starting tests for profile "${currentProfileName}" on asset "${currentPollingAssetNumber}" by operator "${currentOperatorId}"...</p>`;
    checkRunTestButtonState();
    stopLogStreaming();
    lastLogIndex = -1;

    const includeVisual = document.getElementById('toggle-visual-tests')?.checked ?? true;

    const payload = {
        asset_number: currentPollingAssetNumber,
        operator_id: currentOperatorId,
        profile_name: currentProfileName,
        include_visual: includeVisual
    };

    try {
        const response = await fetch('/api/run_tests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        const result = await response.json();

        if (response.ok) {
            const p = document.createElement('p');
            p.textContent = `Test execution request successful: ${result.message}`;
            testStatusLog.appendChild(p);
            testStatusLog.innerHTML += `<p>Streaming logs...</p>`;

            // Prefer Server-Sent Events for real-time stream
            if (!!window.EventSource) {
                logEventSource = new EventSource(`/api/test_log/stream/${currentPollingAssetNumber}`);
                logEventSource.onmessage = (e) => {
                    const payload = JSON.parse(e.data);
                    if (payload.type === 'log') {
                        const { log } = payload;
                        const p = document.createElement('p');
                        p.textContent = log;
                        if (log.startsWith('[ERROR]')) p.style.color = 'red';
                        if (log.startsWith('[WARNING]')) p.style.color = 'orange';
                        if (log.startsWith('[SUCCESS]')) p.style.color = 'green';
                        testStatusLog.appendChild(p);
                        testStatusLog.scrollTop = testStatusLog.scrollHeight;
                        if (log.includes('All tests completed')) {
                            testStatusLog.innerHTML += `<p style="color: green;">Test sequence finished. Stream closed.</p>`;
                            stopLogStreaming();
                            checkRunTestButtonState();
                        }
                    } else if (payload.type === 'visual_progress') {
                        // For now just log to console; could update a small HUD later
                        console.debug('Visual progress', payload);
                    }
                };
                logEventSource.onerror = () => {
                    console.warn('Log EventSource error; falling back to polling.');
                    stopLogStreaming();
                    // Fallback to polling every 2s
                    if (!logPollInterval) {
                        logPollInterval = setInterval(pollLogs, 2000);
                    }
                };
            } else {
                // Browser does not support EventSource; fallback to polling
                if (!logPollInterval) {
                    logPollInterval = setInterval(pollLogs, 2000);
                }
            }
            // After orchestrator started, optionally launch visual tests selected in profile
            if (includeVisual) {
                console.log('=== Calling runVisualTestsFromProfile ===');
                runVisualTestsFromProfile(currentProfileName, currentPollingAssetNumber);
            }
        } else {
            testStatusLog.innerHTML += `<p style="color: red;">Error starting tests: ${result.error} ${result.details || ''}</p>`;
            currentPollingAssetNumber = null;
            checkRunTestButtonState();
        }
    } catch (error) {
        console.error('Failed to run tests:', error);
        testStatusLog.innerHTML += `<p style="color: red;">Failed to send test execution request: ${error.message}</p>`;
        currentPollingAssetNumber = null;
        checkRunTestButtonState();
    }
}

function clearTestLog() {
    stopLogStreaming();
    const testStatusLog = document.getElementById('test-status-log');
    const testStatusSection = document.getElementById('test-status-section');

    if (testStatusLog) {
        testStatusLog.innerHTML = '';
    }
    if (testStatusSection) {
        testStatusSection.style.display = 'none';
    }
}

function updateAssetStatus() {
    const statusElement = document.getElementById('asset-status');
    const statusText = document.getElementById('asset-status-text');
    const operatorId = document.getElementById('operator-id').value;
    const assetNumber = document.getElementById('asset-number').value;

    if (statusElement && statusText) {
        if (operatorId && assetNumber) {
            statusElement.className = 'asset-status ready';
            statusText.textContent = `Ready: ${operatorId} / ${assetNumber}`;
        } else {
            statusElement.className = 'asset-status incomplete';
            statusText.textContent = 'Enter asset information to begin';
        }
    }
}

function handleAssetInfoChange() {
    updateAssetStatus();
    checkRunTestButtonState();
}

function handleEnterKey(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        const operatorId = document.getElementById('operator-id').value;
        const assetNumber = document.getElementById('asset-number').value;

        if (event.target.id === 'operator-id' && operatorId) {
            document.getElementById('asset-number').focus();
        } else if (event.target.id === 'asset-number' && assetNumber && operatorId) {
            const profileSelect = document.getElementById('profile-select-quick');
            if (profileSelect) profileSelect.focus();
        }
    }
}

export function setupTestExecution() {
    const operatorIdInput = document.getElementById('operator-id');
    const assetNumberInput = document.getElementById('asset-number');
    const runTestsBtn = document.getElementById('run-tests-btn');
    const runTestsBtnQuick = document.getElementById('run-tests-btn-quick');
    const clearLogBtn = document.getElementById('clear-log-btn');

    if (operatorIdInput) {
        operatorIdInput.addEventListener('input', handleAssetInfoChange);
        operatorIdInput.addEventListener('keypress', handleEnterKey);
    }
    if (assetNumberInput) {
        assetNumberInput.addEventListener('input', handleAssetInfoChange);
        assetNumberInput.addEventListener('keypress', handleEnterKey);
    }

    if (runTestsBtn) runTestsBtn.addEventListener('click', runTests);
    if (runTestsBtnQuick) runTestsBtnQuick.addEventListener('click', runTests);
    if (clearLogBtn) clearLogBtn.addEventListener('click', clearTestLog);

    document.getElementById('profile-select-quick')?.addEventListener('change', checkRunTestButtonState);
    document.getElementById('profile-select')?.addEventListener('change', checkRunTestButtonState);

    updateAssetStatus();
    checkRunTestButtonState();
}
