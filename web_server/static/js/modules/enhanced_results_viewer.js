import { fetchData } from './utils.js';

// Enhanced Results Viewer with advanced visualization and analysis
class EnhancedResultsViewer {
    constructor() {
        this.currentAsset = null;
        this.allResults = [];
        this.filteredResults = [];
        this.comparisonMode = false;
        this.selectedResults = new Set();
        this.chartInstances = {};
        this.testMetrics = {};
    }

    async initialize() {
        this.setupEventListeners();
        this.initializeCharts();
    }

    setupEventListeners() {
        // Main results viewer button
        const listResultsBtn = document.getElementById('list-results-btn');
        if (listResultsBtn) {
            listResultsBtn.addEventListener('click', () => this.loadResults());
        }

        // Quick action button
        const listResultsBtnQuick = document.getElementById('list-results-btn-quick');
        if (listResultsBtnQuick) {
            listResultsBtnQuick.addEventListener('click', () => this.loadResultsForCurrentAsset());
        }

        // Asset number input (Enter key)
        const resultsAssetNumberInput = document.getElementById('results-asset-number');
        if (resultsAssetNumberInput) {
            resultsAssetNumberInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    event.preventDefault();
                    this.loadResults();
                }
            });
        }

        // Search and filter
        const searchInput = document.getElementById('results-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterResults(e.target.value));
        }

        // View mode toggles
        const viewModeButtons = document.querySelectorAll('.view-mode-btn');
        viewModeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => this.switchViewMode(e.target.dataset.mode));
        });

        // Comparison mode toggle
        const comparisonToggle = document.getElementById('comparison-mode-toggle');
        if (comparisonToggle) {
            comparisonToggle.addEventListener('change', (e) => this.toggleComparisonMode(e.target.checked));
        }

        // Export button
        const exportBtn = document.getElementById('export-results-btn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportResults());
        }

        // Modal close handlers
        this.setupModalHandlers();
    }

    setupModalHandlers() {
        const modal = document.getElementById('enhanced-result-details-modal');
        if (modal) {
            const closeButton = modal.querySelector('.close-button');
            if (closeButton) {
                closeButton.addEventListener('click', () => this.closeResultDetailsModal());
            }
            window.addEventListener('click', (event) => {
                if (event.target === modal) {
                    this.closeResultDetailsModal();
                }
            });
        }
    }

    async loadResults() {
        const assetNumInput = document.getElementById('results-asset-number');
        const assetNum = assetNumInput?.value.trim();
        
        if (!assetNum) {
            this.showNotification('Please enter an Asset Number', 'warning');
            return;
        }

        await this.fetchAndDisplayResults(assetNum);
    }

    async loadResultsForCurrentAsset() {
        const assetNumber = document.getElementById('asset-number')?.value;
        if (!assetNumber) {
            this.showNotification('Please enter an asset number first', 'warning');
            return;
        }

        // Pre-fill the asset number
        const resultsAssetNumberInput = document.getElementById('results-asset-number');
        if (resultsAssetNumberInput) {
            resultsAssetNumberInput.value = assetNumber;
        }

        // Expand and scroll to results section
        const resultsSection = document.getElementById('enhanced-results-viewer');
        if (resultsSection) {
            resultsSection.classList.add('expanded');
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }

        await this.fetchAndDisplayResults(assetNumber);
    }

    async fetchAndDisplayResults(assetNum) {
        this.currentAsset = assetNum;
        this.showLoading(true);

        try {
            const data = await fetchData(`/api/results/${assetNum}`);
            
            if (!data) {
                throw new Error('Could not fetch results');
            }

            if (data.error) {
                throw new Error(data.error);
            }

            // Combine and process results
            const consolidated = data.consolidated_results || [];
            const individual = data.individual_results || [];
            this.allResults = await this.processResults([...consolidated, ...individual], assetNum);
            this.filteredResults = [...this.allResults];

            // Display results
            this.displayResultsList();
            this.updateStatistics();
            this.updateCharts();
            
            this.showLoading(false);
            this.showNotification(`Loaded ${this.allResults.length} results for asset ${assetNum}`, 'success');

        } catch (error) {
            console.error('Error loading results:', error);
            this.showLoading(false);
            this.showNotification(`Error: ${error.message}`, 'error');
            this.displayEmptyState();
        }
    }

    async processResults(fileNames, assetNum) {
        const processedResults = [];
        
        for (const fileName of fileNames) {
            try {
                const response = await fetch(`/api/results/${assetNum}/${fileName}`);
                if (!response.ok) continue;
                
                const content = await response.text();
                const result = this.parseResultContent(content, fileName);
                processedResults.push(result);
            } catch (error) {
                console.error(`Error processing ${fileName}:`, error);
            }
        }

        // Sort by timestamp (newest first)
        processedResults.sort((a, b) => b.timestamp - a.timestamp);
        return processedResults;
    }

    parseResultContent(content, fileName) {
        const result = {
            fileName,
            timestamp: this.extractTimestamp(fileName),
            raw: content,
            tests: [],
            summary: {
                total: 0,
                passed: 0,
                failed: 0,
                skipped: 0,
                duration: 0
            }
        };

        try {
            // Try to parse as JSON first
            const jsonData = JSON.parse(content);
            result.data = jsonData;
            result.tests = this.extractTestsFromJson(jsonData);
        } catch {
            // Parse as text format
            result.tests = this.extractTestsFromText(content);
        }

        // Calculate summary
        result.summary.total = result.tests.length;
        result.summary.passed = result.tests.filter(t => t.status === 'passed').length;
        result.summary.failed = result.tests.filter(t => t.status === 'failed').length;
        result.summary.skipped = result.tests.filter(t => t.status === 'skipped').length;
        result.summary.duration = result.tests.reduce((sum, t) => sum + (t.duration || 0), 0);

        return result;
    }

    extractTimestamp(fileName) {
        // Extract timestamp from filename patterns like "results_20250112_143022.json"
        const match = fileName.match(/(\d{8}_\d{6})/);
        if (match) {
            const [date, time] = match[1].split('_');
            const year = date.substr(0, 4);
            const month = date.substr(4, 2);
            const day = date.substr(6, 2);
            const hour = time.substr(0, 2);
            const minute = time.substr(2, 2);
            const second = time.substr(4, 2);
            return new Date(`${year}-${month}-${day}T${hour}:${minute}:${second}`).getTime();
        }
        return Date.now();
    }

    extractTestsFromJson(data) {
        const tests = [];
        
        // Handle different JSON structures
        if (Array.isArray(data)) {
            data.forEach(item => {
                tests.push(this.normalizeTest(item));
            });
        } else if (data.tests) {
            data.tests.forEach(test => {
                tests.push(this.normalizeTest(test));
            });
        } else if (data.results) {
            Object.entries(data.results).forEach(([name, result]) => {
                tests.push(this.normalizeTest({ name, ...result }));
            });
        }

        return tests;
    }

    extractTestsFromText(content) {
        const tests = [];
        const lines = content.split('\n');
        
        lines.forEach(line => {
            // Look for test result patterns
            if (line.includes('PASS') || line.includes('FAIL') || line.includes('SKIP')) {
                const test = this.parseTestLine(line);
                if (test) tests.push(test);
            }
        });

        return tests;
    }

    normalizeTest(test) {
        return {
            name: test.name || test.test_name || 'Unknown Test',
            status: this.normalizeStatus(test.status || test.result || test.outcome),
            duration: test.duration || test.time || 0,
            message: test.message || test.error || test.details || '',
            metrics: test.metrics || {}
        };
    }

    normalizeStatus(status) {
        const statusStr = String(status).toLowerCase();
        if (statusStr.includes('pass') || statusStr === 'ok' || statusStr === 'success') return 'passed';
        if (statusStr.includes('fail') || statusStr === 'error') return 'failed';
        if (statusStr.includes('skip') || statusStr === 'pending') return 'skipped';
        return 'unknown';
    }

    parseTestLine(line) {
        // Parse common test output formats
        const patterns = [
            /\[(\w+)\]\s+(.+?):\s+(.+)/,  // [PASS] TestName: details
            /(.+?):\s+(PASS|FAIL|SKIP)/,   // TestName: PASS
            /✓\s+(.+)/,                     // ✓ TestName (passed)
            /✗\s+(.+)/                      // ✗ TestName (failed)
        ];

        for (const pattern of patterns) {
            const match = line.match(pattern);
            if (match) {
                return {
                    name: match[1] || match[2],
                    status: this.normalizeStatus(match[2] || (line.includes('✓') ? 'pass' : 'fail')),
                    message: match[3] || '',
                    duration: 0
                };
            }
        }
        return null;
    }

    displayResultsList() {
        const container = document.getElementById('enhanced-results-list');
        if (!container) return;

        if (this.filteredResults.length === 0) {
            this.displayEmptyState();
            return;
        }

        const viewMode = this.getCurrentViewMode();
        
        if (viewMode === 'grid') {
            this.displayGridView(container);
        } else if (viewMode === 'timeline') {
            this.displayTimelineView(container);
        } else {
            this.displayListView(container);
        }
    }

    displayListView(container) {
        let html = '<div class="results-list-view">';
        
        this.filteredResults.forEach(result => {
            const statusClass = this.getStatusClass(result.summary);
            const isSelected = this.selectedResults.has(result.fileName);
            
            html += `
                <div class="result-item ${statusClass} ${isSelected ? 'selected' : ''}" data-filename="${result.fileName}">
                    ${this.comparisonMode ? `
                        <input type="checkbox" class="result-checkbox" 
                               ${isSelected ? 'checked' : ''} 
                               data-filename="${result.fileName}">
                    ` : ''}
                    <div class="result-header">
                        <h4>${result.fileName}</h4>
                        <span class="result-timestamp">${this.formatTimestamp(result.timestamp)}</span>
                    </div>
                    <div class="result-summary">
                        <span class="badge badge-success">✓ ${result.summary.passed}</span>
                        <span class="badge badge-danger">✗ ${result.summary.failed}</span>
                        <span class="badge badge-warning">⊘ ${result.summary.skipped}</span>
                        <span class="badge badge-info">⏱ ${this.formatDuration(result.summary.duration)}</span>
                    </div>
                    <div class="result-actions">
                        <button class="btn-compact view-details-btn" data-filename="${result.fileName}">
                            View Details
                        </button>
                        ${this.comparisonMode ? '' : `
                            <button class="btn-compact export-single-btn" data-filename="${result.fileName}">
                                Export
                            </button>
                        `}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;
        this.attachResultItemListeners();
    }

    displayGridView(container) {
        let html = '<div class="results-grid-view">';
        
        this.filteredResults.forEach(result => {
            const statusClass = this.getStatusClass(result.summary);
            const passRate = result.summary.total > 0 
                ? Math.round((result.summary.passed / result.summary.total) * 100) 
                : 0;
            
            html += `
                <div class="result-card ${statusClass}" data-filename="${result.fileName}">
                    <div class="result-card-header">
                        <h4>${result.fileName}</h4>
                    </div>
                    <div class="result-card-body">
                        <div class="pass-rate-circle">
                            <svg viewBox="0 0 36 36" class="circular-chart">
                                <path class="circle-bg" d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831" />
                                <path class="circle" stroke-dasharray="${passRate}, 100" d="M18 2.0845
                                    a 15.9155 15.9155 0 0 1 0 31.831
                                    a 15.9155 15.9155 0 0 1 0 -31.831" />
                                <text x="18" y="20.35" class="percentage">${passRate}%</text>
                            </svg>
                        </div>
                        <div class="result-stats">
                            <div class="stat">
                                <span class="stat-label">Total</span>
                                <span class="stat-value">${result.summary.total}</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">Passed</span>
                                <span class="stat-value text-success">${result.summary.passed}</span>
                            </div>
                            <div class="stat">
                                <span class="stat-label">Failed</span>
                                <span class="stat-value text-danger">${result.summary.failed}</span>
                            </div>
                        </div>
                    </div>
                    <div class="result-card-footer">
                        <span class="timestamp">${this.formatTimestamp(result.timestamp)}</span>
                        <button class="btn-compact view-details-btn" data-filename="${result.fileName}">
                            Details
                        </button>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;
        this.attachResultItemListeners();
    }

    displayTimelineView(container) {
        let html = '<div class="results-timeline-view">';
        
        // Group results by date
        const groupedByDate = {};
        this.filteredResults.forEach(result => {
            const date = new Date(result.timestamp).toLocaleDateString();
            if (!groupedByDate[date]) {
                groupedByDate[date] = [];
            }
            groupedByDate[date].push(result);
        });

        Object.entries(groupedByDate).forEach(([date, results]) => {
            html += `
                <div class="timeline-date">
                    <h3>${date}</h3>
                    <div class="timeline-items">
            `;

            results.forEach(result => {
                const statusClass = this.getStatusClass(result.summary);
                const time = new Date(result.timestamp).toLocaleTimeString();
                
                html += `
                    <div class="timeline-item ${statusClass}" data-filename="${result.fileName}">
                        <div class="timeline-marker"></div>
                        <div class="timeline-content">
                            <div class="timeline-time">${time}</div>
                            <div class="timeline-title">${result.fileName}</div>
                            <div class="timeline-summary">
                                <span class="badge badge-success">✓ ${result.summary.passed}</span>
                                <span class="badge badge-danger">✗ ${result.summary.failed}</span>
                                <button class="btn-compact view-details-btn" data-filename="${result.fileName}">
                                    View
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        });

        html += '</div>';
        container.innerHTML = html;
        this.attachResultItemListeners();
    }

    attachResultItemListeners() {
        // View details buttons
        document.querySelectorAll('.view-details-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.displayResultDetails(e.target.dataset.filename);
            });
        });

        // Export single buttons
        document.querySelectorAll('.export-single-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.exportSingleResult(e.target.dataset.filename);
            });
        });

        // Comparison checkboxes
        document.querySelectorAll('.result-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.selectedResults.add(e.target.dataset.filename);
                } else {
                    this.selectedResults.delete(e.target.dataset.filename);
                }
                this.updateComparisonButton();
            });
        });

        // Clickable result items (in grid/timeline view)
        document.querySelectorAll('.result-card, .timeline-item').forEach(item => {
            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('btn-compact')) {
                    this.displayResultDetails(item.dataset.filename);
                }
            });
        });
    }

    async displayResultDetails(fileName) {
        const result = this.allResults.find(r => r.fileName === fileName);
        if (!result) return;

        const modal = document.getElementById('enhanced-result-details-modal');
        const titleElement = document.getElementById('enhanced-result-details-title');
        const contentElement = document.getElementById('enhanced-result-details-content');

        if (!modal || !contentElement) return;

        // Update modal title
        if (titleElement) {
            titleElement.textContent = `Result: ${fileName}`;
        }

        // Generate detailed view
        let html = `
            <div class="result-details-container">
                <div class="details-header">
                    <div class="details-summary">
                        <h4>Test Summary</h4>
                        <div class="summary-stats">
                            <div class="stat-card">
                                <span class="stat-label">Total Tests</span>
                                <span class="stat-value">${result.summary.total}</span>
                            </div>
                            <div class="stat-card success">
                                <span class="stat-label">Passed</span>
                                <span class="stat-value">${result.summary.passed}</span>
                            </div>
                            <div class="stat-card danger">
                                <span class="stat-label">Failed</span>
                                <span class="stat-value">${result.summary.failed}</span>
                            </div>
                            <div class="stat-card warning">
                                <span class="stat-label">Skipped</span>
                                <span class="stat-value">${result.summary.skipped}</span>
                            </div>
                            <div class="stat-card info">
                                <span class="stat-label">Duration</span>
                                <span class="stat-value">${this.formatDuration(result.summary.duration)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="details-tabs">
                    <button class="tab-btn active" data-tab="tests">Individual Tests</button>
                    <button class="tab-btn" data-tab="raw">Raw Data</button>
                    <button class="tab-btn" data-tab="metrics">Metrics</button>
                </div>

                <div class="tab-content">
                    <div id="tests-tab" class="tab-pane active">
                        ${this.generateTestsTable(result.tests)}
                    </div>
                    <div id="raw-tab" class="tab-pane">
                        <pre class="raw-content">${this.escapeHtml(result.raw)}</pre>
                    </div>
                    <div id="metrics-tab" class="tab-pane">
                        ${this.generateMetricsView(result)}
                    </div>
                </div>
            </div>
        `;

        contentElement.innerHTML = html;

        // Setup tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.tab-btn').forEach(b => b.classList.remove('active'));
                document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
                
                e.target.classList.add('active');
                document.getElementById(`${e.target.dataset.tab}-tab`).classList.add('active');
            });
        });

        // Show modal
        modal.style.display = 'block';
    }

    generateTestsTable(tests) {
        if (tests.length === 0) {
            return '<p class="no-data">No individual test data available</p>';
        }

        let html = `
            <table class="tests-table">
                <thead>
                    <tr>
                        <th>Test Name</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
        `;

        tests.forEach(test => {
            const statusClass = `status-${test.status}`;
            const statusIcon = this.getStatusIcon(test.status);
            
            html += `
                <tr class="${statusClass}">
                    <td class="test-name">${test.name}</td>
                    <td class="test-status">
                        <span class="status-badge ${statusClass}">
                            ${statusIcon} ${test.status}
                        </span>
                    </td>
                    <td class="test-duration">${this.formatDuration(test.duration)}</td>
                    <td class="test-details">
                        ${test.message ? `<span class="test-message">${this.escapeHtml(test.message)}</span>` : '-'}
                    </td>
                </tr>
            `;
        });

        html += `
                </tbody>
            </table>
        `;

        return html;
    }

    generateMetricsView(result) {
        const passRate = result.summary.total > 0 
            ? ((result.summary.passed / result.summary.total) * 100).toFixed(1)
            : 0;

        let html = `
            <div class="metrics-container">
                <div class="metric-card">
                    <h4>Pass Rate</h4>
                    <div class="metric-value">${passRate}%</div>
                    <div class="metric-bar">
                        <div class="metric-bar-fill" style="width: ${passRate}%"></div>
                    </div>
                </div>
                <div class="metric-card">
                    <h4>Test Distribution</h4>
                    <div class="metric-chart">
                        <canvas id="distribution-mini-chart" width="200" height="100"></canvas>
                    </div>
                </div>
            </div>
        `;

        // Draw mini chart after DOM update
        setTimeout(() => this.drawMiniDistributionChart(result), 100);

        return html;
    }

    drawMiniDistributionChart(result) {
        const canvas = document.getElementById('distribution-mini-chart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Simple bar chart
        const data = [
            { label: 'Pass', value: result.summary.passed, color: '#4ade80' },
            { label: 'Fail', value: result.summary.failed, color: '#f87171' },
            { label: 'Skip', value: result.summary.skipped, color: '#fbbf24' }
        ];

        const barWidth = width / 4;
        const maxValue = Math.max(...data.map(d => d.value), 1);

        data.forEach((item, index) => {
            const barHeight = (item.value / maxValue) * (height - 20);
            const x = (index + 0.5) * barWidth + 20;
            const y = height - barHeight - 10;

            ctx.fillStyle = item.color;
            ctx.fillRect(x, y, barWidth * 0.8, barHeight);

            ctx.fillStyle = '#9ca3af';
            ctx.font = '10px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(item.value.toString(), x + barWidth * 0.4, y - 5);
        });
    }

    updateStatistics() {
        const statsContainer = document.getElementById('results-statistics');
        if (!statsContainer) return;

        const totalTests = this.allResults.reduce((sum, r) => sum + r.summary.total, 0);
        const totalPassed = this.allResults.reduce((sum, r) => sum + r.summary.passed, 0);
        const totalFailed = this.allResults.reduce((sum, r) => sum + r.summary.failed, 0);
        const avgPassRate = totalTests > 0 ? ((totalPassed / totalTests) * 100).toFixed(1) : 0;

        statsContainer.innerHTML = `
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-label">Total Runs</span>
                    <span class="stat-value">${this.allResults.length}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Total Tests</span>
                    <span class="stat-value">${totalTests}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Avg Pass Rate</span>
                    <span class="stat-value">${avgPassRate}%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Failed Tests</span>
                    <span class="stat-value text-danger">${totalFailed}</span>
                </div>
            </div>
        `;
    }

    updateCharts() {
        // Placeholder for chart updates
        // Can be extended with Chart.js or other charting libraries
    }

    initializeCharts() {
        // Initialize any persistent charts
    }

    filterResults(searchTerm) {
        if (!searchTerm) {
            this.filteredResults = [...this.allResults];
        } else {
            const term = searchTerm.toLowerCase();
            this.filteredResults = this.allResults.filter(result => {
                return result.fileName.toLowerCase().includes(term) ||
                       result.tests.some(test => test.name.toLowerCase().includes(term));
            });
        }
        this.displayResultsList();
    }

    switchViewMode(mode) {
        // Update active button
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.mode === mode);
        });

        // Store preference
        localStorage.setItem('resultsViewMode', mode);

        // Redisplay
        this.displayResultsList();
    }

    getCurrentViewMode() {
        return localStorage.getItem('resultsViewMode') || 'list';
    }

    toggleComparisonMode(enabled) {
        this.comparisonMode = enabled;
        this.selectedResults.clear();
        this.displayResultsList();

        // Show/hide comparison controls
        const comparisonControls = document.getElementById('comparison-controls');
        if (comparisonControls) {
            comparisonControls.style.display = enabled ? 'block' : 'none';
        }
    }

    updateComparisonButton() {
        const compareBtn = document.getElementById('compare-selected-btn');
        if (compareBtn) {
            compareBtn.disabled = this.selectedResults.size < 2;
            compareBtn.textContent = `Compare (${this.selectedResults.size})`;
        }
    }

    async exportResults() {
        const exportData = {
            asset: this.currentAsset,
            exportDate: new Date().toISOString(),
            results: this.filteredResults.map(r => ({
                fileName: r.fileName,
                timestamp: r.timestamp,
                summary: r.summary,
                tests: r.tests
            }))
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `results_${this.currentAsset}_${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification('Results exported successfully', 'success');
    }

    exportSingleResult(fileName) {
        const result = this.allResults.find(r => r.fileName === fileName);
        if (!result) return;

        const blob = new Blob([result.raw], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.click();
        URL.revokeObjectURL(url);

        this.showNotification(`Exported ${fileName}`, 'success');
    }

    closeResultDetailsModal() {
        const modal = document.getElementById('enhanced-result-details-modal');
        if (modal) modal.style.display = 'none';
    }

    displayEmptyState() {
        const container = document.getElementById('enhanced-results-list');
        if (!container) return;

        container.innerHTML = `
            <div class="empty-state">
                <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <h3>No Results Found</h3>
                <p>No test results available for this asset</p>
            </div>
        `;
    }

    showLoading(show) {
        const container = document.getElementById('enhanced-results-list');
        if (!container) return;

        if (show) {
            container.innerHTML = `
                <div class="loading-state">
                    <div class="spinner"></div>
                    <p>Loading results...</p>
                </div>
            `;
        }
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    getStatusClass(summary) {
        if (summary.failed > 0) return 'status-failed';
        if (summary.passed === summary.total && summary.total > 0) return 'status-passed';
        if (summary.skipped > 0) return 'status-warning';
        return 'status-unknown';
    }

    getStatusIcon(status) {
        switch (status) {
            case 'passed': return '✓';
            case 'failed': return '✗';
            case 'skipped': return '⊘';
            default: return '?';
        }
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleString();
    }

    formatDuration(ms) {
        if (ms < 1000) return `${ms}ms`;
        if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
        return `${Math.floor(ms / 60000)}m ${Math.floor((ms % 60000) / 1000)}s`;
    }

    formatMetricName(name) {
        return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize when DOM is ready
export function setupEnhancedResultsViewer() {
    const viewer = new EnhancedResultsViewer();
    viewer.initialize();
    return viewer;
}

export { EnhancedResultsViewer };
