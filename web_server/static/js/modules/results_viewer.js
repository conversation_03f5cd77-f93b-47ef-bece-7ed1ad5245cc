import { fetchData } from './utils.js';

// --- Results Viewer Functions ---

async function listResultsForAsset() {
    const assetNumInput = document.getElementById('results-asset-number');
    const resultsListDiv = document.getElementById('results-list');
    const resultDetailsViewDiv = document.getElementById('result-details-view');

    if (!assetNumInput || !resultsListDiv || !resultDetailsViewDiv) return;

    const assetNum = assetNumInput.value.trim();
    if (!assetNum) {
        alert("Please enter an Asset Number to list results.");
        resultsListDiv.innerHTML = '';
        resultDetailsViewDiv.innerHTML = '';
        return;
    }

    resultsListDiv.innerHTML = `<p>Fetching results for asset: ${assetNum}...</p>`;
    resultDetailsViewDiv.innerHTML = ''; // Clear previous details

    const data = await fetchData(`/api/results/${assetNum}`);
    if (!data) {
        resultsListDiv.innerHTML = `<p>Could not fetch results for asset ${assetNum}. Server error or no results found.</p>`;
        return;
    }

    if (data.error) {
         resultsListDiv.innerHTML = `<p>Error: ${data.error}</p>`;
         return;
    }

    let html = `<h4>Available Results for ${assetNum}:</h4>`;
    const allResults = (data.consolidated_results || []).concat(data.individual_results || []);

    if (allResults.length === 0) {
        html += '<p>No result files found for this asset number.</p>';
    } else {
        html += '<ul>';
        allResults.forEach(fileName => {
            html += `<li><a href="#" data-asset="${assetNum}" data-filename="${fileName}" class="result-file-link">${fileName}</a></li>`;
        });
        html += '</ul>';
    }
    resultsListDiv.innerHTML = html;

    // Add event listeners to the new links
    document.querySelectorAll('.result-file-link').forEach(link => {
        link.addEventListener('click', displaySpecificResult);
    });
}

async function displaySpecificResult(event) {
    event.preventDefault();
    const assetNum = event.target.dataset.asset;
    const fileName = event.target.dataset.filename;
    const modal = document.getElementById('result-details-modal');
    const resultDetailsViewDiv = document.getElementById('result-details-view');
    const resultDetailsTitle = document.getElementById('result-details-title');

    if (!assetNum || !fileName || !resultDetailsViewDiv || !modal) return;

    // Show modal and update title
    modal.style.display = 'block';
    if (resultDetailsTitle) {
        resultDetailsTitle.textContent = `Result: ${fileName}`;
    }

    resultDetailsViewDiv.innerHTML = `<p>Fetching ${fileName} for asset ${assetNum}...</p>`;

    try {
        const response = await fetch(`/api/results/${assetNum}/${fileName}`);
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({error: `HTTP error ${response.status}`}));
            throw new Error(errorData.error || `HTTP error ${response.status}`);
        }

        const resultText = await response.text();

        let contentToDisplay;
        try {
            // Try to parse as JSON and pretty-print
            const resultJson = JSON.parse(resultText);
            contentToDisplay = `<pre>${JSON.stringify(resultJson, null, 2)}</pre>`;
        } catch (e) {
            // If not JSON, display as plain text
            const P = document.createElement('pre');
            P.textContent = resultText;
            contentToDisplay = P.outerHTML;
        }
        resultDetailsViewDiv.innerHTML = contentToDisplay;

    } catch (error) {
        console.error(`Error fetching specific result ${fileName}:`, error);
        resultDetailsViewDiv.innerHTML = `<p style="color: red;">Error loading result file ${fileName}: ${error.message}</p>`;
    }
}

function closeResultDetailsModal() {
    const modal = document.getElementById('result-details-modal');
    if (modal) modal.style.display = 'none';
}

export function setupResultsViewer() {
    // Main results viewer section button
    const listResultsBtn = document.getElementById('list-results-btn');
    if (listResultsBtn) {
        listResultsBtn.addEventListener('click', listResultsForAsset);
    }

    // Main results viewer asset number input (for Enter key)
    const resultsAssetNumberInput = document.getElementById('results-asset-number');
    if (resultsAssetNumberInput) {
        resultsAssetNumberInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                listResultsForAsset();
            }
        });
    }

    // Quick action button
    const listResultsBtnQuick = document.getElementById('list-results-btn-quick');
    if (listResultsBtnQuick) {
        listResultsBtnQuick.addEventListener('click', () => {
            const assetNumber = document.getElementById('asset-number').value;
            if (assetNumber) {
                // Pre-fill the asset number in the results viewer section
                if (resultsAssetNumberInput) resultsAssetNumberInput.value = assetNumber;
                
                // Expand the results viewer section and scroll to it
                const resultsSection = document.getElementById('results-viewer');
                if(resultsSection) {
                    resultsSection.classList.add('expanded');
                    resultsSection.scrollIntoView({ behavior: 'smooth' });
                }

                listResultsForAsset();
            } else {
                alert('Please enter an asset number first.');
            }
        });
    }

    // Modal close button and background click
    const modal = document.getElementById('result-details-modal');
    if (modal) {
        const closeButton = modal.querySelector('.close-button');
        if (closeButton) {
            closeButton.addEventListener('click', closeResultDetailsModal);
        }
        window.addEventListener('click', (event) => {
            if (event.target == modal) {
                closeResultDetailsModal();
            }
        });
    }
}
