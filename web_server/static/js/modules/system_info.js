import { fetchData } from './utils.js';

/**
 * Render System Info with accent colors and larger, readable layout.
 * @param {object|null} info
 * @returns {string}
 */
function formatSystemInfo(info) {
    if (!info) {
        return '<div class="sysinfo sysinfo--error">Could not load system information.</div>';
    }

    const gpus = (info.gpus && info.gpus.length > 0) ? info.gpus.join(', ') : 'N/A';

    const battery = (() => {
        if (info.battery && info.battery.present) {
            const parts = [];
            parts.push(`${info.battery.percent ?? 'N/A'}%`);
            if (info.battery.status) parts.push(`${info.battery.status}`);
            if (info.battery.health) parts.push(`Health: ${info.battery.health}`);
            if (info.battery.cycle_count) parts.push(`Cycles: ${info.battery.cycle_count}`);
            return parts.join(' • ');
        }
        return 'No battery detected';
    })();

    const disksBlock = (() => {
        if (info.disks && info.disks.length > 0) {
            const items = info.disks.map((disk, idx) => {
                const smart = (disk.smart_passed === null || disk.smart_passed === undefined)
                    ? 'Unknown'
                    : (disk.smart_passed ? 'OK' : 'BAD');
                const smartClass = (smart === 'OK') ? 'ok' : (smart === 'BAD' ? 'bad' : 'unknown');
                return `
                    <div class="sysinfo-disk">
                        <span class="k">Drive ${idx}:</span>
                        <span class="v">
                            ${disk.model || 'Unknown'} • ${disk.size_gb ?? '?'} GB • ${disk.type || 'Unknown'}
                            <span class="chip chip--${smartClass}" title="SMART">${smart}</span>
                        </span>
                    </div>`;
            }).join('');
            return `<div class="sysinfo-group sysinfo-group--disks">
                        <div class="sysinfo-row">
                            <span class="k">Disks</span><span class="v"></span>
                        </div>
                        <div class="sysinfo-disklist">${items}</div>
                    </div>`;
        }
        return `<div class="sysinfo-group sysinfo-group--disks">
                    <div class="sysinfo-row">
                        <span class="k">Disks</span><span class="v">No disks detected</span>
                    </div>
                </div>`;
    })();

    return `
    <div class="sysinfo">
        <div class="sysinfo-grid">
            <div class="sysinfo-group sysinfo-group--id">
                <div class="sysinfo-row">
                    <span class="k">Serial #</span>
                    <span class="v">${info.serial_number || 'N/A'}</span>
                </div>
            </div>

            <div class="sysinfo-group sysinfo-group--cpu">
                <div class="sysinfo-row">
                    <span class="k">CPU</span>
                    <span class="v">${info.cpu || 'N/A'}</span>
                </div>
            </div>

            <div class="sysinfo-group sysinfo-group--ram">
                <div class="sysinfo-row">
                    <span class="k">Memory</span>
                    <span class="v">${info.memory || 'N/A'}</span>
                </div>
            </div>

            <div class="sysinfo-group sysinfo-group--gpu">
                <div class="sysinfo-row">
                    <span class="k">Graphics</span>
                    <span class="v">${gpus}</span>
                </div>
            </div>

            <div class="sysinfo-group sysinfo-group--display">
                <div class="sysinfo-row">
                    <span class="k">Screen</span>
                    <span class="v">${info.screen_resolution || 'N/A'}</span>
                </div>
            </div>

            <div class="sysinfo-group sysinfo-group--battery">
                <div class="sysinfo-row">
                    <span class="k">Battery</span>
                    <span class="v">${battery}</span>
                </div>
            </div>

            ${disksBlock}
        </div>
    </div>`;
}

/**
 * Fetch system information from the backend and inject it into the DOM.
 */
export async function loadSystemInfo() {
    const systemInfoContent = document.getElementById('system-info-content');
    if (!systemInfoContent) {
        console.error('System info content area not found.');
        return;
    }
    const data = await fetchData('/api/system_info');
    systemInfoContent.innerHTML = formatSystemInfo(data);
}
