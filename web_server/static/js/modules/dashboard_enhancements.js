/**
 * Dashboard Enhancements for Crucible
 * Adds real-time features to existing index.html without changing its design
 * Preserves dashboard-card styling and collapsible sections
 */

class DashboardEnhancements {
    constructor() {
        this.initialized = false;
        this.currentAssetNumber = null;
        this.currentOperatorId = null;
        this.currentProfile = null;
        this.testInProgress = false;
        this.hardwareInfo = null;
    }

    /**
     * Initialize enhancements for existing dashboard
     */
    async initialize() {
        if (this.initialized) return;

        try {
            // Wait for hardware detection
            await this.waitForHardwareDetection();
            
            // Enhance existing form elements
            this.enhanceAssetEntry();
            this.enhanceProfileSelection();
            this.enhanceTestButtons();
            this.enhanceSystemInfo();
            
            // Add real-time features
            this.setupRealTimeUpdates();
            this.addProgressIndicators();
            this.setupKeyboardShortcuts();
            
            this.initialized = true;
            console.log('Dashboard enhancements initialized');
        } catch (error) {
            console.error('Error initializing dashboard enhancements:', error);
        }
    }

    /**
     * Wait for hardware detection to complete
     */
    async waitForHardwareDetection() {
        if (window.CrucibleHardwareDetector && window.CrucibleHardwareDetector.hardwareInfo.screen) {
            this.hardwareInfo = window.CrucibleHardwareDetector.hardwareInfo;
            return;
        }

        return new Promise((resolve) => {
            const checkHardware = () => {
                if (window.CrucibleHardwareDetector && window.CrucibleHardwareDetector.hardwareInfo.screen) {
                    this.hardwareInfo = window.CrucibleHardwareDetector.hardwareInfo;
                    resolve();
                } else {
                    setTimeout(checkHardware, 100);
                }
            };
            checkHardware();
        });
    }

    /**
     * Enhance asset entry section with validation and barcode support
     */
    enhanceAssetEntry() {
        const assetInput = document.getElementById('asset_number');
        const operatorInput = document.getElementById('operator_id');
        
        if (!assetInput || !operatorInput) return;

        // Add real-time validation
        assetInput.addEventListener('input', (e) => {
            this.validateAssetNumber(e.target.value);
            this.currentAssetNumber = e.target.value;
        });

        operatorInput.addEventListener('input', (e) => {
            this.validateOperatorId(e.target.value);
            this.currentOperatorId = e.target.value;
        });

        // Add barcode scanner support (if available)
        this.setupBarcodeScanner(assetInput);

        // Add auto-focus and tab navigation
        assetInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.validateAssetNumber(e.target.value)) {
                operatorInput.focus();
            }
        });

        operatorInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.validateOperatorId(e.target.value)) {
                this.focusNextSection();
            }
        });
    }

    /**
     * Enhance profile selection with hardware-based recommendations
     */
    enhanceProfileSelection() {
        const profileSelect = document.getElementById('profile_select');
        if (!profileSelect) return;

        // Add hardware compatibility indicators
        this.addProfileCompatibilityIndicators(profileSelect);

        // Auto-select recommended profile based on hardware
        this.autoSelectRecommendedProfile(profileSelect);

        profileSelect.addEventListener('change', (e) => {
            this.currentProfile = e.target.value;
            this.updateProfileInfo(e.target.value);
        });
    }

    /**
     * Enhance test buttons with real-time status
     */
    enhanceTestButtons() {
        // Enhance visual test buttons
        const visualTestButtons = document.querySelectorAll('.visual-test-link');
        visualTestButtons.forEach(button => {
            this.enhanceTestButton(button);
        });

        // Enhance main run tests button
        const runTestsBtn = document.getElementById('run_tests_btn');
        if (runTestsBtn) {
            this.enhanceRunTestsButton(runTestsBtn);
        }
    }

    /**
     * Enhance individual test button
     */
    enhanceTestButton(button) {
        const testType = button.dataset.testType;
        if (!testType) return;

        // Add hardware compatibility check
        const compatible = this.checkTestCompatibility(testType);
        if (!compatible.supported) {
            button.disabled = true;
            button.title = compatible.reason;
            button.classList.add('test-incompatible');
        }

        // Add click handler with enhanced functionality
        button.addEventListener('click', (e) => {
            e.preventDefault();
            this.launchEnhancedTest(testType);
        });

        // Add status indicator
        const statusIndicator = document.createElement('span');
        statusIndicator.className = 'test-status-indicator';
        statusIndicator.style.cssText = `
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--status-pending);
            margin-left: var(--space-xs);
        `;
        button.appendChild(statusIndicator);
    }

    /**
     * Enhance run tests button with progress tracking
     */
    enhanceRunTestsButton(button) {
        const originalText = button.textContent;
        
        button.addEventListener('click', async (e) => {
            if (this.testInProgress) return;
            
            e.preventDefault();
            
            if (!this.validateReadyToRun()) {
                this.showValidationErrors();
                return;
            }

            this.testInProgress = true;
            button.disabled = true;
            button.textContent = 'Starting Tests...';

            try {
                await this.runEnhancedTestSuite();
            } catch (error) {
                console.error('Error running test suite:', error);
                this.showError('Failed to start test suite: ' + error.message);
            } finally {
                this.testInProgress = false;
                button.disabled = false;
                button.textContent = originalText;
            }
        });
    }

    /**
     * Enhance system info section with real-time hardware data
     */
    enhanceSystemInfo() {
        const systemInfoSection = document.querySelector('#system-info') || 
                                 document.querySelector('.system-info');
        
        if (!systemInfoSection || !this.hardwareInfo) return;

        // Add hardware compatibility summary
        const compatibilityDiv = document.createElement('div');
        compatibilityDiv.className = 'hardware-compatibility';
        compatibilityDiv.style.cssText = `
            margin-top: var(--space-md);
            padding: var(--space-sm);
            background: var(--bg-secondary);
            border-radius: var(--btn-border-radius);
            font-size: var(--font-size-sm);
        `;

        const summary = window.CrucibleHardwareDetector.getSummary();
        compatibilityDiv.innerHTML = `
            <div style="font-weight: 600; margin-bottom: var(--space-xs);">Hardware Profile</div>
            <div>Resolution: ${summary.resolution}</div>
            <div>Category: ${summary.category.charAt(0).toUpperCase() + summary.category.slice(1)}</div>
            <div>Capabilities: ${summary.capabilities.join(', ')}</div>
        `;

        systemInfoSection.appendChild(compatibilityDiv);

        // Add recommendations if any
        if (summary.recommendations.length > 0) {
            const recommendationsDiv = document.createElement('div');
            recommendationsDiv.className = 'hardware-recommendations';
            recommendationsDiv.style.cssText = `
                margin-top: var(--space-sm);
                padding: var(--space-sm);
                background: var(--accent-warning);
                color: white;
                border-radius: var(--btn-border-radius);
                font-size: var(--font-size-sm);
            `;

            recommendationsDiv.innerHTML = `
                <div style="font-weight: 600; margin-bottom: var(--space-xs);">Recommendations</div>
                ${summary.recommendations.map(rec => `<div>• ${rec.message}</div>`).join('')}
            `;

            systemInfoSection.appendChild(recommendationsDiv);
        }
    }

    /**
     * Setup real-time updates without changing existing UI
     */
    setupRealTimeUpdates() {
        // Listen for hardware changes
        if (window.CrucibleHardwareDetector) {
            window.CrucibleHardwareDetector.on('viewport-changed', (viewport) => {
                this.updateViewportInfo(viewport);
            });

            window.CrucibleHardwareDetector.on('memory-updated', (memory) => {
                this.updateMemoryInfo(memory);
            });
        }

        // Setup periodic status checks
        setInterval(() => {
            this.updateTestStatuses();
        }, 5000);
    }

    /**
     * Add subtle progress indicators to existing elements
     */
    addProgressIndicators() {
        // Add CSS for progress indicators
        const style = document.createElement('style');
        style.textContent = `
            .test-status-indicator.running {
                background: var(--status-running);
                animation: pulse 2s infinite;
            }
            .test-status-indicator.pass {
                background: var(--status-pass);
            }
            .test-status-indicator.fail {
                background: var(--status-fail);
            }
            .test-incompatible {
                opacity: 0.6;
                cursor: not-allowed;
            }
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Setup keyboard shortcuts for efficiency
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Alt+A: Focus asset number
            if (e.altKey && e.key === 'a') {
                e.preventDefault();
                document.getElementById('asset_number')?.focus();
            }
            
            // Alt+O: Focus operator ID
            if (e.altKey && e.key === 'o') {
                e.preventDefault();
                document.getElementById('operator_id')?.focus();
            }
            
            // Alt+R: Run tests (if ready)
            if (e.altKey && e.key === 'r') {
                e.preventDefault();
                if (this.validateReadyToRun()) {
                    document.getElementById('run_tests_btn')?.click();
                }
            }
            
            // F5: Refresh hardware detection
            if (e.key === 'F5' && e.ctrlKey) {
                e.preventDefault();
                this.refreshHardwareDetection();
            }
        });
    }

    /**
     * Check test compatibility with current hardware
     */
    checkTestCompatibility(testType) {
        if (!this.hardwareInfo) {
            return { supported: true, reason: 'Hardware detection pending' };
        }

        const capabilities = this.hardwareInfo.capabilities;
        
        switch (testType) {
            case 'touch':
                return capabilities.touch ? 
                    { supported: true } : 
                    { supported: false, reason: 'No touch input detected' };
                    
            case 'lcd':
                return capabilities.canvas ? 
                    { supported: true } : 
                    { supported: false, reason: 'Canvas not supported' };
                    
            case 'keyboard':
            case 'pointing':
            case 'cpu':
            case 'ram':
                return { supported: true };
                
            default:
                return { supported: true };
        }
    }

    /**
     * Launch enhanced test with real-time tracking
     */
    async launchEnhancedTest(testType) {
        const assetNumber = this.currentAssetNumber || document.getElementById('asset_number')?.value || 'WEBUI_TEST';
        
        // Open test in new window/tab
        const testUrl = this.getTestUrl(testType, assetNumber);
        const testWindow = window.open(testUrl, '_blank', 'width=800,height=600');
        
        // Update button status
        const button = document.querySelector(`[data-test-type="${testType}"]`);
        if (button) {
            const indicator = button.querySelector('.test-status-indicator');
            if (indicator) {
                indicator.className = 'test-status-indicator running';
            }
        }
        
        // Monitor test completion
        this.monitorTestWindow(testWindow, testType);
    }

    /**
     * Get test URL for specific test type
     */
    getTestUrl(testType, assetNumber) {
        const baseUrls = {
            'lcd': '/visual_test',
            'ram': '/visual_test',
            'cpu': '/visual_test',
            'keyboard': '/keyboard_test',
            'pointing': '/pointing_device_test',
            'touch': '/touch_screen_test'
        };
        
        const baseUrl = baseUrls[testType] || '/visual_test';
        return `${baseUrl}?test_type=${testType}&asset_number=${encodeURIComponent(assetNumber)}`;
    }

    /**
     * Monitor test window for completion
     */
    monitorTestWindow(testWindow, testType) {
        const checkInterval = setInterval(() => {
            if (testWindow.closed) {
                clearInterval(checkInterval);
                this.handleTestCompletion(testType);
            }
        }, 1000);
    }

    /**
     * Handle test completion
     */
    handleTestCompletion(testType) {
        const button = document.querySelector(`[data-test-type="${testType}"]`);
        if (button) {
            const indicator = button.querySelector('.test-status-indicator');
            if (indicator) {
                // For now, assume pass - could be enhanced with actual result
                indicator.className = 'test-status-indicator pass';
            }
        }
    }

    /**
     * Run enhanced test suite with real-time dashboard
     */
    async runEnhancedTestSuite() {
        const formData = {
            asset_number: this.currentAssetNumber || document.getElementById('asset_number')?.value,
            operator_id: this.currentOperatorId || document.getElementById('operator_id')?.value,
            profile_name: this.currentProfile || document.getElementById('profile_select')?.value
        };

        if (!formData.asset_number || !formData.operator_id || !formData.profile_name) {
            throw new Error('Missing required fields');
        }

        // Open enhanced test execution dashboard
        const dashboardUrl = `/test_execution?${new URLSearchParams(formData).toString()}`;
        window.open(dashboardUrl, '_blank', 'width=1200,height=800');

        // Start test suite via API
        const response = await fetch('/api/run_tests', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(formData)
        });

        if (!response.ok) {
            const error = await response.json();
            throw new Error(error.error || 'Failed to start tests');
        }

        const result = await response.json();
        this.showSuccess(result.message);
    }

    /**
     * Validation helpers
     */
    validateAssetNumber(value) {
        return value && value.trim().length >= 3;
    }

    validateOperatorId(value) {
        return value && value.trim().length >= 2;
    }

    validateReadyToRun() {
        const assetNumber = document.getElementById('asset_number')?.value;
        const operatorId = document.getElementById('operator_id')?.value;
        const profile = document.getElementById('profile_select')?.value;
        
        return this.validateAssetNumber(assetNumber) && 
               this.validateOperatorId(operatorId) && 
               profile;
    }

    /**
     * UI feedback helpers
     */
    showValidationErrors() {
        // Add subtle validation feedback without changing design
        const assetInput = document.getElementById('asset_number');
        const operatorInput = document.getElementById('operator_id');
        
        if (assetInput && !this.validateAssetNumber(assetInput.value)) {
            assetInput.style.borderColor = 'var(--accent-danger)';
            setTimeout(() => assetInput.style.borderColor = '', 3000);
        }
        
        if (operatorInput && !this.validateOperatorId(operatorInput.value)) {
            operatorInput.style.borderColor = 'var(--accent-danger)';
            setTimeout(() => operatorInput.style.borderColor = '', 3000);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Use the notification system from WebSocket manager
        if (window.CrucibleRealTimeUI) {
            window.CrucibleRealTimeUI.showNotification(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Utility methods
     */
    focusNextSection() {
        const profileSelect = document.getElementById('profile_select');
        if (profileSelect) {
            profileSelect.focus();
        }
    }

    addProfileCompatibilityIndicators(select) {
        // Could be enhanced to show which tests are compatible with each profile
    }

    autoSelectRecommendedProfile(select) {
        // Could be enhanced to auto-select based on hardware capabilities
    }

    updateProfileInfo(profileName) {
        // Could be enhanced to show profile details
    }

    updateViewportInfo(viewport) {
        // Update any viewport-dependent UI elements
    }

    updateMemoryInfo(memory) {
        // Update memory usage indicators if needed
    }

    updateTestStatuses() {
        // Periodically check test statuses
    }

    refreshHardwareDetection() {
        if (window.CrucibleHardwareDetector) {
            window.CrucibleHardwareDetector.initialize();
        }
    }

    setupBarcodeScanner(input) {
        // Placeholder for barcode scanner integration
        // Could be enhanced with actual barcode scanner support
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        window.CrucibleDashboard = new DashboardEnhancements();
        window.CrucibleDashboard.initialize();
    });
} else {
    window.CrucibleDashboard = new DashboardEnhancements();
    window.CrucibleDashboard.initialize();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardEnhancements;
}
