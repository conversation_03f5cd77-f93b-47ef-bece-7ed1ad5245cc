/**
 * Validates and adjusts CPU test parameters to ensure they are within safe bounds.
 * @param {Object} params - The CPU test parameters to validate.
 * @returns {Object} - The validated and adjusted parameters.
 */
function _validateCpuTestParams(params) {
  const validatedParams = { ...params };
  let adjustmentsMade = false;

  // Validate duration (5-300 seconds)
  if (validatedParams.duration_seconds) {
    const originalDuration = validatedParams.duration_seconds;
    validatedParams.duration_seconds = Math.max(
      5,
      Math.min(300, parseInt(validatedParams.duration_seconds) || 20)
    );

    if (validatedParams.duration_seconds !== originalDuration) {
      console.log(
        `CPU test duration adjusted from ${originalDuration} to ${validatedParams.duration_seconds} seconds (valid range: 5-300)`
      );
      adjustmentsMade = true;
    }
  } else {
    validatedParams.duration_seconds = 20; // Default value
    console.log("CPU test duration set to default: 20 seconds");
  }

  // Validate CPU intensity (50-100%)
  if (validatedParams.cpu_load_intensity) {
    const originalIntensity = validatedParams.cpu_load_intensity;
    validatedParams.cpu_load_intensity = Math.max(
      50,
      Math.min(100, parseInt(validatedParams.cpu_load_intensity) || 100)
    );

    if (validatedParams.cpu_load_intensity !== originalIntensity) {
      console.log(
        `CPU load intensity adjusted from ${originalIntensity}% to ${validatedParams.cpu_load_intensity}% (valid range: 50-100%)`
      );
      adjustmentsMade = true;
    }
  } else {
    validatedParams.cpu_load_intensity = 100; // Default value
    console.log("CPU load intensity set to default: 100%");
  }

  if (adjustmentsMade) {
    console.log(
      "CPU test parameters have been automatically adjusted for safety and compliance"
    );
  }

  return validatedParams;
}

/**
 * Launches a visual test in a new window based on the test type and asset number.
 * Handles specific parameter gathering for tests like RAM and CPU.
 * @param {string} testType - The type of visual test to launch (e.g., 'ram', 'cpu', 'keyboard').
 * @param {string} assetNumber - The asset number to associate with the test.
 * @param {string|null} sessionId - Optional session ID for the test.
 * @param {Object|null} testConfig - Optional test configuration from profile.
 */
export function launchVisualTest(
  testType,
  assetNumber,
  sessionId = null,
  testConfig = null
) {
  console.log(
    `🚀 launchVisualTest called: testType=${testType}, assetNumber=${assetNumber}, sessionId=${sessionId}`
  );

  let params = {
    asset_number: assetNumber,
  };

  let targetUrl = "/visual_test"; // Default target for most visual tests

  if (testType === "ram") {
    params.test_type = "ram";

    // Use profile configuration if available, otherwise prompt user
    if (testConfig && testConfig.test_size_mb && testConfig.duration_seconds) {
      params.test_size_mb = testConfig.test_size_mb;
      params.duration_seconds = testConfig.duration_seconds;
    } else {
      const defaultSize = 512;
      const defaultDuration = 15;
      params.test_size_mb =
        parseInt(
          prompt(
            `Enter RAM test size (MB) for ${assetNumber}:`,
            defaultSize.toString()
          )
        ) || defaultSize;
      params.duration_seconds =
        parseInt(
          prompt("Enter test duration (seconds):", defaultDuration.toString())
        ) || defaultDuration;
    }
  } else if (testType === "cpu") {
    params.test_type = "cpu";

    // Use profile configuration if available, otherwise prompt user
    if (testConfig && testConfig.duration_seconds) {
      // Apply runtime parameter validation and safety checks
      const validatedConfig = _validateCpuTestParams(testConfig);
      params.duration_seconds = validatedConfig.duration_seconds;
      if (validatedConfig.cpu_load_intensity) {
        params.cpu_load_intensity = validatedConfig.cpu_load_intensity;
      }
    } else {
      const defaultDurationCPU = 20;
      const durationInput =
        parseInt(
          prompt(
            `Enter CPU test duration (seconds) for ${assetNumber}:`,
            defaultDurationCPU.toString()
          )
        ) || defaultDurationCPU;

      // Prompt for CPU load intensity when profile config is missing
      const defaultIntensity = 100;
      const intensityInput = prompt(
        `Enter CPU load intensity (50-100%) for ${assetNumber}:`,
        defaultIntensity.toString()
      );
      const intensity = intensityInput
        ? parseInt(intensityInput) || defaultIntensity
        : defaultIntensity;

      // Apply runtime parameter validation and safety checks
      const validatedParams = _validateCpuTestParams({
        duration_seconds: durationInput,
        cpu_load_intensity: intensity,
      });

      params.duration_seconds = validatedParams.duration_seconds;
      params.cpu_load_intensity = validatedParams.cpu_load_intensity;
    }
  } else if (testType === "lcd") {
    // Use simple LCD test - manual color advancement with PASS/FAIL buttons
    targetUrl = "/simple_lcd_test";
    params.test_type = "lcd";
  } else if (testType === "keyboard") {
    // Use standalone enhanced keyboard test - no backend API calls
    const keyboardUrl = `/enhanced_keyboard_test?asset_number=${assetNumber}`;
    const windowFeatures = `width=${screen.width},height=${screen.height},top=0,left=0,resizable=yes,scrollbars=no,status=no,menubar=no,toolbar=no,location=no`;
    const testWindow = window.open(keyboardUrl, `KeyboardTest_${assetNumber}`, windowFeatures);
    if (testWindow) {
      try {
        testWindow.focus();
      } catch (_) {}
    } else {
      alert("Failed to open keyboard test window. Please allow pop-ups for this site.");
    }
    return testWindow;
  } else if (testType === "pointing") {
    targetUrl = "/pointing_device_test";
  } else if (testType === "touch") {
    targetUrl = "/touch_screen_test";
  } else {
    console.error("Unknown visual test type:", testType);
    alert(`Test type "${testType}" is not recognized for visual launch.`);
    return;
  }

  if (targetUrl === "/visual_test" && !params.test_type) {
    params.test_type = testType;
  }

  if (sessionId) {
    params.session_id = sessionId;
  }
  const queryString = new URLSearchParams(params).toString();
  const fullUrl = `${targetUrl}?${queryString}`;

  let windowFeatures;

  if (testType === "lcd") {
    // LCD test should open in fullscreen for proper display testing
    windowFeatures = `width=${screen.width},height=${screen.height},top=0,left=0,resizable=yes,scrollbars=no,status=no,menubar=no,toolbar=no,location=no`;
  } else {
    // Other tests use standard window dimensions
    const windowWidth = 1024;
    const windowHeight = 768;
    const left = window.screen.width / 2 - windowWidth / 2;
    const top = window.screen.height / 2 - windowHeight / 2;
    windowFeatures = `width=${windowWidth},height=${windowHeight},top=${top},left=${left},resizable=yes,scrollbars=yes,status=yes`;
  }

  const testWindow = window.open(
    fullUrl,
    `VisualTestWindow_${testType}_${assetNumber}`,
    windowFeatures
  );
  if (testWindow) {
    try {
      testWindow.focus();
    } catch (_) {}
  } else {
    alert(
      "Failed to open test window. Please allow pop-ups for this site and click Retry when ready."
    );
  }
  return testWindow;
}

/* ---------------- Visual Test Sequence Overlay ---------------- */
const OVERLAY_ID = "visual-sequence-overlay";
let _sequenceStop = false;
let _sequenceCurrentWin = null;
let _sequenceCurrentSessionId = null;

function _createOverlay(total) {
  if (document.getElementById(OVERLAY_ID)) return;
  const overlay = document.createElement("div");
  overlay.id = OVERLAY_ID;
  overlay.style.cssText = `position:fixed;top:20px;right:20px;background:rgba(0,0,0,0.85);color:#fff;padding:12px 16px;border-radius:6px;z-index:9999;font-family:Arial, sans-serif;min-width:220px;`; // basic styling
  overlay.innerHTML = `<div id="vt-seq-text" style="margin-bottom:6px;">Preparing visual tests…</div><button id="vt-seq-cancel" style="padding:4px 8px;">Cancel</button>`;
  document.body.appendChild(overlay);
  document.getElementById("vt-seq-cancel").addEventListener("click", () => {
    if (confirm("Cancel the remaining visual tests?")) {
      _sequenceStop = true;
      if (_sequenceCurrentSessionId) {
        fetch(`/api/visual_test/stop/${_sequenceCurrentSessionId}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ result: "cancel" }),
        }).catch(() => {});
      }
      if (_sequenceCurrentWin && !_sequenceCurrentWin.closed)
        _sequenceCurrentWin.close();
      _hideOverlay();
    }
  });
}
function _updateOverlay(currentIdx, total, testType) {
  const txt = document.getElementById("vt-seq-text");
  if (txt) txt.textContent = `Running ${testType} (${currentIdx + 1}/${total})`;
}
function _hideOverlay() {
  const el = document.getElementById(OVERLAY_ID);
  if (el) el.remove();
}

/**
 * Sets up event listeners for visual test links on the main page.
 * When a link is clicked, it calls launchVisualTest with the appropriate parameters.
 */
// Map profile test path to visual testType
function _determineTestTypeFromPath(testPath) {
  const lowered = testPath.toLowerCase();
  if (lowered.includes("keyboard")) return "keyboard";
  if (lowered.includes("pointing")) return "pointing";
  if (lowered.includes("touch")) return "touch";
  if (lowered.includes("visual_cpu") || lowered.includes("cpu")) return "cpu";
  if (lowered.includes("visual_ram") || lowered.includes("ram")) return "ram";
  if (lowered.includes("lcd")) return "lcd";
  return null;
}

async function _runSequence(testTypes, assetNumber, profile, idx = 0) {
  console.log(
    `_runSequence called: idx=${idx}, testType=${
      testTypes[idx] || "none"
    }, total=${testTypes.length}`
  );

  if (_sequenceStop) {
    console.log("Sequence stopped, hiding overlay");
    _hideOverlay();
    return;
  }
  if (idx >= testTypes.length) {
    console.log("Visual test sequence completed.");
    _hideOverlay();
    return;
  }
  const testType = testTypes[idx];
  console.log(`Processing test type: ${testType} at index ${idx}`);

  // Extract test configuration from profile for this test type
  let testConfig = null;
  if (profile && profile.test_args) {
    // Find the test path that matches this test type
    const testPath = profile.tests.find((path) => {
      const lowered = path.toLowerCase();
      return (
        (testType === "ram" &&
          (lowered.includes("visual_ram") || lowered.includes("ram"))) ||
        (testType === "cpu" &&
          (lowered.includes("visual_cpu") || lowered.includes("cpu")))
      );
    });

    if (testPath && profile.test_args[testPath]) {
      const config = profile.test_args[testPath];

      // For RAM tests, calculate actual test size if using percentage mode
      if (testType === "ram" && config.test_size_mode === "percentage") {
        // Get system memory info to calculate actual MB
        try {
          const memResp = await fetch("/api/system_info");
          if (memResp.ok) {
            const sysInfo = await memResp.json();
            const availableMB = sysInfo.memory?.available_mb || 4096; // fallback
            const testSizeMB = Math.floor(
              (availableMB * config.test_size_value) / 100
            );
            testConfig = {
              test_size_mb: testSizeMB,
              duration_seconds: config.duration_seconds || 30,
            };
          }
        } catch (err) {
          console.warn(
            "Could not get system memory info, using fallback:",
            err
          );
          // Fallback calculation assuming 8GB system
          const testSizeMB = Math.floor((8192 * config.test_size_value) / 100);
          testConfig = {
            test_size_mb: testSizeMB,
            duration_seconds: config.duration_seconds || 30,
          };
        }
      } else if (testType === "ram" && config.test_size_mode === "absolute") {
        testConfig = {
          test_size_mb: config.test_size_value,
          duration_seconds: config.duration_seconds || 30,
        };
      } else if (testType === "cpu" && config.duration_seconds) {
        testConfig = {
          duration_seconds: config.duration_seconds,
        };
        // Add support for cpu_load_intensity parameter from profile test_args
        if (config.cpu_load_intensity) {
          testConfig.cpu_load_intensity = config.cpu_load_intensity;
        }
        // Add logging for CPU test parameters being used from profile
        console.log(
          `Using CPU test configuration from profile: duration=${
            testConfig.duration_seconds
          }s, intensity=${testConfig.cpu_load_intensity || "default"}%`
        );
      }
    }
  }

  // Special handling for keyboard tests - use standalone enhanced test (BEFORE backend API call)
  if (testType === "keyboard") {
    console.log(`🎯 Keyboard test detected - launching standalone enhanced test`);

    // Update overlay for keyboard before launching since it runs standalone
    _updateOverlay(idx, testTypes.length, testType);
 
    // Launch standalone enhanced keyboard test (no backend API calls)
    const keyboardUrl = `/enhanced_keyboard_test?asset_number=${assetNumber}`;
    const windowFeatures = `width=${screen.width},height=${screen.height},top=0,left=0,resizable=yes,scrollbars=no,status=no,menubar=no,toolbar=no,location=no`;
    const testWindow = window.open(keyboardUrl, `KeyboardTest_${assetNumber}`, windowFeatures);
    
    if (testWindow) {
      try {
        testWindow.focus();
        console.log(`✅ Enhanced keyboard test launched successfully`);
        // Track the window and auto-advance the sequence when it closes
        _sequenceCurrentWin = testWindow;
        _sequenceCurrentSessionId = null;
        const openTs = Date.now();
        const timer = setInterval(() => {
          if (_sequenceStop) {
            clearInterval(timer);
            if (!testWindow.closed) testWindow.close();
            _hideOverlay();
            return;
          }
          if (testWindow.closed) {
            clearInterval(timer);
            const elapsedMs = Date.now() - openTs;
            if (elapsedMs < 2000) {
              console.log(
                `Keyboard test window closed quickly (${elapsedMs}ms). Showing retry dialog`
              );
              if (
                confirm(
                  "Test window closed too quickly (possibly blocked or closed accidentally). Retry this test?"
                )
              ) {
                console.log("User chose to retry keyboard test");
                _runSequence(testTypes, assetNumber, profile, idx); // retry same
                return;
              } else {
                console.log("User chose not to retry keyboard test");
              }
            }
            // Proceed to next test (or complete sequence if this was last)
            _runSequence(testTypes, assetNumber, profile, idx + 1);
          }
        }, 600);
        return; // prevent falling through
      } catch (_) {}
    } else {
      console.error("Failed to open enhanced keyboard test window");
      // Skip to next after short delay to avoid stuck overlay
      setTimeout(() => _runSequence(testTypes, assetNumber, profile, idx + 1), 1000);
      return;
    }
  }

  // Start visual test session on backend for non-keyboard tests
  let sessionId = null;
  try {
    console.log(
      `Starting visual test session for ${testType} on asset ${assetNumber}`
    );
    console.log(`Test config:`, testConfig);
    const resp = await fetch("/api/visual_test/start", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        test_type: testType,
        asset_number: assetNumber,
        test_params: testConfig || {},
      }),
    });
    if (resp.ok) {
      const data = await resp.json();
      sessionId = data.session_id;
      console.log(`Visual test session started: ${sessionId}`);
    } else {
      console.warn("Could not start visual test session:", resp.status);
    }
  } catch (err) {
    console.error("Failed to start visual test session:", err);
  }
  _updateOverlay(idx, testTypes.length, testType);
  console.log(
    `Launching visual test [${idx + 1}/${testTypes.length}]: ${testType}`,
    testConfig ? "with profile config" : "with prompts"
  );

  // Regular handling for other visual tests (LCD, RAM, CPU, etc.)
  const openTs = Date.now();
  console.log(`🎯 About to call launchVisualTest for ${testType}`);
  const testWin = launchVisualTest(
    testType,
    assetNumber,
    sessionId,
    testConfig
  );
  console.log(
    `🎯 launchVisualTest returned for ${testType}:`,
    testWin ? "window object" : "null"
  );
  _sequenceCurrentWin = testWin;
  _sequenceCurrentSessionId = sessionId;
  if (!testWin) {
    // Failed to open window; skip to next after short delay
    setTimeout(
      () => _runSequence(testTypes, assetNumber, profile, idx + 1),
      1000
    );
    return;
  }
  const timer = setInterval(() => {
    if (_sequenceStop) {
      clearInterval(timer);
      if (!testWin.closed) testWin.close();
      _hideOverlay();
      return;
    }
    if (testWin.closed) {
      clearInterval(timer);
      const elapsedMs = Date.now() - openTs;
      const resultFlag = elapsedMs < 2000 ? "cancel" : "completed";
      if (sessionId) {
        fetch(`/api/visual_test/stop/${sessionId}`, {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ result: resultFlag }),
        }).catch(() => {});
      }
      if (elapsedMs < 2000) {
        console.log(
          `Test window closed quickly (${elapsedMs}ms). Showing retry dialog for ${testType}`
        );
        if (
          confirm(
            "Test window closed too quickly (possibly blocked or closed accidentally). Retry this test?"
          )
        ) {
          console.log(`User chose to retry ${testType} test`);
          _runSequence(testTypes, assetNumber, profile, idx); // retry same
          return;
        } else {
          console.log(`User chose not to retry ${testType} test`);
        }
      }
      // Proceed to next test
      _runSequence(testTypes, assetNumber, profile, idx + 1);
    }
  }, 600);
}

export async function runVisualTestsFromProfile(profileName, assetNumber) {
  if (!profileName) return;
  try {
    const resp = await fetch(
      `/api/profiles/${encodeURIComponent(profileName)}`
    );
    if (!resp.ok) {
      console.warn("Could not fetch profile to run visual tests:", resp.status);
      return;
    }
    const profile = await resp.json();
    if (!profile.tests || !Array.isArray(profile.tests)) {
      console.warn("Profile has no tests array.");
      return;
    }

    console.log("Profile tests:", profile.tests);

    // Determine visual tests in declared order
    const visualTestTypes = profile.tests
      .map((p) => {
        const testType = _determineTestTypeFromPath(p);
        console.log(`Test path: ${p} -> Test type: ${testType}`);
        return testType;
      })
      .filter((t) => !!t);

    console.log("Visual test types found:", visualTestTypes);

    if (visualTestTypes.length === 0) {
      console.log("No visual tests found in profile.");
      return;
    }
    _sequenceStop = false;
    _createOverlay(visualTestTypes.length);
    _runSequence(visualTestTypes, assetNumber, profile);
  } catch (err) {
    console.error("Failed to run visual tests from profile:", err);
  }
}

export function setupVisualTestLinks() {
  const visualTestLinks = document.querySelectorAll(".visual-test-link");
  visualTestLinks.forEach((link) => {
    link.addEventListener("click", function (event) {
      event.preventDefault();
      const testType = this.dataset.testType;
      const assetNumberField = document.getElementById("asset-number");
      const assetNumber = assetNumberField
        ? assetNumberField.value
        : "WEBUI_DEF_ASSET";

      if (!assetNumber || !assetNumber.trim()) {
        alert(
          "Please enter an Asset Number in the main input field before launching a visual test."
        );
        assetNumberField.focus();
        return;
      }

      launchVisualTest(testType, assetNumber);
    });
  });
}
