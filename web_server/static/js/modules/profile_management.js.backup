import { fetchData } from './utils.js';

// To be populated from the backend
let availableTests = [];

// --- Core Profile Loading & Display ---

async function loadProfiles() {
    const profileSelect = document.getElementById('profile-select');
    const profileSelectQuick = document.getElementById('profile-select-quick');

    const profiles = await fetchData('/api/profiles');
    if (!profiles) {
        if (profileSelect) profileSelect.innerHTML = '<option value="">Error loading profiles</option>';
        if (profileSelectQuick) profileSelectQuick.innerHTML = '<option value="">Error loading profiles</option>';
        return;
    }

    const optionHTML = '<option value="">-- Select Profile --</option>' +
        profiles.map(profile => `<option value="${profile.name}">${profile.name}</option>`).join('');

    if (profileSelect) profileSelect.innerHTML = optionHTML;
    if (profileSelectQuick) profileSelectQuick.innerHTML = optionHTML;

    // After loading, ensure buttons are in correct state
    const editBtn = document.getElementById('edit-profile-btn');
    const deleteBtn = document.getElementById('delete-profile-btn');
    if(editBtn) editBtn.disabled = true;
    if(deleteBtn) deleteBtn.disabled = true;

    const profileDetails = document.getElementById('profile-details');
    if (profileDetails) profileDetails.innerHTML = '';
}

async function displaySelectedProfileDetails() {
    const profileSelect = document.getElementById('profile-select');
    const profileDetailsDiv = document.getElementById('profile-details');
    if (!profileSelect || !profileDetailsDiv) return;

    const profileName = profileSelect.value;
    if (!profileName) {
        profileDetailsDiv.innerHTML = '<p>Please select a profile to see details.</p>';
        return;
    }

    profileDetailsDiv.innerHTML = '<p>Loading details...</p>';
    const profile = await fetchData(`/api/profiles/${profileName}`);
    if (!profile) {
        profileDetailsDiv.innerHTML = `<p>Error loading details for ${profileName}.</p>`;
        return;
    }

    let html = `<h3>${profile.name}</h3>`;
    html += `<p><strong>Description:</strong> ${profile.description || 'N/A'}</p>`;
    html += `<p><strong>Device Type:</strong> ${profile.device_type || 'N/A'}</p>`;
    html += `<p><strong>Tests:</strong></p><ul>`;
    if (profile.tests && profile.tests.length > 0) {
        profile.tests.forEach(test => {
            html += `<li>${test}</li>`;
        });
    } else {
        html += `<li>No tests configured.</li>`;
    }
    html += `</ul>`;
    
    // Display RAM test configuration if present
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];
    
    let ramConfig = null;
    if (profile.test_args) {
        for (const path of ramTestPaths) {
            if (profile.test_args[path] && 
                (profile.test_args[path].test_size_mode || profile.test_args[path].test_size_value)) {
                ramConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    if (ramConfig) {
        html += `<p><strong>RAM Test Configuration:</strong></p>`;
        html += `<ul>`;
        html += `<li><strong>Mode:</strong> ${ramConfig.test_size_mode === 'percentage' ? 'Percentage' : 'Absolute'}</li>`;
        html += `<li><strong>Size:</strong> ${ramConfig.test_size_value}${ramConfig.test_size_mode === 'percentage' ? '%' : ' MB'}</li>`;
        html += `<li><strong>Duration:</strong> ${ramConfig.duration_seconds || 30} seconds</li>`;
        html += `</ul>`;
    }
    
    if (profile.test_args && Object.keys(profile.test_args).length > 0) {
        html += `<p><strong>Test Arguments:</strong></p><pre>${JSON.stringify(profile.test_args, null, 2)}</pre>`;
    } else {
        html += `<p><strong>Test Arguments:</strong> None</p>`;
    }
    // Append validation details
    try {
        const validation = await fetchData(`/api/profiles/validate/${encodeURIComponent(profileName)}`);
        if (validation) {
            if (validation.missing_count === 0) {
                html += `<p style="color:green;">All tests resolved successfully.</p>`;
            } else {
                html += `<p style="color:#f44336;">⚠ ${validation.missing_count} tests unavailable:</p><ul>`;
                validation.tests.filter(t=>!t.available).forEach(t=>{
                    html += `<li>${t.path} – ${t.reason}</li>`;
                });
                html += '</ul>';
            }
        }
    } catch(e) {
        console.warn('Validation fetch error', e);
    }

    profileDetailsDiv.innerHTML = html;
}

async function displayProfileDetailsQuick(profileName) {
    const profileDetailsQuick = document.getElementById('profile-details-quick');
    if (!profileDetailsQuick) return;

    if (!profileName) {
        profileDetailsQuick.innerHTML = '';
        return;
    }

    const profile = await fetchData(`/api/profiles/${profileName}`);
    if (!profile) {
        profileDetailsQuick.innerHTML = `<p style="color: #f44336;">Error loading profile details</p>`;
        return;
    }

    let html = `<h4>${profile.name}</h4>`;
    html += `<div class="profile-info">`;
    html += `<strong>Device Type:</strong><span>${profile.device_type || 'N/A'}</span>`;
    html += `<strong>Tests:</strong><span>${profile.tests && profile.tests.length > 0 ? profile.tests.length + ' test(s)' : 'No tests'}</span>`;
    if (profile.description) {
        html += `<strong>Description:</strong><span>${profile.description}</span>`;
    }
    
    // Show RAM configuration if present
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];
    
    let ramConfig = null;
    if (profile.test_args) {
        for (const path of ramTestPaths) {
            if (profile.test_args[path] && 
                (profile.test_args[path].test_size_mode || profile.test_args[path].test_size_value)) {
                ramConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    if (ramConfig) {
        const sizeDisplay = ramConfig.test_size_mode === 'percentage' 
            ? `${ramConfig.test_size_value}%` 
            : `${ramConfig.test_size_value} MB`;
        html += `<strong>RAM Test:</strong><span>${sizeDisplay}, ${ramConfig.duration_seconds || 30}s</span>`;
    }
    
    html += `</div>`;

    if (profile.tests && profile.tests.length > 0) {
        html += `<div style="margin-top: 0.5rem;"><strong>Test sequence:</strong> ${profile.tests.join(' → ')}</div>`;
    }

    // Fetch validation results for quick display
    try {
        const validation = await fetchData(`/api/profiles/validate/${encodeURIComponent(profileName)}`);
        if (validation && validation.missing_count > 0) {
            html += `<div style="color:#f44336;margin-top:0.35rem;font-size:0.85rem;">⚠ ${validation.missing_count} test(s) unavailable</div>`;
        }
    } catch (err) {
        console.warn('Failed to fetch profile validation:', err);
    }

    profileDetailsQuick.innerHTML = html;
}


// --- Modal & Form Logic ---

function setupProfileModal() {
    const modal = document.getElementById('profile-modal');
    const closeButton = document.querySelector('.modal .close-button');
    const profileForm = document.getElementById('profile-form');

    if (closeButton) {
        closeButton.onclick = () => modal.style.display = "none";
    }
    window.onclick = (event) => {
        if (event.target == modal) {
            modal.style.display = "none";
        }
    }
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileFormSubmit);
    }
    // Populate available tests by fetching from the new endpoint
    fetchAndPopulateAvailableTests();
    
    // Setup RAM configuration UI
    setupRamConfigurationUI();
    
    // Setup CPU configuration UI
    setupCpuTestConfiguration();
}

async function fetchAndPopulateAvailableTests() {
    const testsData = await fetchData('/api/available_tests');
    if (testsData && Array.isArray(testsData)) {
        populateTestCheckboxes(testsData);
    } else {
        console.error("Could not fetch or parse available tests. Using fallback or empty list.");
        populateTestCheckboxes([]);
    }
}

function populateTestCheckboxes(testsWithOptions) {
    availableTests = testsWithOptions;
    const checkboxesContainer = document.getElementById('profile-tests-checkboxes');
    if (!checkboxesContainer) return;

    checkboxesContainer.innerHTML = '';
    if (testsWithOptions.length === 0) {
        checkboxesContainer.innerHTML = '<p>No tests available or could not load test list.</p>';
        return;
    }

    testsWithOptions.forEach(testOption => {
        const label = document.createElement('label');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = 'profile-test';
        checkbox.value = testOption.path;

        label.appendChild(checkbox);
        label.appendChild(document.createTextNode(` ${testOption.name}`));
        checkboxesContainer.appendChild(label);
    });
    
    // Update RAM config visibility after populating checkboxes
    setTimeout(() => updateRamConfigVisibility(), 100);
}

async function openProfileModalForCreate() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');

    if (!modal || !modalTitle || !profileForm) return;

    modalTitle.textContent = 'Create Profile';
    profileForm.reset();
    document.getElementById('profile-id').value = '';
    document.getElementById('profile-name').disabled = false;

    const checkboxes = profileForm.querySelectorAll('input[name="profile-test"]');
    checkboxes.forEach(cb => cb.checked = false);

    // Reset RAM configuration to defaults
    setRamConfigInForm(null);

    modal.style.display = 'block';
}

async function openProfileModalForEdit() {
    const modal = document.getElementById('profile-modal');
    const modalTitle = document.getElementById('profile-modal-title');
    const profileForm = document.getElementById('profile-form');
    const profileSelect = document.getElementById('profile-select');

    if (!modal || !modalTitle || !profileForm || !profileSelect) return;

    const profileName = profileSelect.value;
    if (!profileName) {
        alert('Please select a profile to edit.');
        return;
    }

    const profile = await fetchData(`/api/profiles/${profileName}`);
    if (!profile) {
        alert(`Could not load profile: ${profileName}`);
        return;
    }

    modalTitle.textContent = 'Edit Profile';
    profileForm.reset();
    document.getElementById('profile-id').value = profile.name;
    document.getElementById('profile-name').value = profile.name;
    document.getElementById('profile-name').disabled = true;
    document.getElementById('profile-description').value = profile.description || '';
    document.getElementById('profile-device-type').value = profile.device_type || '';
    document.getElementById('profile-test-args').value = profile.test_args ? JSON.stringify(profile.test_args, null, 2) : '{}';

    const checkboxes = profileForm.querySelectorAll('input[name="profile-test"]');
    checkboxes.forEach(cb => {
        cb.checked = profile.tests && profile.tests.includes(cb.value);
    });

    // Load RAM configuration from profile test_args
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];
    
    let ramConfig = null;
    if (profile.test_args) {
        // Look for RAM config in any of the RAM test paths
        for (const path of ramTestPaths) {
            if (profile.test_args[path]) {
                ramConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    setRamConfigInForm(ramConfig);

    // Load CPU configuration from profile test_args
    const cpuTestPaths = [
        'agent.tests.visual_cpu_test.run_visual_cpu_test'
    ];
    
    let cpuConfig = null;
    if (profile.test_args) {
        // Look for CPU config in any of the CPU test paths
        for (const path of cpuTestPaths) {
            if (profile.test_args[path] && 
                (profile.test_args[path].duration_seconds || profile.test_args[path].cpu_load_intensity)) {
                cpuConfig = profile.test_args[path];
                break;
            }
        }
    }
    
    setCpuConfigInForm(cpuConfig);

    modal.style.display = 'block';
}

async function handleProfileFormSubmit(event) {
    event.preventDefault();
    const form = event.target;
    const profileId = document.getElementById('profile-id').value;
    const profileName = document.getElementById('profile-name').value;

    const selectedTests = Array.from(form.querySelectorAll('input[name="profile-test"]:checked'))
                              .map(cb => cb.value);
    
    // Validate RAM configuration if visible
    const ramConfigSection = document.getElementById('ram-config-section');
    if (ramConfigSection && ramConfigSection.style.display !== 'none') {
        const ramSizeValid = validateRamSizeInput();
        const ramDurationValid = validateRamDurationInput();
        
        if (!ramSizeValid || !ramDurationValid) {
            alert('Please correct the RAM configuration errors before saving.');
            return;
        }
    }
    
    // Validate CPU configuration if visible
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (cpuConfigSection && cpuConfigSection.style.display !== 'none') {
        const cpuDurationValid = validateCpuDuration();
        
        if (!cpuDurationValid) {
            alert('Please correct the CPU configuration errors before saving.');
            return;
        }
    }
    
    let testArgs = {};
    try {
        const testArgsRaw = document.getElementById('profile-test-args').value.trim();
        if (testArgsRaw) {
            testArgs = JSON.parse(testArgsRaw);
        }
    } catch (e) {
        alert('Test Arguments are not valid JSON. Please correct them.');
        return;
    }

    // Add RAM configuration to test_args if RAM tests are selected
    const ramConfig = getRamConfigFromForm();
    if (ramConfig) {
        const ramTestPaths = [
            'agent.tests.visual_ram_test.run_visual_ram_test',
            'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
        ];
        
        // Add RAM config to each selected RAM test
        ramTestPaths.forEach(testPath => {
            if (selectedTests.includes(testPath)) {
                testArgs[testPath] = { ...testArgs[testPath], ...ramConfig };
            }
        });
    }

    // Add CPU configuration to test_args if CPU tests are selected
    const cpuConfig = getCpuConfigFromForm();
    if (cpuConfig) {
        const cpuTestPaths = [
            'agent.tests.visual_cpu_test.run_visual_cpu_test'
        ];
        
        // Add CPU config to each selected CPU test
        cpuTestPaths.forEach(testPath => {
            if (selectedTests.includes(testPath)) {
                testArgs[testPath] = { ...testArgs[testPath], ...cpuConfig };
            }
        });
    }

    const profileData = {
        name: profileName,
        description: document.getElementById('profile-description').value,
        device_type: document.getElementById('profile-device-type').value,
        tests: selectedTests,
        test_args: testArgs
    };

    let response;
    if (profileId && profileId === profileName) {
        response = await fetch(`/api/profiles/${profileId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
    } else {
         if (!profileName.trim()) {
            alert("Profile name cannot be empty.");
            return;
        }
        response = await fetch('/api/profiles', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(profileData)
        });
    }

    if (response.ok) {
        alert(`Profile ${profileId ? 'updated' : 'created'} successfully!`);
        document.getElementById('profile-modal').style.display = 'none';
        loadProfiles();
        const currentSelectedProfile = document.getElementById('profile-select').value;
        if (currentSelectedProfile === profileName) {
            displaySelectedProfileDetails();
        }
    } else {
        const errorData = await response.json();
        alert(`Error: ${errorData.error || 'Failed to save profile.'}`);
    }
}

async function deleteSelectedProfile() {
    const profileSelect = document.getElementById('profile-select');
    if (!profileSelect) return;
    const profileName = profileSelect.value;

    if (!profileName) {
        alert('Please select a profile to delete.');
        return;
    }

    if (confirm(`Are you sure you want to delete profile "${profileName}"?`)) {
        const response = await fetch(`/api/profiles/${profileName}`, { method: 'DELETE' });
        if (response.ok) {
            alert(`Profile "${profileName}" deleted successfully.`);
            loadProfiles();
            document.getElementById('profile-details').innerHTML = '';
        } else {
            const errorData = await response.json();
            alert(`Error deleting profile: ${errorData.error || 'Failed to delete profile.'}`);
        }
    }
}

// --- Main Setup Function ---

// --- RAM Configuration UI Functions ---

function setupRamConfigurationUI() {
    const ramSizeModeRadios = document.querySelectorAll('input[name="ram-size-mode"]');
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramTestDurationInput = document.getElementById('ram-test-duration');
    const ramSizeUnit = document.getElementById('ram-size-unit');
    const ramConfigPreview = document.getElementById('ram-config-preview');

    // Add event listeners for mode switching
    ramSizeModeRadios.forEach(radio => {
        radio.addEventListener('change', handleRamSizeModeChange);
    });

    // Add event listeners for real-time validation and preview
    if (ramTestSizeInput) {
        ramTestSizeInput.addEventListener('input', handleRamSizeInputChange);
        ramTestSizeInput.addEventListener('blur', validateRamSizeInput);
    }

    if (ramTestDurationInput) {
        ramTestDurationInput.addEventListener('input', validateRamDurationInput);
        ramTestDurationInput.addEventListener('blur', validateRamDurationInput);
    }

    // Add event listener for test selection changes to show/hide RAM config
    const testCheckboxes = document.getElementById('profile-tests-checkboxes');
    if (testCheckboxes) {
        testCheckboxes.addEventListener('change', updateRamConfigVisibility);
    }
}

function handleRamSizeModeChange() {
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramSizeUnit = document.getElementById('ram-size-unit');

    if (selectedMode === 'percentage') {
        ramTestSizeInput.min = '1';
        ramTestSizeInput.max = '50';
        ramTestSizeInput.value = Math.min(parseInt(ramTestSizeInput.value) || 25, 50);
        ramSizeUnit.textContent = '%';
    } else if (selectedMode === 'absolute') {
        ramTestSizeInput.min = '1';
        ramTestSizeInput.removeAttribute('max');
        ramTestSizeInput.value = ramTestSizeInput.value || '1024';
        ramSizeUnit.textContent = 'MB';
    }

    validateRamSizeInput();
    updateRamConfigPreview();
}

function handleRamSizeInputChange() {
    validateRamSizeInput();
    updateRamConfigPreview();
}

function validateRamSizeInput() {
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const value = parseInt(ramTestSizeInput.value);

    // Remove existing error classes and messages
    ramTestSizeInput.classList.remove('error');
    const existingError = ramTestSizeInput.parentNode.querySelector('.ram-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value <= 0) {
        isValid = false;
        errorMessage = 'Please enter a positive number';
    } else if (selectedMode === 'percentage' && (value < 1 || value > 50)) {
        isValid = false;
        errorMessage = 'Percentage must be between 1% and 50%';
    } else if (selectedMode === 'absolute' && value < 1) {
        isValid = false;
        errorMessage = 'Size must be at least 1 MB';
    }

    if (!isValid) {
        ramTestSizeInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ram-config-error';
        errorDiv.textContent = errorMessage;
        ramTestSizeInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

function validateRamDurationInput() {
    const ramTestDurationInput = document.getElementById('ram-test-duration');
    const value = parseInt(ramTestDurationInput.value);

    // Remove existing error classes and messages
    ramTestDurationInput.classList.remove('error');
    const existingError = ramTestDurationInput.parentNode.querySelector('.ram-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value < 5 || value > 3600) {
        isValid = false;
        errorMessage = 'Duration must be between 5 and 3600 seconds';
    }

    if (!isValid) {
        ramTestDurationInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'ram-config-error';
        errorDiv.textContent = errorMessage;
        ramTestDurationInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

async function updateRamConfigPreview() {
    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const ramTestSizeInput = document.getElementById('ram-test-size');
    const ramConfigPreview = document.getElementById('ram-config-preview');
    const value = parseInt(ramTestSizeInput.value);

    if (!ramConfigPreview || isNaN(value)) {
        if (ramConfigPreview) ramConfigPreview.style.display = 'none';
        return;
    }

    if (selectedMode === 'percentage') {
        try {
            // Fetch current system memory info for preview calculation
            const memoryInfo = await fetchData('/api/system/memory_info');
            if (memoryInfo && memoryInfo.available_mb) {
                const calculatedMB = Math.floor(memoryInfo.available_mb * value / 100);
                ramConfigPreview.innerHTML = `
                    <strong>Preview:</strong> ${value}% of ${Math.floor(memoryInfo.available_mb)} MB available = ~${calculatedMB} MB<br>
                    <small>Actual value calculated at test runtime based on available memory</small>
                `;
                ramConfigPreview.style.display = 'block';
            } else {
                ramConfigPreview.innerHTML = `
                    <strong>Preview:</strong> ${value}% of available RAM<br>
                    <small>Actual MB value will be calculated at test runtime</small>
                `;
                ramConfigPreview.style.display = 'block';
            }
        } catch (error) {
            ramConfigPreview.innerHTML = `
                <strong>Preview:</strong> ${value}% of available RAM<br>
                <small>Actual MB value will be calculated at test runtime</small>
            `;
            ramConfigPreview.style.display = 'block';
        }
    } else {
        ramConfigPreview.innerHTML = `<strong>Preview:</strong> Fixed ${value} MB will be used for RAM test`;
        ramConfigPreview.style.display = 'block';
    }
}

function updateRamConfigVisibility() {
    const ramConfigSection = document.getElementById('ram-config-section');
    const testCheckboxes = document.querySelectorAll('input[name="profile-test"]:checked');
    
    if (!ramConfigSection) return;

    // Check if any RAM tests are selected
    const ramTestPaths = [
        'agent.tests.visual_ram_test.run_visual_ram_test',
        'agent.tests.web_visual_ram_test.run_web_visual_ram_test'
    ];

    const hasRamTests = Array.from(testCheckboxes).some(checkbox => 
        ramTestPaths.includes(checkbox.value)
    );

    if (hasRamTests) {
        ramConfigSection.style.display = 'block';
        updateRamConfigPreview();
    } else {
        ramConfigSection.style.display = 'none';
    }
}

function getRamConfigFromForm() {
    const ramConfigSection = document.getElementById('ram-config-section');
    if (!ramConfigSection || ramConfigSection.style.display === 'none') {
        return null;
    }

    const selectedMode = document.querySelector('input[name="ram-size-mode"]:checked')?.value;
    const testSize = parseInt(document.getElementById('ram-test-size').value);
    const duration = parseInt(document.getElementById('ram-test-duration').value);

    if (!selectedMode || isNaN(testSize) || isNaN(duration)) {
        return null;
    }

    return {
        test_size_mode: selectedMode,
        test_size_value: testSize,
        duration_seconds: duration
    };
}

function setRamConfigInForm(ramConfig) {
    if (!ramConfig) {
        // Set defaults
        document.querySelector('input[name="ram-size-mode"][value="percentage"]').checked = true;
        document.getElementById('ram-test-size').value = '25';
        document.getElementById('ram-test-duration').value = '30';
    } else {
        const modeRadio = document.querySelector(`input[name="ram-size-mode"][value="${ramConfig.test_size_mode}"]`);
        if (modeRadio) modeRadio.checked = true;
        
        document.getElementById('ram-test-size').value = ramConfig.test_size_value || 25;
        document.getElementById('ram-test-duration').value = ramConfig.duration_seconds || 30;
    }

    handleRamSizeModeChange();
    updateRamConfigVisibility();
}

// --- CPU Configuration UI Functions ---

function setupCpuTestConfiguration() {
    const cpuTestDurationInput = document.getElementById('cpu-test-duration');
    const cpuLoadIntensitySlider = document.getElementById('cpu-load-intensity');
    const cpuIntensityValue = document.getElementById('cpu-intensity-value');

    // Add event listeners for CPU duration input
    if (cpuTestDurationInput) {
        cpuTestDurationInput.addEventListener('input', validateCpuDuration);
        cpuTestDurationInput.addEventListener('blur', validateCpuDuration);
        cpuTestDurationInput.addEventListener('input', updateCpuConfigPreview);
    }

    // Add event listener for CPU intensity slider
    if (cpuLoadIntensitySlider && cpuIntensityValue) {
        cpuLoadIntensitySlider.addEventListener('input', function() {
            cpuIntensityValue.textContent = this.value + '%';
            updateCpuConfigPreview();
        });
    }

    // Add event listener for test selection changes to show/hide CPU config
    const testCheckboxes = document.getElementById('profile-tests-checkboxes');
    if (testCheckboxes) {
        testCheckboxes.addEventListener('change', updateCpuConfigVisibility);
    }
}

function validateCpuDuration() {
    const cpuTestDurationInput = document.getElementById('cpu-test-duration');
    const value = parseInt(cpuTestDurationInput.value);

    // Remove existing error classes and messages
    cpuTestDurationInput.classList.remove('error');
    const existingError = cpuTestDurationInput.parentNode.querySelector('.cpu-config-error');
    if (existingError) {
        existingError.remove();
    }

    let isValid = true;
    let errorMessage = '';

    if (isNaN(value) || value < 5 || value > 300) {
        isValid = false;
        errorMessage = 'Duration must be between 5 and 300 seconds';
    }

    if (!isValid) {
        cpuTestDurationInput.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'cpu-config-error';
        errorDiv.textContent = errorMessage;
        cpuTestDurationInput.parentNode.appendChild(errorDiv);
    }

    return isValid;
}

function updateCpuConfigPreview() {
    const cpuConfigPreview = document.getElementById('cpu-config-preview');
    const duration = document.getElementById('cpu-test-duration').value;
    const intensity = document.getElementById('cpu-load-intensity').value;

    if (!cpuConfigPreview) return;

    const durationValue = parseInt(duration);
    const intensityValue = parseInt(intensity);

    if (isNaN(durationValue) || isNaN(intensityValue)) {
        cpuConfigPreview.style.display = 'none';
        return;
    }

    cpuConfigPreview.innerHTML = `
        <div class="config-preview">
            <strong>Configuration Preview:</strong><br>
            Duration: ${duration} seconds<br>
            CPU Load: ${intensity}%<br>
            <em>Estimated test time: ~${duration}s</em>
        </div>
    `;
    cpuConfigPreview.style.display = 'block';
}

function updateCpuConfigVisibility() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    const testCheckboxes = document.querySelectorAll('input[name="profile-test"]:checked');
    
    if (!cpuConfigSection) return;

    // Check if any CPU tests are selected
    const cpuTestPaths = [
        'agent.tests.visual_cpu_test.run_visual_cpu_test'
    ];

    const hasCpuTests = Array.from(testCheckboxes).some(checkbox => 
        cpuTestPaths.includes(checkbox.value) || 
        checkbox.value.toLowerCase().includes('cpu')
    );

    if (hasCpuTests) {
        cpuConfigSection.style.display = 'block';
        updateCpuConfigPreview();
    } else {
        cpuConfigSection.style.display = 'none';
    }
}

function getCpuConfigFromForm() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (!cpuConfigSection || cpuConfigSection.style.display === 'none') {
        return null;
    }

    const duration = parseInt(document.getElementById('cpu-test-duration').value);
    const intensity = parseInt(document.getElementById('cpu-load-intensity').value);

    if (isNaN(duration) || isNaN(intensity)) {
        return null;
    }

    return {
        duration_seconds: duration,
        cpu_load_intensity: intensity
    };
}

function setCpuConfigInForm(cpuConfig) {
    if (!cpuConfig) {
        // Set defaults
        document.getElementById('cpu-test-duration').value = '20';
        document.getElementById('cpu-load-intensity').value = '100';
        document.getElementById('cpu-intensity-value').textContent = '100%';
    } else {
        document.getElementById('cpu-test-duration').value = cpuConfig.duration_seconds || 20;
        document.getElementById('cpu-load-intensity').value = cpuConfig.cpu_load_intensity || 100;
        document.getElementById('cpu-intensity-value').textContent = (cpuConfig.cpu_load_intensity || 100) + '%';
    }

    updateCpuConfigVisibility();
    updateCpuConfigPreview();
}x.value) || 
        checkbox.value.toLowerCase().includes('cpu')
    );

    if (hasCpuTests) {
        cpuConfigSection.style.display = 'block';
        updateCpuConfigPreview();
    } else {
        cpuConfigSection.style.display = 'none';
    }
}

function getCpuConfigFromForm() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (!cpuConfigSection || cpuConfigSection.style.display === 'none') {
        return null;
    }

    const duration = parseInt(document.getElementById('cpu-test-duration').value);
    const intensity = parseInt(document.getElementById('cpu-load-intensity').value);

    if (isNaN(duration) || isNaN(intensity)) {
        return null;
    }

    return {
        duration_seconds: duration,
        cpu_load_intensity: intensity
    };
}

function setCpuConfigInForm(cpuConfig) {
    if (!cpuConfig) {
        // Set defaults
        document.getElementById('cpu-test-duration').value = '20';
        document.getElementById('cpu-load-intensity').value = '100';
        document.getElementById('cpu-intensity-value').textContent = '100%';
    } else {
        document.getElementById('cpu-test-duration').value = cpuConfig.duration_seconds || 20;
        document.getElementById('cpu-load-intensity').value = cpuConfig.cpu_load_intensity || 100;
        document.getElementById('cpu-intensity-value').textContent = (cpuConfig.cpu_load_intensity || 100) + '%';
    }

    updateCpuConfigVisibility();
    updateCpuConfigPreview();
}x.value) || 
        checkbox.value.toLowerCase().includes('cpu')
    );

    if (hasCpuTests) {
        cpuConfigSection.style.display = 'block';
        updateCpuConfigPreview();
    } else {
        cpuConfigSection.style.display = 'none';
    }
}

function getCpuConfigFromForm() {
    const cpuConfigSection = document.getElementById('cpu-config-section');
    if (!cpuConfigSection || cpuConfigSection.style.display === 'none') {
        return null;
    }

    const duration = parseInt(document.getElementById('cpu-test-duration').value);
    const intensity = parseInt(document.getElementById('cpu-load-intensity').value);

    if (isNaN(duration) || isNaN(intensity)) {
        return null;
    }

    return {
        duration_seconds: duration,
        cpu_load_intensity: intensity
    };
}

function setCpuConfigInForm(cpuConfig) {
    if (!cpuConfig) {
        // Set defaults
        document.getElementById('cpu-test-duration').value = '20';
        document.getElementById('cpu-load-intensity').value = '100';
        document.getElementById('cpu-intensity-value').textContent = '100%';
    } else {
        document.getElementById('cpu-test-duration').value = cpuConfig.duration_seconds || 20;
        document.getElementById('cpu-load-intensity').value = cpuConfig.cpu_load_intensity || 100;
        document.getElementById('cpu-intensity-value').textContent = (cpuConfig.cpu_load_intensity || 100) + '%';
    }

    updateCpuConfigVisibility();
    updateCpuConfigPreview();
}

export function setupProfileManagement(checkRunTestButtonState) {
    // Initial load
    loadProfiles();
    setupProfileModal();

    // Button listeners
    const loadProfileDetailsBtn = document.getElementById('load-profile-details-btn');
    if (loadProfileDetailsBtn) loadProfileDetailsBtn.addEventListener('click', displaySelectedProfileDetails);

    const createProfileBtn = document.getElementById('create-profile-btn');
    if (createProfileBtn) createProfileBtn.addEventListener('click', openProfileModalForCreate);

    const editProfileBtn = document.getElementById('edit-profile-btn');
    if (editProfileBtn) editProfileBtn.addEventListener('click', openProfileModalForEdit);

    const deleteProfileBtn = document.getElementById('delete-profile-btn');
    if (deleteProfileBtn) deleteProfileBtn.addEventListener('click', deleteSelectedProfile);

    // Select dropdown listeners
    const profileSelect = document.getElementById('profile-select');
    const profileSelectQuick = document.getElementById('profile-select-quick');

    if (profileSelect) {
        profileSelect.addEventListener('change', () => {
            const selectedProfileName = profileSelect.value;
            const isProfileSelected = !!selectedProfileName;
            if(editProfileBtn) editProfileBtn.disabled = !isProfileSelected;
            if(deleteProfileBtn) deleteProfileBtn.disabled = !isProfileSelected;
            if (!isProfileSelected) {
                 document.getElementById('profile-details').innerHTML = '';
            }
            if (profileSelectQuick) profileSelectQuick.value = selectedProfileName;
            if (checkRunTestButtonState) checkRunTestButtonState();
        });
    }

    if (profileSelectQuick) {
        profileSelectQuick.addEventListener('change', () => {
            const selectedProfileName = profileSelectQuick.value;
            if (profileSelect) profileSelect.value = selectedProfileName;
            displayProfileDetailsQuick(selectedProfileName);
            if (checkRunTestButtonState) checkRunTestButtonState();
        });
    }

    // Quick profile details button
    const loadProfileDetailsQuick = document.getElementById('load-profile-details-quick');
    if (loadProfileDetailsQuick) {
        loadProfileDetailsQuick.addEventListener('click', () => {
            const selectedProfileName = profileSelectQuick ? profileSelectQuick.value : '';
            if (selectedProfileName) {
                displaySelectedProfileDetails();
                const profileSection = document.getElementById('profile-management');
                if (profileSection) profileSection.classList.add('expanded');
            } else {
                alert('Please select a profile first.');
            }
        });
    }
}
