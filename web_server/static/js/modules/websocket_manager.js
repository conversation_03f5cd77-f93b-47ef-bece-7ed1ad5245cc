/**
 * WebSocket Manager for Crucible Real-time Updates
 * Provides live test progress, hardware detection, and system status
 * Compatible with existing SSE endpoints while adding WebSocket support
 */

class WebSocketManager {
    constructor() {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.listeners = new Map();
        this.isConnected = false;
        this.heartbeatInterval = null;
        
        // Fallback to SSE if WebSocket unavailable
        this.fallbackToSSE = true;
        this.sseConnections = new Map();
    }

    /**
     * Connect to WebSocket server or fallback to SSE
     * @param {string} assetNumber - Asset being tested
     * @param {Object} options - Connection options
     */
    connect(assetNumber, options = {}) {
        this.assetNumber = assetNumber;
        
        // Try WebSocket first, fallback to existing SSE
        if (this.supportsWebSocket()) {
            this.connectWebSocket(options);
        } else {
            this.connectSSE(assetNumber);
        }
    }

    /**
     * Check if WebSocket is supported and server available
     */
    supportsWebSocket() {
        return 'WebSocket' in window;
    }

    /**
     * Connect via WebSocket (future enhancement)
     */
    connectWebSocket(options) {
        try {
            // For now, fallback to SSE since WebSocket isn't implemented in Flask app yet
            console.log('WebSocket support detected, but using SSE for compatibility');
            this.connectSSE(this.assetNumber);
        } catch (error) {
            console.warn('WebSocket connection failed, falling back to SSE:', error);
            this.connectSSE(this.assetNumber);
        }
    }

    /**
     * Connect via Server-Sent Events (existing implementation)
     */
    connectSSE(assetNumber) {
        if (!assetNumber) {
            console.error('Asset number required for SSE connection');
            return;
        }

        // Use existing SSE endpoint
        const eventSource = new EventSource(`/api/test_log/stream/${assetNumber}`);
        this.sseConnections.set(assetNumber, eventSource);

        eventSource.onopen = () => {
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.emit('connected', { type: 'sse', assetNumber });
            console.log(`SSE connected for asset ${assetNumber}`);
        };

        eventSource.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('Error parsing SSE message:', error);
            }
        };

        eventSource.onerror = (error) => {
            console.error('SSE connection error:', error);
            this.isConnected = false;
            this.emit('disconnected', { error });
            
            // Auto-reconnect logic
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                setTimeout(() => {
                    this.reconnectAttempts++;
                    this.connectSSE(assetNumber);
                }, this.reconnectDelay * this.reconnectAttempts);
            }
        };
    }

    /**
     * Handle incoming messages from SSE or WebSocket
     */
    handleMessage(data) {
        switch (data.type) {
            case 'log':
                this.emit('testLog', {
                    index: data.idx,
                    message: data.log,
                    timestamp: new Date()
                });
                break;
                
            case 'progress':
                this.emit('testProgress', {
                    progress: data.progress,
                    status: data.status,
                    testType: data.test_type
                });
                break;
                
            case 'hardware_detection':
                this.emit('hardwareDetected', data);
                break;
                
            case 'test_complete':
                this.emit('testComplete', data);
                break;
                
            default:
                this.emit('message', data);
        }
    }

    /**
     * Subscribe to events
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    /**
     * Unsubscribe from events
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * Emit events to listeners
     */
    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in ${event} listener:`, error);
                }
            });
        }
    }

    /**
     * Send message (for future WebSocket implementation)
     */
    send(message) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.warn('Cannot send message: not connected via WebSocket');
        }
    }

    /**
     * Disconnect all connections
     */
    disconnect() {
        // Close SSE connections
        this.sseConnections.forEach((eventSource, assetNumber) => {
            eventSource.close();
            console.log(`SSE disconnected for asset ${assetNumber}`);
        });
        this.sseConnections.clear();

        // Close WebSocket if exists
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }

        // Clear heartbeat
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        this.isConnected = false;
        this.emit('disconnected', { reason: 'manual' });
    }

    /**
     * Get connection status
     */
    getStatus() {
        return {
            connected: this.isConnected,
            type: this.socket ? 'websocket' : 'sse',
            assetNumber: this.assetNumber,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

/**
 * Real-time UI Update Manager
 * Handles DOM updates based on WebSocket/SSE events
 */
class RealTimeUIManager {
    constructor(wsManager) {
        this.wsManager = wsManager;
        this.setupEventListeners();
        this.logContainer = null;
        this.progressElements = new Map();
    }

    setupEventListeners() {
        this.wsManager.on('testLog', (data) => this.updateTestLog(data));
        this.wsManager.on('testProgress', (data) => this.updateTestProgress(data));
        this.wsManager.on('hardwareDetected', (data) => this.updateHardwareInfo(data));
        this.wsManager.on('testComplete', (data) => this.handleTestComplete(data));
        this.wsManager.on('connected', (data) => this.updateConnectionStatus(true));
        this.wsManager.on('disconnected', (data) => this.updateConnectionStatus(false));
    }

    /**
     * Update test log display (if log container exists)
     */
    updateTestLog(data) {
        if (!this.logContainer) {
            this.logContainer = document.getElementById('test-logs') || 
                              document.querySelector('.test-logs') ||
                              this.createLogContainer();
        }

        if (this.logContainer) {
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">${data.timestamp.toLocaleTimeString()}</span>
                <span class="log-message">${this.escapeHtml(data.message)}</span>
            `;
            
            this.logContainer.appendChild(logEntry);
            this.logContainer.scrollTop = this.logContainer.scrollHeight;
            
            // Limit log entries to prevent memory issues
            const maxEntries = 100;
            while (this.logContainer.children.length > maxEntries) {
                this.logContainer.removeChild(this.logContainer.firstChild);
            }
        }
    }

    /**
     * Update test progress indicators
     */
    updateTestProgress(data) {
        // Update progress bars
        const progressBars = document.querySelectorAll('.progress-bar');
        progressBars.forEach(bar => {
            if (data.progress && typeof data.progress === 'number') {
                bar.style.width = `${data.progress}%`;
                bar.setAttribute('aria-valuenow', data.progress);
            }
        });

        // Update status indicators
        const statusElements = document.querySelectorAll('.test-status');
        statusElements.forEach(element => {
            element.textContent = data.status || 'Running';
            element.className = `test-status status-${(data.status || 'running').toLowerCase()}`;
        });

        // Update specific test type progress
        if (data.testType) {
            const testElement = document.querySelector(`[data-test-type="${data.testType}"]`);
            if (testElement) {
                testElement.classList.add('status-running');
                if (data.status === 'complete') {
                    testElement.classList.remove('status-running');
                    testElement.classList.add('status-pass');
                }
            }
        }
    }

    /**
     * Update hardware detection info
     */
    updateHardwareInfo(data) {
        const hardwareInfo = document.getElementById('hardware-info') || 
                           document.querySelector('.hardware-info');
        
        if (hardwareInfo && data.hardware) {
            // Update system info without changing existing layout
            const infoHTML = Object.entries(data.hardware)
                .map(([key, value]) => `<span class="hw-item">${key}: ${value}</span>`)
                .join('');
            
            hardwareInfo.innerHTML = infoHTML;
        }
    }

    /**
     * Handle test completion
     */
    handleTestComplete(data) {
        // Update all running status indicators
        document.querySelectorAll('.status-running').forEach(element => {
            element.classList.remove('status-running');
            element.classList.add(data.success ? 'status-pass' : 'status-fail');
        });

        // Show completion notification (non-intrusive)
        this.showNotification(`Test completed: ${data.status}`, data.success ? 'success' : 'error');
    }

    /**
     * Update connection status indicator
     */
    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-status') || 
                         this.createConnectionIndicator();
        
        indicator.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
        indicator.title = connected ? 'Real-time updates active' : 'Connection lost';
    }

    /**
     * Create log container if it doesn't exist
     */
    createLogContainer() {
        const container = document.createElement('div');
        container.id = 'test-logs';
        container.className = 'test-logs';
        container.style.cssText = `
            max-height: 200px;
            overflow-y: auto;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: var(--space-sm);
            font-family: monospace;
            font-size: var(--font-size-sm);
        `;
        
        // Try to append to existing container
        const parent = document.querySelector('.dashboard-card') || document.body;
        parent.appendChild(container);
        
        return container;
    }

    /**
     * Create connection status indicator
     */
    createConnectionIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'connection-status';
        indicator.className = 'connection-status';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--status-pending);
            z-index: 1000;
            transition: background-color var(--transition-fast);
        `;
        
        document.body.appendChild(indicator);
        
        // Add CSS for connected/disconnected states
        const style = document.createElement('style');
        style.textContent = `
            .connection-status.connected { background: var(--status-pass); }
            .connection-status.disconnected { background: var(--status-fail); }
        `;
        document.head.appendChild(style);
        
        return indicator;
    }

    /**
     * Show non-intrusive notification
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 50px;
            right: 20px;
            padding: var(--space-md);
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            color: var(--text-primary);
            z-index: 1001;
            transform: translateX(100%);
            transition: transform var(--transition-normal);
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Auto-remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    /**
     * Escape HTML to prevent XSS
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global instances
window.CrucibleWebSocket = new WebSocketManager();
window.CrucibleRealTimeUI = new RealTimeUIManager(window.CrucibleWebSocket);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { WebSocketManager, RealTimeUIManager };
}
