// Enhanced Test Execution Monitor for Crucible
// Provides real-time test monitoring with WebSocket/SSE support, visual progress tracking,
// and improved feedback for warehouse technicians processing hardware

class EnhancedTestMonitor {
    constructor() {
        this.eventSource = null;
        this.websocket = null;
        this.currentTests = new Map();
        this.testQueue = [];
        this.assetNumber = null;
        this.operatorId = null;
        this.profileName = null;
        this.startTime = null;
        this.isRunning = false;
        this.useWebSocket = false; // Disable WS for now; backend WS endpoint not implemented
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.stats = {
            total: 0,
            completed: 0,
            passed: 0,
            failed: 0,
            skipped: 0,
            running: 0
        };
    }

    // Initialize the enhanced test monitor
    init() {
        this.setupEventListeners();
        this.createMonitorUI();
        console.log('Enhanced Test Monitor initialized');
    }

    // Create the enhanced monitor UI
    createMonitorUI() {
        // Preferred placement: right below Secure Drive Wipe section
        const driveWipeSection = document.getElementById('drive-wipe-section');
        const testStatusSection = document.getElementById('test-status-section');
        if (!driveWipeSection && !testStatusSection) return;

        // Create enhanced monitor container
        const monitorHTML = `
            <div id="enhanced-test-monitor" class="dashboard-card full-width enhanced-monitor-container">
                <!-- Header with real-time stats -->
                <div class="monitor-header">
                    <div class="monitor-title">
                        <h3>Test Execution Monitor</h3>
                        <span id="monitor-status" class="status-indicator">Idle</span>
                    </div>
                    <div class="monitor-stats">
                        <div class="stat-box">
                            <span class="stat-label">Progress</span>
                            <span class="stat-value" id="progress-percent">0%</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-label">Elapsed</span>
                            <span class="stat-value" id="elapsed-time">00:00</span>
                        </div>
                        <div class="stat-box">
                            <span class="stat-label">ETA</span>
                            <span class="stat-value" id="eta-time">--:--</span>
                        </div>
                    </div>
                </div>

                <!-- Progress bar -->
                <div class="monitor-progress">
                    <div class="progress-bar-container">
                        <div id="overall-progress-bar" class="progress-bar">
                            <div class="progress-fill" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="progress-details">
                        <span id="progress-text">0 of 0 tests completed</span>
                    </div>
                </div>

                <!-- Test cards grid -->
                <div id="test-cards-container" class="test-cards-grid">
                    <!-- Individual test cards will be added here -->
                </div>

                <!-- Live log stream -->
                <div class="monitor-logs">
                    <div class="logs-header">
                        <h4>Live Output</h4>
                        <div class="log-controls">
                            <button id="toggle-verbose" class="btn-compact">Verbose</button>
                            <button id="clear-logs" class="btn-compact">Clear</button>
                            <button id="export-logs" class="btn-compact">Export</button>
                        </div>
                    </div>
                    <div id="monitor-log-stream" class="log-stream">
                        <!-- Real-time logs will appear here -->
                    </div>
                </div>

                <!-- Control buttons -->
                <div class="monitor-controls">
                    <button id="pause-tests" class="btn-warning" disabled>Pause</button>
                    <button id="stop-tests" class="btn-danger" disabled>Stop All</button>
                    <button id="retry-failed" class="btn-secondary" disabled>Retry Failed</button>
                </div>
            </div>
        `;

        // Insert the enhanced monitor
        const container = document.createElement('div');
        container.innerHTML = monitorHTML;
        const monitorEl = container.firstElementChild;

        if (driveWipeSection && driveWipeSection.parentElement) {
            // Place right after the drive wipe section
            const parent = driveWipeSection.parentElement;
            parent.insertBefore(monitorEl, driveWipeSection.nextSibling);
        } else if (testStatusSection) {
            // Fallback: place above the orchestrator log within test status section
            const logArea = document.getElementById('test-status-log');
            if (logArea && logArea.parentElement === testStatusSection) {
                testStatusSection.insertBefore(monitorEl, logArea);
            } else {
                testStatusSection.appendChild(monitorEl);
            }
        }

        // Hide sections we don't want visible right now
        const visualTests = document.getElementById('visual-tests');
        if (visualTests) visualTests.style.display = 'none';
        const resultsViewer = document.getElementById('results-viewer');
        if (resultsViewer) resultsViewer.style.display = 'none';
    }

    // Setup event listeners
    setupEventListeners() {
        // Monitor control buttons
        document.addEventListener('click', (e) => {
            if (e.target.id === 'pause-tests') this.pauseTests();
            if (e.target.id === 'stop-tests') this.stopTests();
            if (e.target.id === 'retry-failed') this.retryFailedTests();
            if (e.target.id === 'toggle-verbose') this.toggleVerboseMode();
            if (e.target.id === 'clear-logs') this.clearLogs();
            if (e.target.id === 'export-logs') this.exportLogs();
        });

        // Listen for test start events from existing system
        document.addEventListener('testExecutionStarted', (e) => {
            this.startMonitoring(e.detail);
        });
    }

    // Start monitoring test execution
    async startMonitoring(config) {
        this.assetNumber = config.assetNumber;
        this.operatorId = config.operatorId;
        this.profileName = config.profileName;
        this.isRunning = true;
        this.startTime = Date.now();
        this.stats = { total: 0, completed: 0, passed: 0, failed: 0, skipped: 0, running: 0 };
        
        // Update UI
        this.updateStatus('Running');
        this.enableControls(true);
        
        // Clear previous test cards
        const container = document.getElementById('test-cards-container');
        if (container) container.innerHTML = '';
        
        // Connect to real-time updates
        if (this.useWebSocket) {
            this.connectWebSocket();
        } else {
            this.connectEventSource();
        }
        
        // Start elapsed time counter
        this.startElapsedTimer();
        
        // Log start
        this.addLog('info', `Starting test execution for Asset: ${this.assetNumber}`);
        this.addLog('info', `Profile: ${this.profileName} | Operator: ${this.operatorId}`);
    }

    // Connect WebSocket for real-time updates
    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws/test_monitor/${this.assetNumber}`;
        
        try {
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = () => {
                console.log('WebSocket connected for test monitoring');
                this.reconnectAttempts = 0;
                this.addLog('success', 'Real-time connection established');
            };
            
            this.websocket.onmessage = (event) => {
                this.handleRealtimeUpdate(JSON.parse(event.data));
            };
            
            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.addLog('error', 'Connection error - falling back to SSE');
                this.connectEventSource();
            };
            
            this.websocket.onclose = () => {
                if (this.isRunning && this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    setTimeout(() => this.connectWebSocket(), 2000);
                }
            };
        } catch (error) {
            console.error('Failed to create WebSocket:', error);
            this.connectEventSource();
        }
    }

    // Connect EventSource (SSE) as fallback
    connectEventSource() {
        // Use the existing backend SSE endpoint for logs and visual progress
        const sseUrl = `/api/test_log/stream/${this.assetNumber}`;
        
        try {
            this.eventSource = new EventSource(sseUrl);
            
            this.eventSource.onopen = () => {
                console.log('EventSource connected for test monitoring');
                this.addLog('info', 'Streaming connection established (SSE)');
            };
            
            this.eventSource.onmessage = (event) => {
                try {
                    const payload = JSON.parse(event.data);
                    this.handleRealtimeUpdate(payload);
                } catch (e) {
                    console.warn('Non-JSON SSE message:', event.data);
                }
            };
            
            this.eventSource.onerror = (error) => {
                console.error('EventSource error:', error);
                if (this.eventSource.readyState === EventSource.CLOSED) {
                    this.addLog('warning', 'Stream connection lost - attempting reconnect');
                    setTimeout(() => this.connectEventSource(), 3000);
                }
            };
        } catch (error) {
            console.error('Failed to create EventSource:', error);
            this.addLog('error', 'Failed to establish streaming connection');
        }
    }

    // Handle real-time updates
    handleRealtimeUpdate(data) {
        switch (data.type) {
            case 'test_started':
                this.onTestStarted(data);
                break;
            case 'test_progress':
                this.onTestProgress(data);
                break;
            case 'test_completed':
                this.onTestCompleted(data);
                break;
            case 'test_log':
                this.onTestLog(data);
                break;
            case 'execution_complete':
                this.onExecutionComplete(data);
                break;
            case 'error':
                this.onError(data);
                break;
            // Backend /api/test_log/stream emits simple entries:
            // { type: 'log', log: '[INFO] message' }
            // { type: 'progress', progress: { overall_progress, operation_text, ... }, status, test_type }
            case 'log': {
                const log = data.log || '';
                let level = 'info';
                if (log.startsWith('[ERROR]')) level = 'error';
                else if (log.startsWith('[WARNING]')) level = 'warning';
                else if (log.startsWith('[SUCCESS]')) level = 'success';
                this.addLog(level, log.replace(/^\[[A-Z]+\]\s*/, ''));
                // Heuristic: some backends only send a log line on completion
                // Detect common completion phrases and finalize the monitor
                const normalized = log.toLowerCase();
                const completionHints = [
                    'all tests completed',
                    'test sequence finished',
                    'test execution complete',
                    'execution complete'
                ];
                if (completionHints.some(h => normalized.includes(h))) {
                    try {
                        this.onExecutionComplete({ total_duration: Date.now() - (this.startTime || Date.now()), summary: {} });
                    } catch (_) {
                        // no-op
                    }
                }
                break;
            }
            case 'progress': {
                const prog = data.progress || {};
                const percent = Math.round(prog.overall_progress || 0);
                // Update overall UI progress and text if available
                const progressBar = document.querySelector('#overall-progress-bar .progress-fill');
                if (progressBar) progressBar.style.width = `${percent}%`;
                const progressPercent = document.getElementById('progress-percent');
                if (progressPercent) progressPercent.textContent = `${percent}%`;
                const progressText = document.getElementById('progress-text');
                if (progressText && typeof prog.cycles === 'number') {
                    progressText.textContent = `Cycles: ${prog.cycles} | Errors: ${prog.errors || 0}`;
                }
                if (prog.operation_text) this.addLog('info', prog.operation_text);
                break;
            }
        }
    }

    // Handle test started event
    onTestStarted(data) {
        const { test_name, test_type, estimated_duration } = data;
        
        // Create test card
        const card = this.createTestCard(test_name, test_type);
        document.getElementById('test-cards-container').appendChild(card);
        
        // Track test
        this.currentTests.set(test_name, {
            status: 'running',
            startTime: Date.now(),
            estimatedDuration: estimated_duration || 60000,
            progress: 0
        });
        
        this.stats.running++;
        this.stats.total++;
        this.updateProgress();
        
        this.addLog('info', `Started: ${test_name}`);
    }

    // Handle test progress update
    onTestProgress(data) {
        const { test_name, progress, message } = data;
        const test = this.currentTests.get(test_name);
        
        if (test) {
            test.progress = progress;
            this.updateTestCard(test_name, 'running', progress, message);
        }
    }

    // Handle test completed event
    onTestCompleted(data) {
        const { test_name, status, duration, message, details } = data;
        const test = this.currentTests.get(test_name);
        
        if (test) {
            test.status = status;
            test.duration = duration;
            test.endTime = Date.now();
            
            // Update stats
            this.stats.running--;
            this.stats.completed++;
            if (status === 'passed') this.stats.passed++;
            else if (status === 'failed') this.stats.failed++;
            else if (status === 'skipped') this.stats.skipped++;
            
            // Update UI
            this.updateTestCard(test_name, status, 100, message);
            this.updateProgress();
            
            // Log result
            const statusColor = status === 'passed' ? 'success' : status === 'failed' ? 'error' : 'warning';
            this.addLog(statusColor, `${test_name}: ${status.toUpperCase()} (${(duration/1000).toFixed(1)}s)`);
            
            if (details) {
                this.addLog('info', `  Details: ${details}`);
            }
        }
    }

    // Handle test log message
    onTestLog(data) {
        const { level, message, test_name } = data;
        const prefix = test_name ? `[${test_name}] ` : '';
        this.addLog(level, prefix + message);
    }

    // Handle execution complete
    onExecutionComplete(data) {
        const { total_duration, summary } = data;
        
        this.isRunning = false;
        this.updateStatus('Complete');
        this.enableControls(false);
        
        // Stop connections
        this.disconnect();
        
        // Show summary
        this.addLog('success', '═══════════════════════════════════════');
        this.addLog('success', 'Test Execution Complete!');
        this.addLog('info', `Total Duration: ${(total_duration/1000).toFixed(1)}s`);
        this.addLog('info', `Passed: ${this.stats.passed}/${this.stats.total}`);
        if (this.stats.failed > 0) {
            this.addLog('error', `Failed: ${this.stats.failed}`);
        }
        if (this.stats.skipped > 0) {
            this.addLog('warning', `Skipped: ${this.stats.skipped}`);
        }
        this.addLog('success', '═══════════════════════════════════════');
        
        // Enable retry button if there were failures
        if (this.stats.failed > 0) {
            const retryBtn = document.getElementById('retry-failed');
            if (retryBtn) retryBtn.disabled = false;
        }
    }

    // Handle error
    onError(data) {
        const { message, critical } = data;
        this.addLog('error', `ERROR: ${message}`);
        
        if (critical) {
            this.stopTests();
        }
    }

    // Create test card UI element
    createTestCard(testName, testType) {
        const card = document.createElement('div');
        card.className = 'test-card';
        card.id = `test-card-${testName.replace(/\s+/g, '-')}`;
        card.innerHTML = `
            <div class="test-card-header">
                <span class="test-name">${testName}</span>
                <span class="test-type">${testType || 'Standard'}</span>
            </div>
            <div class="test-card-body">
                <div class="test-status-icon">
                    <div class="spinner-small"></div>
                </div>
                <div class="test-progress">
                    <div class="test-progress-bar">
                        <div class="test-progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="test-progress-text">Starting...</span>
                </div>
            </div>
            <div class="test-card-footer">
                <span class="test-duration">--:--</span>
                <button class="btn-compact test-action" data-test="${testName}" data-action="skip">Skip</button>
            </div>
        `;
        
        // Add event listener for skip button
        card.querySelector('.test-action').addEventListener('click', (e) => {
            this.skipTest(e.target.dataset.test);
        });
        
        return card;
    }

    // Update test card
    updateTestCard(testName, status, progress, message) {
        const cardId = `test-card-${testName.replace(/\s+/g, '-')}`;
        const card = document.getElementById(cardId);
        if (!card) return;
        
        const progressBar = card.querySelector('.test-progress-fill');
        const progressText = card.querySelector('.test-progress-text');
        const statusIcon = card.querySelector('.test-status-icon');
        const duration = card.querySelector('.test-duration');
        
        // Update progress
        if (progressBar) progressBar.style.width = `${progress}%`;
        if (progressText) progressText.textContent = message || `${progress}%`;
        
        // Update status icon
        if (statusIcon) {
            if (status === 'running') {
                statusIcon.innerHTML = '<div class="spinner-small"></div>';
            } else if (status === 'passed') {
                statusIcon.innerHTML = '<span class="icon-check">✓</span>';
                card.classList.add('test-passed');
            } else if (status === 'failed') {
                statusIcon.innerHTML = '<span class="icon-cross">✗</span>';
                card.classList.add('test-failed');
            } else if (status === 'skipped') {
                statusIcon.innerHTML = '<span class="icon-skip">⊘</span>';
                card.classList.add('test-skipped');
            }
        }
        
        // Update duration
        const test = this.currentTests.get(testName);
        if (test && duration) {
            const elapsed = (Date.now() - test.startTime) / 1000;
            const minutes = Math.floor(elapsed / 60);
            const seconds = Math.floor(elapsed % 60);
            duration.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // Update overall progress
    updateProgress() {
        const percent = this.stats.total > 0 ? 
            Math.round((this.stats.completed / this.stats.total) * 100) : 0;
        
        // Update progress bar
        const progressBar = document.querySelector('#overall-progress-bar .progress-fill');
        if (progressBar) progressBar.style.width = `${percent}%`;
        
        // Update progress text
        const progressText = document.getElementById('progress-text');
        if (progressText) {
            progressText.textContent = `${this.stats.completed} of ${this.stats.total} tests completed`;
        }
        
        // Update progress percent
        const progressPercent = document.getElementById('progress-percent');
        if (progressPercent) progressPercent.textContent = `${percent}%`;
        
        // Update ETA
        this.updateETA();
    }

    // Update ETA calculation
    updateETA() {
        if (this.stats.completed === 0 || this.stats.completed === this.stats.total) {
            document.getElementById('eta-time').textContent = '--:--';
            return;
        }
        
        const elapsed = Date.now() - this.startTime;
        const avgTimePerTest = elapsed / this.stats.completed;
        const remainingTests = this.stats.total - this.stats.completed;
        const eta = avgTimePerTest * remainingTests;
        
        const minutes = Math.floor(eta / 60000);
        const seconds = Math.floor((eta % 60000) / 1000);
        
        document.getElementById('eta-time').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Start elapsed timer
    startElapsedTimer() {
        const updateElapsed = () => {
            if (!this.isRunning) return;
            
            const elapsed = Date.now() - this.startTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            const elapsedElement = document.getElementById('elapsed-time');
            if (elapsedElement) {
                elapsedElement.textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
            
            requestAnimationFrame(updateElapsed);
        };
        
        requestAnimationFrame(updateElapsed);
    }

    // Add log message
    addLog(level, message) {
        const logStream = document.getElementById('monitor-log-stream');
        if (!logStream) return;
        
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${level}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-time">${timestamp}</span>
            <span class="log-message">${this.escapeHtml(message)}</span>
        `;
        
        logStream.appendChild(logEntry);
        logStream.scrollTop = logStream.scrollHeight;
        
        // Limit log entries to prevent memory issues
        while (logStream.children.length > 500) {
            logStream.removeChild(logStream.firstChild);
        }
    }

    // Update status indicator
    updateStatus(status) {
        const statusElement = document.getElementById('monitor-status');
        if (statusElement) {
            statusElement.textContent = status;
            statusElement.className = `status-indicator status-${status.toLowerCase()}`;
        }
    }

    // Enable/disable controls
    enableControls(running) {
        const pauseBtn = document.getElementById('pause-tests');
        const stopBtn = document.getElementById('stop-tests');
        const retryBtn = document.getElementById('retry-failed');
        
        if (pauseBtn) pauseBtn.disabled = !running;
        if (stopBtn) stopBtn.disabled = !running;
        if (retryBtn) retryBtn.disabled = running;
    }

    // Pause tests
    async pauseTests() {
        try {
            const response = await fetch(`/api/tests/pause/${this.assetNumber}`, { method: 'POST' });
            if (response.ok) {
                this.addLog('warning', 'Tests paused');
                this.updateStatus('Paused');
            }
        } catch (error) {
            console.error('Failed to pause tests:', error);
        }
    }

    // Stop all tests
    async stopTests() {
        if (!confirm('Are you sure you want to stop all running tests?')) return;
        
        try {
            const response = await fetch(`/api/tests/stop/${this.assetNumber}`, { method: 'POST' });
            if (response.ok) {
                this.addLog('error', 'Tests stopped by user');
                this.isRunning = false;
                this.updateStatus('Stopped');
                this.disconnect();
                this.enableControls(false);
            }
        } catch (error) {
            console.error('Failed to stop tests:', error);
        }
    }

    // Skip specific test
    async skipTest(testName) {
        try {
            const response = await fetch(`/api/tests/skip/${this.assetNumber}/${testName}`, { method: 'POST' });
            if (response.ok) {
                this.addLog('warning', `Skipped: ${testName}`);
                this.onTestCompleted({
                    test_name: testName,
                    status: 'skipped',
                    duration: 0,
                    message: 'Skipped by user'
                });
            }
        } catch (error) {
            console.error('Failed to skip test:', error);
        }
    }

    // Retry failed tests
    async retryFailedTests() {
        const failedTests = Array.from(this.currentTests.entries())
            .filter(([name, test]) => test.status === 'failed')
            .map(([name]) => name);
        
        if (failedTests.length === 0) {
            this.addLog('info', 'No failed tests to retry');
            return;
        }
        
        this.addLog('info', `Retrying ${failedTests.length} failed test(s)`);
        
        // Reset failed test cards
        failedTests.forEach(testName => {
            const card = document.getElementById(`test-card-${testName.replace(/\s+/g, '-')}`);
            if (card) {
                card.classList.remove('test-failed');
                this.updateTestCard(testName, 'running', 0, 'Retrying...');
            }
        });
        
        // Send retry request
        try {
            const response = await fetch('/api/tests/retry', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    asset_number: this.assetNumber,
                    tests: failedTests
                })
            });
            
            if (response.ok) {
                this.isRunning = true;
                this.updateStatus('Running');
                this.enableControls(true);
                this.connectWebSocket();
            }
        } catch (error) {
            console.error('Failed to retry tests:', error);
            this.addLog('error', 'Failed to retry tests');
        }
    }

    // Toggle verbose mode
    toggleVerboseMode() {
        const btn = document.getElementById('toggle-verbose');
        const logStream = document.getElementById('monitor-log-stream');
        
        if (logStream.classList.contains('verbose')) {
            logStream.classList.remove('verbose');
            btn.textContent = 'Verbose';
        } else {
            logStream.classList.add('verbose');
            btn.textContent = 'Simple';
        }
    }

    // Clear logs
    clearLogs() {
        const logStream = document.getElementById('monitor-log-stream');
        if (logStream) logStream.innerHTML = '';
    }

    // Export logs
    exportLogs() {
        const logStream = document.getElementById('monitor-log-stream');
        if (!logStream) return;
        
        const logs = Array.from(logStream.children).map(entry => {
            const time = entry.querySelector('.log-time')?.textContent || '';
            const message = entry.querySelector('.log-message')?.textContent || '';
            return `${time} ${message}`;
        }).join('\n');
        
        const blob = new Blob([logs], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `test_logs_${this.assetNumber}_${Date.now()}.txt`;
        a.click();
        URL.revokeObjectURL(url);
        
        this.addLog('success', 'Logs exported');
    }

    // Disconnect all connections
    disconnect() {
        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
    }

    // Utility: Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Create and export singleton instance
const enhancedTestMonitor = new EnhancedTestMonitor();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => enhancedTestMonitor.init());
} else {
    enhancedTestMonitor.init();
}

// Export for use in other modules
window.enhancedTestMonitor = enhancedTestMonitor;
