// touch_test.js – redesigned client-side touch screen test

const canvas = document.getElementById('touchCanvas');
const ctx = canvas.getContext('2d');
const instructions = document.getElementById('instructionsHeader');
const infoTargetsHit = document.getElementById('infoTargetsHit');
const infoMultiTouch = document.getElementById('infoMultiTouch');
const infoPinch = document.getElementById('infoPinch');
const infoZoom = document.getElementById('infoZoom');
const infoTouchPoints = document.getElementById('infoTouchPoints');
const completeBtn = document.getElementById('completeTestBtn');
const forceBtn = document.getElementById('forceCompleteBtn');

// Session
let sessionId = null;

// Phases
const PHASES = { TOUCH: 0, MULTI: 1, PINCH: 2 };
let phase = PHASES.TOUCH;

// Targets
const NUM_TARGETS = 5;
let targets = [];
let hitCount = 0;

// Pinch/zoom
let initialPinchDist = null;
let gestureDetected = false;
let feedbackCircle = { x: 0, y: 0, baseRadius: 0, radius: 0, color: 'cyan', visible: false };

function resizeCanvas() {
    const hdr = instructions.offsetHeight + 20;
    const ctrl = document.querySelector('.touch-test-controls').offsetHeight + 10;
    const info = document.querySelector('.touch-test-info-bar').offsetHeight + 10;
    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight - hdr - ctrl - info;
    canvas.style.width = `${canvas.width}px`;
    canvas.style.height = `${canvas.height}px`;
    defineTargets();
    feedbackCircle.x = canvas.width / 2;
    feedbackCircle.y = canvas.height / 2;
    feedbackCircle.baseRadius = Math.min(canvas.width, canvas.height) * 0.1;
    feedbackCircle.radius = feedbackCircle.baseRadius;
    draw();
    updateUI();
}

function defineTargets() {
    targets = [];
    const W = canvas.width;
    const H = canvas.height;
    const r = Math.min(W, H) * 0.08;
    const pos = [
        { x: W * 0.2, y: H * 0.2 },
        { x: W * 0.8, y: H * 0.2 },
        { x: W * 0.5, y: H * 0.5 },
        { x: W * 0.2, y: H * 0.8 },
        { x: W * 0.8, y: H * 0.8 }
    ];
    for (let i = 0; i < NUM_TARGETS; i++) {
        targets.push({ x: pos[i].x, y: pos[i].y, r, hit: false });
    }
}

function draw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.font = `${Math.min(canvas.width, canvas.height) * 0.03}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    // draw targets
    targets.forEach((t, i) => {
        ctx.beginPath();
        ctx.arc(t.x, t.y, t.r, 0, 2 * Math.PI);
        ctx.fillStyle = t.hit ? 'green' : 'red';
        ctx.fill();
        ctx.strokeStyle = 'white';
        ctx.lineWidth = 2;
        ctx.stroke();
        ctx.fillStyle = 'white';
        ctx.fillText((i+1).toString(), t.x, t.y);
    });
    // feedback circle
    if (feedbackCircle.visible) {
        ctx.beginPath();
        ctx.arc(feedbackCircle.x, feedbackCircle.y, feedbackCircle.radius, 0, 2 * Math.PI);
        ctx.strokeStyle = feedbackCircle.color;
        ctx.lineWidth = 3;
        ctx.stroke();
    }
}

function updateUI() {
    infoTargetsHit.textContent = `Targets: ${hitCount}/${NUM_TARGETS}`;
    infoTargetsHit.className = hitCount === NUM_TARGETS ? 'info-item pass' : (phase === PHASES.TOUCH ? 'info-item active' : 'info-item neutral');

    infoMultiTouch.textContent = `Multi-touch: ${phase > PHASES.TOUCH ? 'Yes' : 'No'}`;
    infoMultiTouch.className = phase > PHASES.TOUCH ? 'info-item pass' : (phase === PHASES.MULTI ? 'info-item active' : 'info-item neutral');

    infoPinch.textContent = `Gesture: ${gestureDetected ? 'Yes' : 'No'}`;
    infoPinch.className = gestureDetected ? 'info-item pass' : (phase === PHASES.PINCH ? 'info-item active' : 'info-item neutral');

    infoZoom.textContent = `Zoom: ${feedbackCircle.radius > feedbackCircle.baseRadius ? 'Out' : (feedbackCircle.radius < feedbackCircle.baseRadius ? 'In' : 'No')}`;
    infoZoom.style.display = 'inline';
    infoZoom.className = gestureDetected ? 'info-item pass' : 'infoZoom.className';

    infoTouchPoints.textContent = `Touches: ${activeTouchCount}`;

    completeBtn.disabled = !gestureDetected;
}

let activeTouchCount = 0;
function handleTouchStart(e) {
    e.preventDefault();
    const touches = e.touches;
    activeTouchCount = touches.length;
    updateUI();
    if (phase === PHASES.TOUCH) {
        Array.from(e.changedTouches).forEach(t => {
            const x = t.clientX - canvas.offsetLeft;
            const y = t.clientY - canvas.offsetTop;
            targets.forEach(tar => {
                if (!tar.hit) {
                    const d = Math.hypot(x - tar.x, y - tar.y);
                    if (d <= tar.r) {
                        tar.hit = true;
                        hitCount++;
                    }
                }
            });
        });
        draw();
        if (hitCount === NUM_TARGETS) {
            phase = PHASES.MULTI;
            sendEvent({ action: 'phase_complete', phase: 'touch' });
        }
    }
    if (phase === PHASES.MULTI && touches.length >= 2) {
        phase = PHASES.PINCH;
        sendEvent({ action: 'phase_complete', phase: 'multi_touch' });
    }
    if (phase === PHASES.PINCH && touches.length >= 2 && initialPinchDist === null) {
        initialPinchDist = Math.hypot(
            touches[0].clientX - touches[1].clientX,
            touches[0].clientY - touches[1].clientY
        );
        feedbackCircle.visible = true;
    }
    updateUI();
}

function handleTouchMove(e) {
    e.preventDefault();
    const touches = e.touches;
    activeTouchCount = touches.length;
    if (phase === PHASES.PINCH && touches.length >= 2 && initialPinchDist) {
        const newDist = Math.hypot(
            touches[0].clientX - touches[1].clientX,
            touches[0].clientY - touches[1].clientY
        );
        const scale = newDist / initialPinchDist;
        feedbackCircle.radius = feedbackCircle.baseRadius * scale;
        if (Math.abs(scale - 1) > 0.2 && !gestureDetected) {
            gestureDetected = true;
            sendEvent({ action: 'phase_complete', phase: 'pinch' });
        }
        draw();
    }
    updateUI();
}

function handleTouchEnd(e) {
    e.preventDefault();
    activeTouchCount = e.touches.length;
    if (phase === PHASES.PINCH && activeTouchCount < 2) {
        // reset pinch base if still in pinch phase
        if (!gestureDetected) initialPinchDist = null;
    }
    updateUI();
}

function sendEvent(data) {
    if (!sessionId) return;
    fetch(`/api/visual_test/input/${sessionId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    }).catch(console.error);
}

async function initTest() {
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    // start session
    const params = new URLSearchParams(window.location.search);
    const asset = params.get('asset_number') || 'TOUCHTEST';
    try {
        const resp = await fetch('/api/visual_test/start', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ test_type: 'touch', asset_number: asset, test_params: {} })
        });
        const result = await resp.json();
        sessionId = result.session_id;
        sendEvent({ action: 'session_started' });
    } catch (err) {
        instructions.textContent = `Error starting test: ${err}`;
        return;
    }
    canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
    canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
    canvas.addEventListener('touchend', handleTouchEnd, { passive: false });
    canvas.addEventListener('touchcancel', handleTouchEnd, { passive: false });
    completeBtn.addEventListener('click', () => completeTest(false));
    forceBtn.addEventListener('click', () => completeTest(true));
}

async function completeTest(forced) {
    canvas.removeEventListener('touchstart', handleTouchStart);
    canvas.removeEventListener('touchmove', handleTouchMove);
    canvas.removeEventListener('touchend', handleTouchEnd);
    canvas.removeEventListener('touchcancel', handleTouchEnd);
    completeBtn.disabled = true;
    forceBtn.disabled = true;
    const body = { test_complete: true, force_complete: forced, progress: { targets_hit: hitCount, multi_touch: phase > PHASES.TOUCH, pinch: gestureDetected } };
    sendEvent(body);
    try {
        const resp = await fetch(`/api/visual_test/stop/${sessionId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(body)
        });
        const res = await resp.json();
        const msg = res.status.toUpperCase();
        instructions.textContent = `Test ${msg}. Notes: ${res.notes || ''}`;
    } catch (err) {
        instructions.textContent = `Error completing test: ${err}`;
    }
    setTimeout(closeWindow, 5000);
}

function closeWindow() {
    if (window.opener && !window.opener.closed) window.close();
    else window.location.href = '/';
}

window.addEventListener('load', initTest);
