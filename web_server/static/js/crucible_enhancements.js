/**
 * Crucible UI/UX Enhancements Integration
 * Loads all enhancement modules for existing dashboard
 * Preserves index.html design while adding real-time features
 */

(function() {
    'use strict';

    // Configuration
    const ENHANCEMENT_CONFIG = {
        loadTimeout: 10000, // 10 seconds
        retryAttempts: 3,
        modules: [
            '/static/css/variables.css',
            '/static/js/modules/hardware_detector.js',
            '/static/js/modules/websocket_manager.js',
            '/static/js/modules/dashboard_enhancements.js'
        ]
    };

    /**
     * Load CSS file dynamically
     */
    function loadCSS(href) {
        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            link.onload = resolve;
            link.onerror = reject;
            document.head.appendChild(link);
        });
    }

    /**
     * Load JavaScript file dynamically
     */
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Load enhancement modules
     */
    async function loadEnhancements() {
        console.log('Loading Crucible enhancements...');
        
        try {
            // Load CSS first
            const cssModules = ENHANCEMENT_CONFIG.modules.filter(m => m.endsWith('.css'));
            for (const css of cssModules) {
                await loadCSS(css);
            }

            // Load JavaScript modules
            const jsModules = ENHANCEMENT_CONFIG.modules.filter(m => m.endsWith('.js'));
            for (const js of jsModules) {
                await loadScript(js);
            }

            console.log('Crucible enhancements loaded successfully');
            
            // Add enhancement indicator
            addEnhancementIndicator();
            
        } catch (error) {
            console.warn('Some enhancements failed to load:', error);
            // Continue without enhancements - don't break existing functionality
        }
    }

    /**
     * Add subtle indicator that enhancements are active
     */
    function addEnhancementIndicator() {
        // Add a small, unobtrusive indicator
        const indicator = document.createElement('div');
        indicator.id = 'enhancement-status';
        indicator.style.cssText = `
            position: fixed;
            bottom: 10px;
            left: 10px;
            width: 8px;
            height: 8px;
            background: var(--status-pass, #4CAF50);
            border-radius: 50%;
            z-index: 999;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        `;
        indicator.title = 'Crucible enhancements active';
        
        // Fade out after 3 seconds
        document.body.appendChild(indicator);
        setTimeout(() => {
            indicator.style.opacity = '0.3';
        }, 3000);
    }

    /**
     * Check if we're on the main dashboard
     */
    function isMainDashboard() {
        return window.location.pathname === '/' || 
               window.location.pathname === '/index.html' ||
               document.querySelector('#asset_number') !== null;
    }

    /**
     * Initialize enhancements
     */
    function initialize() {
        // Only load on main dashboard to preserve existing functionality
        if (!isMainDashboard()) {
            return;
        }

        // Load enhancements when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', loadEnhancements);
        } else {
            loadEnhancements();
        }
    }

    // Start initialization
    initialize();

})();
