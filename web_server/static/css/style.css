/* Dark Theme Nexus Inspired */
:root {
    --bg-dark: #2c2c2c; /* Dark grey */
    --bg-light: #383838; /* Slightly lighter grey for elements */
    --text-light: #e0e0e0; /* Light grey for text */
    --text-dark: #1a1a1a; /* For light backgrounds if any */
    --primary: #007bff; /* Blue - common primary color */
    --accent: #ffc107;  /* Amber/Yellow - common accent */
    --success: #28a745; /* Green */
    --error: #dc3545;   /* Red */
    --button-bg: var(--primary);
    --button-text: #ffffff;
    --button-hover-bg: #0056b3;
    --input-bg: #4f4f4f;
    --input-border: #666;
    --input-text: var(--text-light);
    --link-color: var(--primary);
    --link-hover-color: var(--accent);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    background-color: var(--bg-dark);
    color: var(--text-light);
    font-size: 16px; /* Slightly larger base for "large font" feel */
    line-height: 1.6;
}

header {
    background-color: var(--bg-light); /* Darker header */
    color: var(--text-light);
    padding: 1em 0;
    text-align: center;
    border-bottom: 2px solid var(--primary);
}

header h1 {
    margin: 0;
    font-size: 1.8em; /* Larger header text */
}

main {
    padding: 1.5em; /* Increased padding */
    display: flex;
    flex-direction: column;
    gap: 1.5em; /* Increased gap */
}

section {
    border: 1px solid var(--input-border);
    padding: 1.5em; /* Increased padding */
    background-color: var(--bg-light);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    border-radius: 8px; /* Rounded corners for sections */
}

section h2 {
    color: var(--primary);
    margin-top: 0;
    border-bottom: 1px solid var(--input-border);
    padding-bottom: 0.5em;
}

footer {
    text-align: center;
    padding: 1em 0;
    background-color: var(--bg-light);
    color: var(--text-light);
    width: 100%;
    margin-top: 2em;
    border-top: 1px solid var(--input-border);
}

/* Input and Button styling */
input[type="text"],
textarea,
select {
    width: calc(100% - 24px); /* Adjusted for padding */
    padding: 12px; /* Increased padding */
    margin-bottom: 12px; /* Increased margin */
    border: 1px solid var(--input-border);
    border-radius: 4px;
    box-sizing: border-box;
    background-color: var(--input-bg);
    color: var(--input-text);
    font-size: 1em;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-focus, rgba(0, 123, 255, 0.25));
}


button {
    background-color: var(--button-bg);
    color: var(--button-text);
    padding: 12px 18px; /* Increased padding */
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1em;
    transition: background-color 0.2s ease-in-out;
}

button:hover {
    background-color: var(--button-hover-bg);
}

button:disabled {
    background-color: #555; /* Darker disabled button */
    color: #888;
    cursor: not-allowed;
}

label {
    display: block;
    margin-bottom: 8px; /* Increased margin */
    font-weight: bold;
    color: var(--text-light);
}

#system-info ul, #results-list ul {
    list-style: none;
    padding-left: 0;
}
#system-info ul li, #results-list ul li {
    padding: 0.3em 0;
}
#results-list ul li a {
    color: var(--link-color);
    text-decoration: none;
}
#results-list ul li a:hover {
    color: var(--link-hover-color);
    text-decoration: underline;
}


/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000; /* Ensure modal is on top */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.7); /* Darker overlay */
    padding-top: 50px;
}

.modal-content {
    background-color: var(--bg-light); /* Dark modal content */
    color: var(--text-light);
    margin: 5% auto;
    padding: 25px; /* Increased padding */
    border: 1px solid var(--input-border);
    width: 70%; /* Slightly narrower for larger screens */
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.5);
}

.close-button {
    color: var(--text-light);
    float: right;
    font-size: 30px; /* Larger close button */
    font-weight: bold;
    transition: color 0.2s ease-in-out;
}

.close-button:hover,
.close-button:focus {
    color: var(--accent); /* Accent color on hover */
    text-decoration: none;
    cursor: pointer;
}

#profile-tests-checkboxes label {
    display: block;
    font-weight: normal;
    margin-bottom: 0.5em;
}
#profile-tests-checkboxes input[type="checkbox"] {
    margin-right: 0.5em;
}


#test-status-log, #results-list, #result-details-view, #dc-form-area {
    margin-top: 1em;
    padding: 1em; /* Increased padding */
    background-color: var(--input-bg); /* Darker background for these areas */
    border: 1px solid var(--input-border);
    min-height: 100px; /* Increased min-height */
    max-height: 350px;
    overflow-y: auto;
    border-radius: 4px;
    color: var(--text-light); /* Ensure text is light */
}
#test-status-log p, #result-details-view pre { /* Ensure preformatted text also uses light color */
    color: var(--text-light);
    word-wrap: break-word; /* Wrap long lines in logs/results */
    white-space: pre-wrap; /* Preserve whitespace but wrap */
}


/* Responsive adjustments */
@media (max-width: 768px) {
    body {
        font-size: 15px; /* Maintain readability */
    }
    main {
        padding: 1em;
        gap: 1em;
    }
    section {
        padding: 1em;
    }
    header h1 {
        font-size: 1.6em;
    }
    button, input[type="text"], textarea, select {
        padding: 10px;
        font-size: 0.95em;
    }
    .modal-content {
        width: 90%;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 14px;
    }
    header h1 {
        font-size: 1.3em;
    }
    main {
        padding: 0.5em;
    }
    section {
        padding: 0.8em;
    }
    #profile-management div,
    #results-viewer div,
    #device-conditions div {
        /* display: flex; flex-direction: column; already default for divs */
    }
    #profile-management div button, /* These selectors might be too broad */
    #results-viewer div button,
    #device-conditions div button {
        /* width: 100%;  Let them be natural width or style specific buttons */
        /* margin-top: 5px; */
    }
    /* Ensure buttons inside these specific divs are full width if that's the intent */
    #profile-management > div > button,
    #results-viewer > div > button,
    #device-conditions > div > button {
        width: 100%;
        margin-top: 8px;
    }
    input[type="text"],
    textarea,
    select {
       width: calc(100% - 20px); /* Adjust for padding */
    }
    .modal-content {
        width: 95%;
        margin: 5% auto; /* Adjust margin for smaller screens */
        padding: 15px;
    }
}
