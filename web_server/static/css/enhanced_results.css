/* Enhanced Results Viewer Styles */
/* Maintains dark theme and existing design patterns from index.html */

/* Results Viewer Container */
#enhanced-results-viewer {
    background: var(--card-bg, #1a1a1a);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

/* View Controls */
.results-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.view-mode-selector {
    display: flex;
    gap: 10px;
}

.view-mode-btn {
    padding: 8px 16px;
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    color: #e0e0e0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.view-mode-btn:hover {
    background: #3a3a3a;
}

.view-mode-btn.active {
    background: #4a9eff;
    border-color: #4a9eff;
    color: white;
}

/* Statistics Section */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    background: #2a2a2a;
    padding: 15px;
    border-radius: 6px;
    text-align: center;
}

.stat-label {
    display: block;
    color: #888;
    font-size: 0.85em;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: #e0e0e0;
    font-size: 1.5em;
    font-weight: bold;
}

/* List View */
.results-list-view {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.result-item {
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 15px;
    transition: all 0.3s ease;
}

.result-item:hover {
    background: #333;
    border-color: #4a9eff;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.result-header h4 {
    margin: 0;
    color: #e0e0e0;
    font-size: 1.1em;
}

.result-timestamp {
    color: #888;
    font-size: 0.9em;
}

.result-summary {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.badge-success {
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
}

.badge-danger {
    background: rgba(248, 113, 113, 0.2);
    color: #f87171;
}

.badge-warning {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.badge-info {
    background: rgba(74, 158, 255, 0.2);
    color: #4a9eff;
}

/* Grid View */
.results-grid-view {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.result-card {
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
}

.result-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(74, 158, 255, 0.2);
    border-color: #4a9eff;
}

/* Timeline View */
.results-timeline-view {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    padding: 15px;
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 20px;
    width: 12px;
    height: 12px;
    background: #4a9eff;
    border: 2px solid #1a1a1a;
    border-radius: 50%;
}

/* Modal Styles */
#enhanced-result-details-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
}

#enhanced-result-details-modal .modal-content {
    position: relative;
    background: #1a1a1a;
    margin: 2% auto;
    padding: 20px;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    border-radius: 8px;
    overflow-y: auto;
}

/* Tests Table */
.tests-table {
    width: 100%;
    border-collapse: collapse;
}

.tests-table th {
    padding: 12px;
    text-align: left;
    color: #888;
    font-weight: 500;
    border-bottom: 1px solid #3a3a3a;
    background: #2a2a2a;
}

.tests-table td {
    padding: 10px 12px;
    color: #e0e0e0;
    border-bottom: 1px solid #2a2a2a;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.status-badge.status-passed {
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
}

.status-badge.status-failed {
    background: rgba(248, 113, 113, 0.2);
    color: #f87171;
}

.status-badge.status-skipped {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

/* Tabs */
.details-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #3a3a3a;
}

.tab-btn {
    padding: 10px 20px;
    background: transparent;
    border: none;
    color: #888;
    cursor: pointer;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #4a9eff;
    border-bottom-color: #4a9eff;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Loading & Empty States */
.loading-state, .empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #888;
}

.spinner {
    width: 40px;
    height: 40px;
    margin: 0 auto 20px;
    border: 4px solid #3a3a3a;
    border-top-color: #4a9eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 6px;
    background: #2a2a2a;
    color: #e0e0e0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 2000;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    background: rgba(74, 222, 128, 0.2);
    border: 1px solid #4ade80;
}

.notification-error {
    background: rgba(248, 113, 113, 0.2);
    border: 1px solid #f87171;
}

/* Responsive Design */
@media (max-width: 768px) {
    .results-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .results-grid-view {
        grid-template-columns: 1fr;
    }
    
    #enhanced-result-details-modal .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
