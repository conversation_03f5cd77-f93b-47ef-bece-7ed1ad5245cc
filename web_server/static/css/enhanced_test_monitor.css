/* Enhanced Test Monitor Styles */
/* Maintains dark theme and existing design patterns */

.enhanced-monitor-container {
    background: #1a1a1a;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

/* Monitor Header */
.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #3a3a3a;
}

.monitor-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.monitor-title h3 {
    margin: 0;
    color: #e0e0e0;
}

.status-indicator {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.85em;
    font-weight: 500;
    text-transform: uppercase;
}

.status-indicator.status-idle {
    background: #3a3a3a;
    color: #888;
}

.status-indicator.status-running {
    background: rgba(74, 158, 255, 0.2);
    color: #4a9eff;
    animation: pulse 2s infinite;
}

.status-indicator.status-paused {
    background: rgba(251, 191, 36, 0.2);
    color: #fbbf24;
}

.status-indicator.status-complete {
    background: rgba(74, 222, 128, 0.2);
    color: #4ade80;
}

.status-indicator.status-stopped {
    background: rgba(248, 113, 113, 0.2);
    color: #f87171;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Monitor Stats */
.monitor-stats {
    display: flex;
    gap: 20px;
}

.stat-box {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stat-label {
    color: #888;
    font-size: 0.85em;
    margin-bottom: 4px;
}

.stat-value {
    color: #e0e0e0;
    font-size: 1.2em;
    font-weight: bold;
}

/* Progress Bar */
.monitor-progress {
    margin-bottom: 25px;
}

.progress-bar-container {
    margin-bottom: 10px;
}

.progress-bar {
    height: 24px;
    background: #2a2a2a;
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4a9eff, #4ade80);
    border-radius: 12px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-details {
    display: flex;
    justify-content: center;
    color: #888;
    font-size: 0.9em;
}

/* Test Cards Grid */
.test-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
    max-height: 400px;
    overflow-y: auto;
    padding: 5px;
}

.test-card {
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.3s ease;
}

.test-card:hover {
    border-color: #4a9eff;
    box-shadow: 0 2px 8px rgba(74, 158, 255, 0.2);
}

.test-card.test-passed {
    border-color: #4ade80;
    background: rgba(74, 222, 128, 0.05);
}

.test-card.test-failed {
    border-color: #f87171;
    background: rgba(248, 113, 113, 0.05);
}

.test-card.test-skipped {
    border-color: #fbbf24;
    background: rgba(251, 191, 36, 0.05);
}

.test-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.test-name {
    color: #e0e0e0;
    font-weight: 500;
    font-size: 0.95em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
}

.test-type {
    color: #888;
    font-size: 0.8em;
    padding: 2px 6px;
    background: #1a1a1a;
    border-radius: 3px;
}

.test-card-body {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.test-status-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner-small {
    width: 20px;
    height: 20px;
    border: 2px solid #3a3a3a;
    border-top-color: #4a9eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.icon-check {
    color: #4ade80;
    font-size: 1.2em;
}

.icon-cross {
    color: #f87171;
    font-size: 1.2em;
}

.icon-skip {
    color: #fbbf24;
    font-size: 1.2em;
}

.test-progress {
    flex: 1;
}

.test-progress-bar {
    height: 6px;
    background: #1a1a1a;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.test-progress-fill {
    height: 100%;
    background: #4a9eff;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.test-progress-text {
    color: #888;
    font-size: 0.8em;
}

.test-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.test-duration {
    color: #888;
    font-size: 0.85em;
}

.test-action {
    padding: 2px 8px;
    font-size: 0.8em;
    background: #3a3a3a;
    border: 1px solid #4a4a4a;
    color: #888;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.test-action:hover {
    background: #4a4a4a;
    color: #e0e0e0;
}

/* Monitor Logs */
.monitor-logs {
    background: #0a0a0a;
    border: 1px solid #2a2a2a;
    border-radius: 6px;
    margin-bottom: 20px;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #1a1a1a;
    border-bottom: 1px solid #2a2a2a;
}

.logs-header h4 {
    margin: 0;
    color: #888;
    font-size: 0.95em;
}

.log-controls {
    display: flex;
    gap: 8px;
}

.log-stream {
    height: 200px;
    overflow-y: auto;
    padding: 10px 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
}

.log-entry {
    display: flex;
    gap: 10px;
    margin-bottom: 4px;
    padding: 2px 0;
}

.log-time {
    color: #666;
    white-space: nowrap;
}

.log-message {
    color: #b0b0b0;
    word-break: break-word;
}

.log-entry.log-info .log-message {
    color: #b0b0b0;
}

.log-entry.log-success .log-message {
    color: #4ade80;
}

.log-entry.log-warning .log-message {
    color: #fbbf24;
}

.log-entry.log-error .log-message {
    color: #f87171;
}

/* Verbose mode - show all logs */
.log-stream.verbose .log-entry {
    display: flex;
}

/* Simple mode - hide info logs */
.log-stream:not(.verbose) .log-entry.log-info {
    display: none;
}

/* Monitor Controls */
.monitor-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.monitor-controls button {
    padding: 10px 20px;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-warning {
    background: rgba(251, 191, 36, 0.2);
    border: 1px solid #fbbf24;
    color: #fbbf24;
}

.btn-warning:hover:not(:disabled) {
    background: rgba(251, 191, 36, 0.3);
}

.btn-danger {
    background: rgba(248, 113, 113, 0.2);
    border: 1px solid #f87171;
    color: #f87171;
}

.btn-danger:hover:not(:disabled) {
    background: rgba(248, 113, 113, 0.3);
}

.btn-compact {
    padding: 4px 10px;
    font-size: 0.85em;
    background: #2a2a2a;
    border: 1px solid #3a3a3a;
    color: #888;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-compact:hover {
    background: #3a3a3a;
    color: #e0e0e0;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .test-cards-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .monitor-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }
    
    .monitor-stats {
        justify-content: space-around;
    }
    
    .test-cards-grid {
        grid-template-columns: 1fr;
        max-height: 300px;
    }
    
    .monitor-controls {
        flex-wrap: wrap;
    }
}

/* Scrollbar Styling */
.test-cards-grid::-webkit-scrollbar,
.log-stream::-webkit-scrollbar {
    width: 8px;
}

.test-cards-grid::-webkit-scrollbar-track,
.log-stream::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.test-cards-grid::-webkit-scrollbar-thumb,
.log-stream::-webkit-scrollbar-thumb {
    background: #3a3a3a;
    border-radius: 4px;
}

.test-cards-grid::-webkit-scrollbar-thumb:hover,
.log-stream::-webkit-scrollbar-thumb:hover {
    background: #4a4a4a;
}
