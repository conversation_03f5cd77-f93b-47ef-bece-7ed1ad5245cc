/* Crucible Responsive Design Variables
 * Extends existing design system for hardware-adaptive UI
 * Preserves index.html styling while adding scalability
 */

:root {
  /* === EXISTING COLORS (from index.html) === */
  /* Preserve the dark theme colors already in use */
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-card: #333;
  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-muted: #999999;
  --border-color: #555;
  --accent-primary: #4CAF50;
  --accent-secondary: #2196F3;
  --accent-warning: #FF9800;
  --accent-danger: #f44336;

  /* === RESPONSIVE TYPOGRAPHY === */
  /* Scalable fonts using clamp() for 768p to 4K support */
  --font-size-xs: clamp(0.75rem, 1.5vw, 0.875rem);
  --font-size-sm: clamp(0.875rem, 2vw, 1rem);
  --font-size-base: clamp(1rem, 2.5vw, 1.125rem);
  --font-size-lg: clamp(1.125rem, 3vw, 1.25rem);
  --font-size-xl: clamp(1.25rem, 3.5vw, 1.5rem);
  --font-size-2xl: clamp(1.5rem, 4vw, 2rem);
  --font-size-3xl: clamp(2rem, 5vw, 3rem);

  /* === RESPONSIVE SPACING === */
  /* Maintains existing dashboard-card spacing patterns */
  --space-xs: clamp(0.25rem, 0.5vw, 0.5rem);
  --space-sm: clamp(0.5rem, 1vw, 0.75rem);
  --space-md: clamp(0.75rem, 1.5vw, 1rem);
  --space-lg: clamp(1rem, 2vw, 1.5rem);
  --space-xl: clamp(1.5rem, 3vw, 2rem);
  --space-2xl: clamp(2rem, 4vw, 3rem);

  /* === ADAPTIVE BUTTON SIZES === */
  /* Extends btn-secondary for different hardware */
  --btn-padding-sm: var(--space-sm) var(--space-md);
  --btn-padding-md: var(--space-md) var(--space-lg);
  --btn-padding-lg: var(--space-lg) var(--space-xl);
  --btn-font-size: var(--font-size-base);
  --btn-border-radius: 4px;

  /* === RESPONSIVE CONTAINERS === */
  /* Extends dashboard-card for different screen sizes */
  --container-padding: clamp(1rem, 2vw, 2rem);
  --card-padding: clamp(0.75rem, 1.5vw, 1.5rem);
  --card-gap: clamp(0.5rem, 1vw, 1rem);

  /* === HARDWARE-ADAPTIVE BREAKPOINTS === */
  /* Support for diverse hardware resolutions */
  --breakpoint-sm: 768px;   /* Minimum supported resolution */
  --breakpoint-md: 1024px;  /* Standard laptop */
  --breakpoint-lg: 1440px;  /* Desktop */
  --breakpoint-xl: 1920px;  /* Full HD */
  --breakpoint-2xl: 2560px; /* 4K displays */

  /* === TOUCH-FRIENDLY SIZING === */
  /* Optional touch support without breaking mouse/keyboard */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-spacing: var(--space-md);

  /* === PERFORMANCE OPTIMIZATIONS === */
  /* For low-spec hardware */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.25s ease;
  --transition-slow: 0.35s ease;
  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.2);
  --shadow-normal: 0 2px 6px rgba(0, 0, 0, 0.3);

  /* === REAL-TIME UPDATE INDICATORS === */
  --status-running: #2196F3;
  --status-pass: #4CAF50;
  --status-fail: #f44336;
  --status-warning: #FF9800;
  --status-pending: #9E9E9E;
  --status-skipped: #607D8B;

  /* === ACCESSIBILITY === */
  /* High contrast for diverse lighting conditions */
  --focus-ring: 2px solid var(--accent-primary);
  --focus-offset: 2px;
}

/* === RESPONSIVE UTILITIES === */
/* Hardware-adaptive helper classes */

.responsive-text {
  font-size: var(--font-size-base);
  line-height: 1.5;
}

.responsive-container {
  padding: var(--container-padding);
  max-width: 100%;
}

.adaptive-grid {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* === HARDWARE-SPECIFIC OPTIMIZATIONS === */

/* Low-spec hardware optimizations */
@media (max-width: 1024px) and (max-height: 768px) {
  :root {
    --transition-fast: 0.1s ease;
    --transition-normal: 0.15s ease;
    --transition-slow: 0.2s ease;
    --shadow-subtle: none;
    --shadow-normal: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

/* High-DPI display optimizations */
@media (min-resolution: 2dppx) {
  :root {
    --font-size-base: clamp(1rem, 2.2vw, 1.2rem);
    --btn-border-radius: 6px;
  }
}

/* Ultra-wide display support */
@media (min-aspect-ratio: 21/9) {
  .adaptive-grid {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  }
}

/* === STATUS INDICATOR ANIMATIONS === */
/* Subtle animations for real-time feedback */

@keyframes pulse-status {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-running {
  color: var(--status-running);
  animation: pulse-status 2s infinite;
}

.status-pass { color: var(--status-pass); }
.status-fail { color: var(--status-fail); }
.status-warning { color: var(--status-warning); }
.status-pending { color: var(--status-pending); }
.status-skipped { color: var(--status-skipped); }

/* === FOCUS MANAGEMENT === */
/* Keyboard navigation support */

.focusable:focus {
  outline: var(--focus-ring);
  outline-offset: var(--focus-offset);
}

/* === PRINT STYLES === */
/* For attestation documents */

@media print {
  :root {
    --bg-primary: white;
    --bg-secondary: white;
    --bg-card: white;
    --text-primary: black;
    --text-secondary: #333;
    --border-color: #ccc;
  }
  
  .no-print { display: none !important; }
}
