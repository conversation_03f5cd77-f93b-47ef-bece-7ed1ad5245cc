<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Keyboard Test - Crucible</title>
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/dark-theme.css') }}"
    />
    <style>
      /* Map generic variables used in this file to variables provided by the main theme */
      :root {
        --background-color: var(--bg-dark);
        --text-color: var(--text-light);
        --border-color: var(--input-border);
        --secondary-bg-color: var(--bg-light);
        --accent-color-dark: var(--button-hover-bg, #0056b3);
        --primary-color: var(--primary);
        --button-bg-color: var(--button-bg);
        --button-text-color: var(--button-text);
        --success-color: var(--success);
        --error-color: var(--error);
        --warning-color: #ff9800;
        --outline-color: #d9d9d9;
        --panel-radius: 8px;
      }
      body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        background-color: var(--background-color);
        color: var(--text-color);
        font-family: Arial, sans-serif;
        padding: 10px;
        overflow-x: auto; /* Allow horizontal scrolling for very wide keyboards */
      }
      .keyboard-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: auto; /* Fit content */
        max-width: 100%;
        padding: 15px;
        border: 1px solid var(--border-color);
        border-radius: var(--panel-radius);
        background-color: var(--secondary-bg-color);
      }
      .keyboard-info {
        display: flex;
        justify-content: space-between;
        width: 100%;
        margin-bottom: 15px;
        padding: 0 10px;
        font-size: 1.1em;
      }
      .keyboard-info .status {
        font-weight: bold;
      }
      .progress-bar-container {
        width: 100%;
        background-color: var(--accent-color-dark);
        border-radius: 5px;
        margin-bottom: 15px;
        height: 25px;
        overflow: hidden;
      }
      .progress-bar-fill {
        height: 100%;
        width: 0%;
        background-color: var(--primary-color);
        border-radius: 5px;
        transition: width 0.2s ease-in-out;
        text-align: center;
        line-height: 25px;
        color: white;
        font-weight: bold;
      }
      .keyboard-layout {
        display: flex; /* Main keyboard and numpad side-by-side */
        gap: 20px; /* Space between main keyboard and numpad */
        justify-content: center;
        align-items: flex-start; /* Align tops of keyboard sections */
      }
      .main-keyboard,
      .numpad {
        display: flex;
        flex-direction: column;
        gap: 5px; /* Space between rows */
      }
      .keyboard-row {
        display: flex;
        justify-content: center;
        gap: 5px; /* Space between keys in a row */
      }
      .key {
        background-color: var(--button-bg-color);
        color: var(--button-text-color);
        border: 1px solid var(--border-color);
        border-radius: 5px;
        padding: 5px; /* Reduced padding */
        min-width: 30px; /* Minimum width */
        height: 40px; /* Fixed height */
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8em; /* Smaller font size */
        text-align: center;
        transition: background-color 100ms ease, color 100ms ease, transform 60ms ease;
        box-shadow: 0 2px 0 var(--accent-color-dark); /* Simple 3D effect */
        white-space: nowrap; /* Prevent text wrapping */
        user-select: none;
      }
      .key.pressed {
        background-color: var(--primary-color);
        color: white;
        box-shadow: none;
        transform: translateY(1px); /* Simulate press */
      }
      .key.placeholder {
        visibility: hidden; /* For spacing where multi-row keys are */
      }
      .controls {
        margin-top: 20px;
        display: flex;
        gap: 15px;
      }
      .btn {
        padding: 12px 25px;
        border-radius: var(--panel-radius);
      }
      .btn:focus-visible {
        outline: 2px solid var(--outline-color);
        outline-offset: 2px;
      }
      .instructions {
        margin-bottom: 15px;
        font-size: 1em;
        text-align: center;
        max-width: 800px;
      }
      .legend {
        display: flex;
        gap: 12px;
        align-items: center;
        font-size: 0.9em;
        color: var(--text-color);
        margin-bottom: 10px;
      }
      .legend .chip {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 3px 8px;
        border-radius: 999px;
        border: 1px solid var(--border-color);
        background: rgba(0,0,0,0.15);
      }
      .chip .swatch {
        width: 12px;
        height: 12px;
        border-radius: 3px;
        border: 1px solid var(--border-color);
      }
      .swatch.default { background: var(--button-bg-color); }
      .swatch.pressed { background: var(--primary-color); }
      .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0,0,0,0);
        white-space: nowrap;
        border: 0;
      }
      @media (prefers-reduced-motion: reduce) {
        .key { transition: none; }
        .progress-bar-fill { transition: none; }
      }
      #unpressedKeysList {
        margin-top: 15px;
        font-size: 0.9em;
        max-height: 100px;
        overflow-y: auto;
        padding: 5px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        width: 100%;
        max-width: 700px;
      }
    </style>
  </head>
  <body>
    <div class="keyboard-container">
      <h2 id="testTitle">Keyboard Test</h2>
      <p class="instructions" id="instructionText" aria-live="polite" role="status">
        A native keyboard test window will open automatically. This allows
        testing of ALL keys including system keys like PrintScreen and Windows
        key.
        <strong
          >Please wait for the native test window to appear and follow the
          instructions there.</strong
        >
      </p>
      <div class="keyboard-info" aria-live="polite">
        <span id="keysPressedCount">Pressed: 0/0</span>
        <span id="testStatus" class="status">Status: Running</span>
      </div>
      <div class="progress-bar-container" aria-label="Progress" role="progressbar" aria-valuemin="0" aria-valuenow="0" aria-valuemax="100">
        <div class="progress-bar-fill" id="progressBarFill">0%</div>
      </div>

      <div class="legend" aria-hidden="true">
        <span class="chip"><span class="swatch default"></span> Unpressed</span>
        <span class="chip"><span class="swatch pressed"></span> Pressed</span>
      </div>
      <div class="keyboard-layout" id="keyboardLayout" role="group" aria-label="Keyboard layout">
        <!-- Keys will be dynamically inserted here by JavaScript -->
      </div>

      <div id="unpressedKeysListContainer" style="display: none">
        <strong>Untested Keys:</strong>
        <div id="unpressedKeysList"></div>
      </div>

      <div class="controls">
        <button id="forceCompleteBtn" class="btn btn-danger" title="Mark test complete with hardware issue" aria-label="Force complete with issue">
          Force Complete (Hardware Issue)
        </button>
        <button id="completeTestBtn" class="btn btn-primary" disabled title="Enabled at 90% keys pressed" aria-label="Complete test">
          Complete Test
        </button>
        <button id="closeBtn" class="btn btn-secondary" title="Close keyboard test window" aria-label="Close">Close</button>
      </div>
    </div>

    <script>
      let currentSessionId = null;
      let eventSource = null;
      let completing = false;
      let reconnectTimer = null;
      let keyLayoutData = null;
      let totalKeys = 0;
      let pressedKeysMap = new Map(); // Stores 'key_name' -> domElement
      let keyDomElements = {}; // Stores 'key_name' -> domElement for all keys

      const MIN_PASS_PERCENT = 90; // From agent.tests.keyboard_test

      // Key mapping for JavaScript key events to layout key names
      // Synchronized with agent.tests.keyboard_test.py KEY_MAP
      const jsKeyToLayoutMap = {
        // event.key to layout key name
        Escape: "Escape",
        F1: "F1",
        F2: "F2",
        F3: "F3",
        F4: "F4",
        F5: "F5",
        F6: "F6",
        F7: "F7",
        F8: "F8",
        F9: "F9",
        F10: "F10",
        F11: "F11",
        F12: "F12",
        PrintScreen: "Print", // Note: 'PrintScreen' is common for event.key, Python uses 'Print'
        ScrollLock: "Scroll_Lock",
        Pause: "Pause",
        "`": "grave",
        "~": "grave", // Shift + `
        1: "1",
        "!": "1",
        2: "2",
        "@": "2",
        3: "3",
        "#": "3",
        4: "4",
        $: "4",
        5: "5",
        "%": "5",
        6: "6",
        "^": "6",
        7: "7",
        "&": "7",
        8: "8",
        "*": "8", // Note: NumpadMultiply is different
        9: "9",
        "(": "9",
        0: "0",
        ")": "0",
        "-": "minus",
        _: "minus",
        "=": "equal",
        "+": "equal", // Note: NumpadAdd is different
        Backspace: "BackSpace",
        Insert: "Insert",
        Home: "Home",
        PageUp: "Page_Up",
        Tab: "Tab",
        Delete: "Delete",
        End: "End",
        PageDown: "Page_Down",
        CapsLock: "Caps_Lock",
        Enter: "Return", // Covers main Enter key
        Shift: "Shift_L", // General Shift, map to Left for simplicity or handle event.code
        Control: "Control_L", // General Ctrl
        Alt: "Alt_L", // General Alt
        Meta: "Super_L", // Windows/Command key
        " ": "space",
        ContextMenu: "Menu",
        ArrowUp: "Up",
        ArrowLeft: "Left",
        ArrowDown: "Down",
        ArrowRight: "Right",
        "[": "bracketleft",
        "{": "bracketleft",
        "]": "bracketright",
        "}": "bracketright",
        "\\": "backslash",
        "|": "backslash",
        ";": "semicolon",
        ":": "semicolon",
        "'": "apostrophe",
        '"': "apostrophe",
        ",": "comma",
        "<": "comma",
        ".": "period",
        ">": "period", // Note: NumpadDecimal is different
        "/": "slash",
        "?": "slash", // Note: NumpadDivide is different

        // Mappings for event.code for more specific keys (especially modifiers and Numpad)
        // These often override the event.key mappings if event.code is more specific.
        Backquote: "grave", // For ` key if event.key is not reliable
        Digit1: "1",
        Digit2: "2",
        Digit3: "3",
        Digit4: "4",
        Digit5: "5",
        Digit6: "6",
        Digit7: "7",
        Digit8: "8",
        Digit9: "9",
        Digit0: "0",
        Minus: "minus",
        Equal: "equal",
        BracketLeft: "bracketleft",
        BracketRight: "bracketright",
        Backslash: "backslash",
        Semicolon: "semicolon",
        Quote: "apostrophe",
        Comma: "comma",
        Period: "period",
        Slash: "slash",

        ShiftLeft: "Shift_L",
        ShiftRight: "Shift_R",
        ControlLeft: "Control_L",
        ControlRight: "Control_R",
        AltLeft: "Alt_L",
        AltRight: "Alt_R",
        MetaLeft: "Super_L",
        MetaRight: "Super_R", // Or map to Super_L / Super_R if layout has them distinct

        Numpad0: "KP_0",
        Numpad1: "KP_1",
        Numpad2: "KP_2",
        Numpad3: "KP_3",
        Numpad4: "KP_4",
        Numpad5: "KP_5",
        Numpad6: "KP_6",
        Numpad7: "KP_7",
        Numpad8: "KP_8",
        Numpad9: "KP_9",
        NumpadAdd: "KP_Add",
        NumpadSubtract: "KP_Subtract",
        NumpadMultiply: "KP_Multiply",
        NumpadDivide: "KP_Divide",
        NumpadDecimal: "KP_Decimal",
        NumpadEnter: "KP_Enter",
        NumLock: "Num_Lock",

        // For keys that might have different event.key values on Numpad (e.g. Home, End)
        // Python KEY_MAP handles these alternatives like "KP_Home": "KP_7"
        // The js map should ensure that if event.code indicates a numpad key, it maps correctly.
        // Example: if Numpad7 sends event.key="Home" but event.code="Numpad7", we prefer "Numpad7".
      };

      // Add lowercase a-z for event.key
      for (let i = 0; i < 26; i++) {
        const char = String.fromCharCode(97 + i); // a-z
        jsKeyToLayoutMap[char] = char; // for event.key 'a'
        jsKeyToLayoutMap[char.toUpperCase()] = char; // for event.key 'A' (treat as 'a')
        // Also map KeyA, KeyB, etc. from event.code to 'a', 'b'
        jsKeyToLayoutMap["Key" + char.toUpperCase()] = char;
      }

      function getUrlParams() {
        const params = new URLSearchParams(window.location.search);
        return {
          asset_number: params.get("asset_number") || "KBTEST001",
          session_id: params.get("session_id") || null,
        };
      }

      async function initTest() {
        const params = getUrlParams();
        console.log("🔍 KEYBOARD TEST WINDOW: initTest() called");
        console.log("🔍 KEYBOARD TEST WINDOW: URL params:", params);
        console.log("🔍 KEYBOARD TEST WINDOW: Full URL:", window.location.href);

        document.getElementById(
          "testTitle"
        ).textContent = `Keyboard Test for Asset: ${params.asset_number}`;

        try {
          // Check if a session ID was provided in the URL (from visual test sequence)
          if (params.session_id) {
            console.log(
              "✅ KEYBOARD TEST WINDOW: Using existing session ID:",
              params.session_id
            );
            currentSessionId = params.session_id;
            connectToProgressStream();
          } else {
            // Only create a new session if no session ID was provided
            console.log(
              "❌ KEYBOARD TEST WINDOW: No session ID provided, creating new session"
            );
            const response = await fetch("/api/visual_test/start", {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                test_type: "keyboard",
                asset_number: params.asset_number,
                test_params: {},
              }),
            });
            const result = await response.json();
            if (!response.ok)
              throw new Error(result.error || "Failed to start test session");

            currentSessionId = result.session_id;
            connectToProgressStream();
          }
        } catch (error) {
          console.error("Error starting test:", error);
          document.getElementById("instructionText").textContent =
            "Error starting test: " + error.message;
          document.getElementById("testStatus").textContent = "Status: Error";
        }

        document
          .getElementById("closeBtn")
          .addEventListener("click", closeTest);
        document
          .getElementById("completeTestBtn")
          .addEventListener("click", () => completeTest(false));
        document
          .getElementById("forceCompleteBtn")
          .addEventListener("click", () => completeTest(true));

        // Add comprehensive key event listeners for enhanced capture
        document.addEventListener("keydown", handleKeyDown, true); // Use capture phase
        document.addEventListener("keyup", handleKeyUp, true); // Also capture keyup for completeness

        // Add additional event listeners to prevent OS functions
        document.addEventListener(
          "keypress",
          function (event) {
            if (shouldPreventDefault(event)) {
              event.preventDefault();
              event.stopPropagation();
              event.stopImmediatePropagation();
              return false;
            }
          },
          true
        );

        // Add window-level event listeners to catch system keys
        window.addEventListener(
          "keydown",
          function (event) {
            if (shouldPreventDefault(event)) {
              event.preventDefault();
              event.stopPropagation();
              event.stopImmediatePropagation();
              return false;
            }
          },
          true
        );

        window.addEventListener(
          "keyup",
          function (event) {
            if (shouldPreventDefault(event)) {
              event.preventDefault();
              event.stopPropagation();
              event.stopImmediatePropagation();
              return false;
            }
          },
          true
        );

        // Prevent context menu to capture right-click and context menu key
        document.addEventListener("contextmenu", function (event) {
          event.preventDefault();
          event.stopPropagation();
          return false;
        });

        // Prevent drag and drop which might interfere with key testing
        document.addEventListener("dragstart", function (event) {
          event.preventDefault();
          event.stopPropagation();
          return false;
        });

        // Prevent window focus changes that might interfere with key capture
        window.addEventListener("blur", function (event) {
          // Try to regain focus immediately
          setTimeout(() => {
            window.focus();
            document.focus();
          }, 10);
        });

        // Request fullscreen to get better key capture
        try {
          if (document.documentElement.requestFullscreen) {
            document.documentElement.requestFullscreen().catch((err) => {
              console.log("Fullscreen request failed:", err);
            });
          }
        } catch (err) {
          console.log("Fullscreen not supported:", err);
        }

        // Ensure the document has focus for key capture
        document.focus();
        window.focus();

        // Add visibility change listener to maintain focus
        document.addEventListener("visibilitychange", function () {
          if (!document.hidden) {
            setTimeout(() => {
              window.focus();
              document.focus();
            }, 100);
          }
        });
      }

      function connectToProgressStream() {
        if (!currentSessionId) return;

        const connect = () => {
          if (eventSource) {
            try { eventSource.close(); } catch {}
            eventSource = null;
          }
          eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);

          eventSource.onmessage = function (event) {
            const data = JSON.parse(event.data);
            if (data.error) {
              console.error("SSE Error:", data.error);
              document.getElementById("instructionText").textContent =
                "Error: " + data.error;
              document.getElementById("testStatus").textContent = "Status: Error";
              return;
            }
            updateKeyboardState(data.progress);
            if (data.test_complete) {
              handleTestComplete(data.test_result);
            }
          };

          eventSource.onerror = function (err) {
            console.error("EventSource failed:", err);
            if (eventSource) {
              try { eventSource.close(); } catch {}
              eventSource = null;
            }
            if (!completing) {
              document.getElementById("instructionText").textContent =
                "Connection to server lost. Reconnecting…";
              document.getElementById("testStatus").textContent =
                "Status: Reconnecting";
              clearTimeout(reconnectTimer);
              reconnectTimer = setTimeout(connect, 1000);
            }
          };
        };

        connect();
      }

      function renderKeyboard(layout) {
        if (!layout || Object.keys(layout).length === 0) {
          console.error("Received empty or invalid layout data:", layout);
          document.getElementById("keyboardLayout").innerHTML =
            "<p>Error: Keyboard layout data not loaded.</p>";
          return;
        }
        keyLayoutData = layout; // Store for later use
        const keyboardLayoutDiv = document.getElementById("keyboardLayout");
        keyboardLayoutDiv.innerHTML = ""; // Clear previous

        const mainKeyboardDiv = document.createElement("div");
        mainKeyboardDiv.className = "main-keyboard";
        const numpadDiv = document.createElement("div");
        numpadDiv.className = "numpad";

        let currentTotalKeys = 0;

        // Determine which rows go where
        const mainRows = ["row1", "row2", "row3", "row4", "row5", "row6"];
        const numpadRows = [
          "numpad_row1",
          "numpad_row2",
          "numpad_row3",
          "numpad_row4",
          "numpad_row5",
        ];

        function createRow(rowName, keysInRow, targetDiv) {
          const rowDiv = document.createElement("div");
          rowDiv.className = "keyboard-row";
          rowDiv.dataset.rowName = rowName;

          keysInRow.forEach((keyInfo) => {
            const keyDiv = document.createElement("div");
            keyDiv.className = "key";
            keyDiv.textContent = keyInfo.display || keyInfo.key;
            keyDiv.dataset.keyName = keyInfo.key;

            // Basic styling for width/height based on keyInfo
            // More sophisticated styling might be needed for perfect alignment
            const keyWidth = keyInfo.width || 1;
            const keyHeight = keyInfo.height || 1;
            keyDiv.style.flexGrow = keyWidth; // Simple width scaling
            keyDiv.style.minWidth = `${30 * keyWidth}px`; // Base width * factor
            if (keyHeight > 1) {
              keyDiv.style.height = `${40 * keyHeight + 5 * (keyHeight - 1)}px`; // Base height * factor + gaps
              // For keys spanning multiple rows, we might need to adjust alignment or use grid
            }

            rowDiv.appendChild(keyDiv);
            keyDomElements[keyInfo.key] = keyDiv;
            currentTotalKeys++;
          });
          targetDiv.appendChild(rowDiv);
        }

        for (const rowName in layout) {
          if (mainRows.includes(rowName)) {
            createRow(rowName, layout[rowName], mainKeyboardDiv);
          } else if (numpadRows.includes(rowName)) {
            createRow(rowName, layout[rowName], numpadDiv);
          }
        }

        keyboardLayoutDiv.appendChild(mainKeyboardDiv);
        if (numpadDiv.children.length > 0) {
          // Only add numpad if it has keys
          keyboardLayoutDiv.appendChild(numpadDiv);
        }

        totalKeys = currentTotalKeys; // Set the global totalKeys
        console.log("Keyboard rendered. Total keys identified:", totalKeys);
      }

      function updateKeyboardState(progressData) {
        if (!keyLayoutData && progressData.key_layout) {
          renderKeyboard(progressData.key_layout);
        }
        if (!totalKeys && progressData.total_keys > 0) {
          // Fallback if renderKeyboard didn't set it
          totalKeys = progressData.total_keys;
        }

        const pressedCount = progressData.pressed_keys_count || 0;
        const currentTotalForProgress =
          progressData.total_keys || totalKeys || 1; // Avoid division by zero

        document.getElementById(
          "keysPressedCount"
        ).textContent = `Pressed: ${pressedCount}/${currentTotalForProgress}`;
        const percentage =
          currentTotalForProgress > 0
            ? (pressedCount / currentTotalForProgress) * 100
            : 0;
        const progressBarFill = document.getElementById("progressBarFill");
        progressBarFill.style.width = percentage + "%";
        progressBarFill.textContent = Math.round(percentage) + "%";

        // Highlight pressed keys
        if (
          progressData.pressed_keys &&
          Array.isArray(progressData.pressed_keys)
        ) {
          progressData.pressed_keys.forEach((keyName) => {
            if (
              keyDomElements[keyName] &&
              !keyDomElements[keyName].classList.contains("pressed")
            ) {
              keyDomElements[keyName].classList.add("pressed");
            }
          });
        }

        // Un-highlight keys if state changes (e.g. test reset, though not typical for this test)
        // This part might be more relevant if we need to show unpressed keys changing.
        // For now, we assume keys only go to 'pressed'.

        if (percentage >= MIN_PASS_PERCENT) {
          document.getElementById("completeTestBtn").disabled = false;
        } else {
          document.getElementById("completeTestBtn").disabled = true;
        }
        // Update unpressed keys list (optional, can be verbose)
        if (
          progressData.unpressed_keys &&
          progressData.unpressed_keys.length < totalKeys
        ) {
          const unpressedDiv = document.getElementById("unpressedKeysList");
          const container = document.getElementById(
            "unpressedKeysListContainer"
          );
          if (progressData.unpressed_keys.length > 0) {
            unpressedDiv.textContent = progressData.unpressed_keys.join(", ");
            container.style.display = "block";
          } else {
            container.style.display = "none";
          }
        }
      }

      function mapEventToKeyName(event) {
        let mappedKeyName = null;

        // Priority 1: event.code mapping (most reliable for physical keys)
        if (jsKeyToLayoutMap[event.code]) {
          mappedKeyName = jsKeyToLayoutMap[event.code];
        }

        // Priority 2: event.key mapping (can be good for character keys or some special keys)
        // Only use event.key if event.code didn't yield a specific map, or if event.key is more specific.
        if (!mappedKeyName && jsKeyToLayoutMap[event.key]) {
          mappedKeyName = jsKeyToLayoutMap[event.key];
        }

        // Fallback for single characters if CapsLock might be on, or direct char input
        if (!mappedKeyName && event.key && event.key.length === 1) {
          const lowerKey = event.key.toLowerCase();
          if (jsKeyToLayoutMap[lowerKey]) {
            // Check if 'a' is in map for event.key 'A'
            mappedKeyName = jsKeyToLayoutMap[lowerKey];
          }
        }

        // For numpad keys that might report digits via event.key (e.g. "7" instead of "Numpad7")
        // when NumLock is on, event.code (e.g. "Numpad7") is more reliable.
        // The current jsKeyToLayoutMap prioritizes event.code for Numpad keys, which is good.

        // Heuristic for Numpad Enter vs Main Enter if event.code is not distinct enough
        // (though 'NumpadEnter' vs 'Enter' in event.code should be distinct)
        // This was more for Tkinter; browsers are usually good with event.code for this.
        if (event.code === "Enter" && mappedKeyName === "Return") {
          // Main enter
          // If there's a way to detect location (e.g. event.location in some browsers for numpad)
          // This is not universally supported, event.code is better.
          // For now, 'Enter' code maps to 'Return'. 'NumpadEnter' maps to 'KP_Enter'.
        }
        // console.log(`Key Event: key='${event.key}', code='${event.code}', location=${event.location}, mappedTo='${mappedKeyName}'`);
        return mappedKeyName;
      }

      // ===== ENHANCED KEY CAPTURE INFRASTRUCTURE =====

      // Comprehensive key categorization constants for different types of keys that need special handling
      const KEY_CATEGORIES = {
        // Function keys that always need preventDefault to avoid browser functions
        FUNCTION_KEYS: [
          "F1",
          "F2",
          "F3",
          "F4",
          "F5",
          "F6",
          "F7",
          "F8",
          "F9",
          "F10",
          "F11",
          "F12",
        ],

        // System keys that trigger OS functions and need to be captured
        SYSTEM_KEYS: [
          "PrintScreen",
          "ScrollLock",
          "Pause",
          "Insert",
          "Delete",
          "Home",
          "End",
          "PageUp",
          "PageDown",
          "ContextMenu",
        ],

        // Meta/Windows keys that open system menus
        META_KEYS: [
          "Meta",
          "MetaLeft",
          "MetaRight",
          "OS", // OS is alternative name for Meta in some browsers
        ],

        // Navigation keys that can cause page scrolling or focus changes
        NAVIGATION_KEYS: [
          "Tab",
          "ArrowUp",
          "ArrowDown",
          "ArrowLeft",
          "ArrowRight",
          "Space",
        ],

        // Browser shortcut combinations that need to be prevented
        BROWSER_SHORTCUTS: [
          // Refresh shortcuts
          { keys: ["F5"] },
          { keys: ["Ctrl", "r"] },
          { keys: ["Ctrl", "F5"] },
          { keys: ["Ctrl", "Shift", "r"] },

          // Window/tab management
          { keys: ["Ctrl", "w"] },
          { keys: ["Ctrl", "t"] },
          { keys: ["Ctrl", "n"] },
          { keys: ["Ctrl", "Shift", "t"] },
          { keys: ["Alt", "F4"] }, // Will be handled as safety exit

          // Developer tools
          { keys: ["F12"] },
          { keys: ["Ctrl", "Shift", "i"] },
          { keys: ["Ctrl", "Shift", "j"] },
          { keys: ["Ctrl", "Shift", "c"] },

          // Navigation
          { keys: ["Ctrl", "l"] },
          { keys: ["Ctrl", "d"] },
          { keys: ["Ctrl", "h"] },
          { keys: ["Ctrl", "j"] },
          { keys: ["Ctrl", "u"] },

          // Find
          { keys: ["Ctrl", "f"] },
          { keys: ["Ctrl", "g"] },
        ],

        // Modifier keys that should be captured individually
        MODIFIER_KEYS: [
          "Control",
          "ControlLeft",
          "ControlRight",
          "Alt",
          "AltLeft",
          "AltRight",
          "AltGraph",
          "Shift",
          "ShiftLeft",
          "ShiftRight",
        ],

        // Safety exit combinations that should NOT be prevented (emergency exits)
        SAFETY_EXIT_COMBINATIONS: [
          { keys: ["Alt", "F4"] }, // Windows close window
          { keys: ["Cmd", "w"] }, // Mac close window
          { keys: ["Cmd", "q"] }, // Mac quit application
          { keys: ["Ctrl", "Alt", "Delete"] }, // Windows task manager (though unlikely to work in browser)
        ],
      };

      // Enhanced shouldPreventDefault function with comprehensive logic for all key categories
      function shouldPreventDefault(event) {
        const key = event.key;
        const code = event.code;
        const ctrlKey = event.ctrlKey;
        const altKey = event.altKey;
        const metaKey = event.metaKey;
        const shiftKey = event.shiftKey;

        // AGGRESSIVE MODE: Prevent almost all special keys and combinations
        // Only allow basic typing keys and essential safety exits

        // Safety check: Never prevent safety exit combinations
        for (const combo of KEY_CATEGORIES.SAFETY_EXIT_COMBINATIONS) {
          if (isKeyComboMatch(event, combo.keys)) {
            return false; // Allow safety exits to work
          }
        }

        // ALWAYS prevent function keys (F1-F12) - these commonly trigger OS functions
        if (
          KEY_CATEGORIES.FUNCTION_KEYS.includes(key) ||
          KEY_CATEGORIES.FUNCTION_KEYS.includes(code) ||
          /^F\d+$/.test(key) ||
          /^F\d+$/.test(code)
        ) {
          return true;
        }

        // ALWAYS prevent system keys that trigger OS functions
        if (
          KEY_CATEGORIES.SYSTEM_KEYS.includes(key) ||
          KEY_CATEGORIES.SYSTEM_KEYS.includes(code)
        ) {
          return true;
        }

        // ALWAYS prevent Meta/Windows/Super keys to avoid Start menu and OS shortcuts
        if (
          KEY_CATEGORIES.META_KEYS.includes(key) ||
          KEY_CATEGORIES.META_KEYS.includes(code) ||
          key === "Super" ||
          code === "MetaLeft" ||
          code === "MetaRight" ||
          key === "OS" ||
          metaKey
        ) {
          return true;
        }

        // Prevent ALL navigation keys to avoid focus changes and scrolling
        if (
          KEY_CATEGORIES.NAVIGATION_KEYS.includes(key) ||
          KEY_CATEGORIES.NAVIGATION_KEYS.includes(code)
        ) {
          // Special case: Allow Alt+Tab for window switching (safety)
          if (
            (key === "Tab" || code === "Tab") &&
            altKey &&
            !ctrlKey &&
            !metaKey
          ) {
            return false; // Allow Alt+Tab
          }
          return true;
        }

        // Prevent ALL Ctrl combinations except safety exits
        if (ctrlKey) {
          // Allow Ctrl+C for copy (might be needed for accessibility)
          if (key === "c" || key === "C") {
            return false;
          }
          // Prevent all other Ctrl combinations
          return true;
        }

        // Prevent ALL Alt combinations except Alt+Tab (handled above)
        if (altKey && !(key === "Tab" || code === "Tab")) {
          return true;
        }

        // ALWAYS prevent Escape key to avoid exiting fullscreen
        if (key === "Escape" || code === "Escape") {
          return true;
        }

        // ALWAYS prevent Enter keys to avoid form submissions
        if (key === "Enter" || code === "Enter" || code === "NumpadEnter") {
          return true;
        }

        // ALWAYS prevent Backspace to avoid browser back navigation
        if (key === "Backspace" || code === "Backspace") {
          return true;
        }

        // ALWAYS prevent Delete key to avoid potential OS functions
        if (key === "Delete" || code === "Delete") {
          return true;
        }

        // Prevent ALL modifier keys when pressed alone
        if (
          KEY_CATEGORIES.MODIFIER_KEYS.includes(key) ||
          KEY_CATEGORIES.MODIFIER_KEYS.includes(code)
        ) {
          return true;
        }

        // Prevent context menu key
        if (key === "ContextMenu" || code === "ContextMenu") {
          return true;
        }

        // Prevent Insert key
        if (key === "Insert" || code === "Insert") {
          return true;
        }

        // Prevent all Page keys
        if (
          key === "PageUp" ||
          key === "PageDown" ||
          code === "PageUp" ||
          code === "PageDown"
        ) {
          return true;
        }

        // Prevent Home and End keys
        if (
          key === "Home" ||
          key === "End" ||
          code === "Home" ||
          code === "End"
        ) {
          return true;
        }

        // Prevent Print Screen and related keys
        if (
          key === "PrintScreen" ||
          key === "Print" ||
          code === "PrintScreen"
        ) {
          return true;
        }

        // Prevent Scroll Lock and Pause
        if (
          key === "ScrollLock" ||
          key === "Pause" ||
          code === "ScrollLock" ||
          code === "Pause"
        ) {
          return true;
        }

        // Prevent Caps Lock and Num Lock to avoid state changes
        if (
          key === "CapsLock" ||
          key === "NumLock" ||
          code === "CapsLock" ||
          code === "NumLock"
        ) {
          return true;
        }

        // Linux-specific: Prevent additional system shortcuts
        if (isDebianChromium()) {
          // Prevent any remaining Linux shortcuts
          if (shiftKey && altKey) return true; // Shift+Alt combinations
          if (ctrlKey && shiftKey) return true; // Ctrl+Shift combinations (except safety)
        }

        // Allow basic typing keys (a-z, 0-9, punctuation) without modifiers
        if (!ctrlKey && !altKey && !metaKey) {
          // Allow single character keys for typing
          if (key && key.length === 1) {
            return false;
          }
          // Allow space for typing
          if (key === " " || code === "Space") {
            return false;
          }
        }

        // Default: prevent everything else to be safe
        return true;
      }

      // Helper function to check if current key event matches a key combination
      function isKeyComboMatch(event, keyCombo) {
        const currentKeys = [];

        // Add modifier keys if active
        if (event.ctrlKey) currentKeys.push("Ctrl", "Control");
        if (event.altKey) currentKeys.push("Alt");
        if (event.metaKey) currentKeys.push("Cmd", "Meta", "OS");
        if (event.shiftKey) currentKeys.push("Shift");

        // Add the main key (both event.key and event.code for better matching)
        currentKeys.push(event.key);
        currentKeys.push(event.code);

        // Normalize key names for better matching
        const normalizedCurrentKeys = currentKeys.map((k) => k.toLowerCase());
        const normalizedComboKeys = keyCombo.map((k) => k.toLowerCase());

        // Check if all keys in the combo are present in current keys
        return normalizedComboKeys.every((comboKey) =>
          normalizedCurrentKeys.includes(comboKey)
        );
      }

      // Detect if running on Debian Linux with Chromium browser
      function isDebianChromium() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isLinux = userAgent.includes("linux");
        const isChromium =
          userAgent.includes("chrome") || userAgent.includes("chromium");

        // Additional check for Debian-specific indicators if available
        const isDebian =
          userAgent.includes("debian") ||
          (isLinux &&
            !userAgent.includes("ubuntu") &&
            !userAgent.includes("fedora"));

        return isLinux && isChromium; // Assume Debian if Linux + Chromium
      }

      // Enhanced key down handler with comprehensive preventDefault logic
      async function handleKeyDown(event) {
        // Log key event for debugging (can be removed in production)
        console.log(
          `Key Event: key='${event.key}', code='${event.code}', ctrlKey=${event.ctrlKey}, altKey=${event.altKey}, metaKey=${event.metaKey}, shiftKey=${event.shiftKey}`
        );

        // Apply comprehensive preventDefault logic FIRST to ensure we capture all special keys
        const shouldPrevent = shouldPreventDefault(event);
        if (shouldPrevent) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation(); // Prevent other handlers from running
        }

        // Map the event to a key name for testing
        const keyName = mapEventToKeyName(event);

        // Check for system-level keys that may still trigger OS functions despite preventDefault
        const systemLevelKeys = [
          "PrintScreen",
          "Print",
          "Meta",
          "Super_L",
          "Super_R",
        ];
        const isSystemLevelKey =
          systemLevelKeys.includes(keyName) ||
          event.key === "PrintScreen" ||
          event.key === "Meta" ||
          event.code === "MetaLeft" ||
          event.code === "MetaRight";

        // Only proceed if we have a valid session and key mapping
        if (keyName && currentSessionId) {
          // Check if key is part of the layout we are testing
          if (keyDomElements[keyName]) {
            // Only send if not already pressed to avoid duplicate registrations
            if (!keyDomElements[keyName].classList.contains("pressed")) {
              try {
                const response = await fetch(
                  `/api/visual_test/input/${currentSessionId}`,
                  {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({
                      action: "key_press",
                      key: keyName,
                      // Include additional metadata for debugging
                      event_key: event.key,
                      event_code: event.code,
                      prevented: shouldPrevent,
                      system_level_key: isSystemLevelKey,
                    }),
                  }
                );
                if (!response.ok) {
                  const errorResult = await response.json();
                  console.error("Failed to send key press:", errorResult.error);
                }
                // The SSE will update the UI based on server response
              } catch (error) {
                console.error("Error sending key press:", error);
              }
            }
          } else {
            // Log unmapped keys for debugging (helpful for identifying missing mappings)
            console.warn(
              `Key '${keyName}' (from event: ${event.key}/${event.code}) not found in rendered layout.`
            );
          }
        } else if (!keyName) {
          // Log unmappable keys for debugging
          console.warn(
            `Could not map key event: key='${event.key}', code='${event.code}'`
          );
        }

        // Always return false to ensure no further default processing
        return false;
      }

      // Enhanced key up handler for completeness (captures key releases)
      function handleKeyUp(event) {
        // Apply the same preventDefault logic for consistency
        if (shouldPreventDefault(event)) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
        }

        // We don't need to send keyup events to the server for this test,
        // but we prevent default behavior to maintain comprehensive capture
        return false;
      }

      async function completeTest(isForced) {
        if (!currentSessionId || completing) return;
        completing = true;
        document.getElementById("testStatus").textContent =
          "Status: Completing...";
        try {
          const response = await fetch(
            `/api/visual_test/input/${currentSessionId}`,
            {
              method: "POST",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({
                action: "complete_test",
                force_complete: isForced,
              }),
            }
          );
          const result = await response.json();
          if (!response.ok)
            throw new Error(result.error || "Failed to complete test");

          // The SSE should send the final test_result, which calls handleTestComplete
          // If not, we might need to manually call it here with result.test_result
          if (result.test_result) {
            handleTestComplete(result.test_result);
          }
        } catch (error) {
          console.error("Error completing test:", error);
          document.getElementById("instructionText").textContent =
            "Error completing test: " + error.message;
          document.getElementById("testStatus").textContent =
            "Status: Error on completion";
        }
      }

      function handleTestComplete(result) {
        if (eventSource) {
          eventSource.close();
          eventSource = null;
        }
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
        // Remove all key event listeners
        document.removeEventListener("keydown", handleKeyDown, true);
        document.removeEventListener("keyup", handleKeyUp, true);
        document.getElementById("completeTestBtn").disabled = true;
        document.getElementById("forceCompleteBtn").disabled = true;

        const statusMsg = `Status: ${result.status.toUpperCase()}`;
        document.getElementById("testStatus").textContent = statusMsg;
        let notes = result.notes || "Test finished.";
        if (
          result.status.toLowerCase() === "pass" ||
          result.status.toLowerCase() === "launched"
        ) {
          const statusText =
            result.status.toLowerCase() === "pass" ? "PASSED" : "LAUNCHED";
          document.getElementById(
            "instructionText"
          ).innerHTML = `<b>Test ${statusText}!</b> ${notes}<br>This window will close automatically in 5 seconds.`;
          setTimeout(closeTest, 5000);
        } else {
          document.getElementById(
            "instructionText"
          ).innerHTML = `<b>Test ${result.status.toUpperCase()}</b>: ${notes}`;
          const unpressedDiv = document.getElementById("unpressedKeysList");
          const container = document.getElementById(
            "unpressedKeysListContainer"
          );
          if (result.untested_keys && result.untested_keys.length > 0) {
            unpressedDiv.textContent = result.untested_keys.join(", ");
            container.style.display = "block";
          } else {
            container.style.display = "none";
          }
        }
      }

      function closeTest() {
        if (eventSource) {
          eventSource.close();
          eventSource = null;
        }
        // Try to inform the backend the session is being closed by user action
        if (currentSessionId) {
          fetch(`/api/visual_test/stop/${currentSessionId}`, {
            method: "POST",
          }).catch((err) =>
            console.warn("Could not send stop signal on close:", err)
          );
        }

        if (window.opener && !window.opener.closed) {
          window.close();
        } else {
          // Fallback if window cannot be closed (e.g. not opened by script)
          // document.body.innerHTML = '<h1>Test Closed</h1><p>You can now close this tab/window.</p>';
          window.location.href = "/"; // Redirect to home or a generic page
        }
      }

      window.addEventListener("load", initTest);
      window.addEventListener("beforeunload", () => {
        if (eventSource) eventSource.close();
        // If test is running and user closes tab, try to mark as error or incomplete?
      });
    </script>
  </body>
</html>
