<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        /* Extends existing dashboard-card styling */
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--card-gap);
            padding: var(--container-padding);
        }

        .test-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            transition: border-color var(--transition-fast);
        }

        .test-card.running {
            border-color: var(--status-running);
            box-shadow: 0 0 8px rgba(33, 150, 243, 0.3);
        }

        .test-card.pass {
            border-color: var(--status-pass);
        }

        .test-card.fail {
            border-color: var(--status-fail);
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .test-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
        }

        .test-status-badge {
            padding: var(--space-xs) var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-sm);
            font-weight: 500;
            text-transform: uppercase;
        }

        .progress-section {
            margin: var(--space-md) 0;
        }

        .progress-bar-container {
            background: var(--bg-secondary);
            border-radius: var(--btn-border-radius);
            height: 8px;
            overflow: hidden;
            margin: var(--space-xs) 0;
        }

        .progress-bar {
            height: 100%;
            background: var(--status-running);
            transition: width var(--transition-normal);
            border-radius: var(--btn-border-radius);
        }

        .progress-bar.complete {
            background: var(--status-pass);
        }

        .test-details {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-top: var(--space-sm);
        }

        .test-logs {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--space-sm);
            max-height: 150px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: var(--font-size-xs);
            margin-top: var(--space-md);
        }

        .log-entry {
            margin: 2px 0;
            color: var(--text-secondary);
        }

        .log-timestamp {
            color: var(--text-muted);
            margin-right: var(--space-sm);
        }

        .control-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--space-md);
            box-shadow: var(--shadow-normal);
            z-index: 100;
        }

        .control-panel button {
            margin: var(--space-xs);
            padding: var(--btn-padding-sm);
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }

        .control-panel button:hover {
            background: var(--accent-secondary);
        }

        .system-info {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--space-md);
            margin-bottom: var(--space-lg);
        }

        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-sm);
            margin-top: var(--space-sm);
        }

        .hw-item {
            background: var(--bg-primary);
            padding: var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-sm);
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--status-pending);
            z-index: 1000;
            transition: background-color var(--transition-fast);
        }

        .connection-status.connected {
            background: var(--status-pass);
        }

        .connection-status.disconnected {
            background: var(--status-fail);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
                padding: var(--space-md);
            }
            
            .control-panel {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: var(--space-lg);
            }
        }
    </style>
</head>
<body>
    <div class="responsive-container">
        <!-- System Information Header -->
        <div class="system-info">
            <h2 style="margin: 0 0 var(--space-sm) 0; color: var(--text-primary);">
                Test Execution Dashboard
            </h2>
            <div class="hardware-grid" id="hardware-info">
                <div class="hw-item">Asset: <span id="asset-number">Loading...</span></div>
                <div class="hw-item">Operator: <span id="operator-id">Loading...</span></div>
                <div class="hw-item">Profile: <span id="test-profile">Loading...</span></div>
                <div class="hw-item">Status: <span id="overall-status">Initializing</span></div>
            </div>
        </div>

        <!-- Test Execution Grid -->
        <div class="test-grid" id="test-grid">
            <!-- Test cards will be populated dynamically -->
        </div>

        <!-- Control Panel -->
        <div class="control-panel">
            <button onclick="pauseTests()" id="pause-btn">Pause</button>
            <button onclick="resumeTests()" id="resume-btn">Resume</button>
            <button onclick="stopTests()" id="stop-btn">Stop All</button>
            <button onclick="returnToDashboard()">Return to Dashboard</button>
        </div>

        <!-- Connection Status Indicator -->
        <div class="connection-status" id="connection-status" title="Real-time connection status"></div>
    </div>

    <!-- Include WebSocket Manager -->
    <script src="{{ url_for('static', filename='js/modules/websocket_manager.js') }}"></script>
    
    <script>
        let currentAssetNumber = null;
        let testSessions = new Map();
        let overallTestStatus = 'initializing';

        // Initialize from URL parameters
        function initializeFromParams() {
            const params = new URLSearchParams(window.location.search);
            currentAssetNumber = params.get('asset_number') || 'UNKNOWN';
            const operatorId = params.get('operator_id') || 'UNKNOWN';
            const profileName = params.get('profile_name') || 'UNKNOWN';

            document.getElementById('asset-number').textContent = currentAssetNumber;
            document.getElementById('operator-id').textContent = operatorId;
            document.getElementById('test-profile').textContent = profileName;

            return { currentAssetNumber, operatorId, profileName };
        }

        // Create test card for each test type
        function createTestCard(testType, testName) {
            const card = document.createElement('div');
            card.className = 'test-card';
            card.id = `test-${testType}`;
            card.innerHTML = `
                <div class="test-header">
                    <div class="test-title">${testName}</div>
                    <div class="test-status-badge status-pending" id="status-${testType}">Pending</div>
                </div>
                <div class="progress-section">
                    <div class="progress-bar-container">
                        <div class="progress-bar" id="progress-${testType}" style="width: 0%"></div>
                    </div>
                    <div class="test-details" id="details-${testType}">Waiting to start...</div>
                </div>
                <div class="test-logs" id="logs-${testType}"></div>
            `;
            return card;
        }

        // Update test card with progress data
        function updateTestCard(testType, data) {
            const card = document.getElementById(`test-${testType}`);
            if (!card) return;

            const statusBadge = document.getElementById(`status-${testType}`);
            const progressBar = document.getElementById(`progress-${testType}`);
            const details = document.getElementById(`details-${testType}`);

            // Update status
            statusBadge.textContent = data.status || 'Running';
            statusBadge.className = `test-status-badge status-${(data.status || 'running').toLowerCase()}`;

            // Update progress
            if (data.progress && typeof data.progress.overall_progress === 'number') {
                progressBar.style.width = `${data.progress.overall_progress}%`;
                if (data.progress.overall_progress >= 100) {
                    progressBar.classList.add('complete');
                }
            }

            // Update details
            if (data.progress) {
                const detailsText = [];
                if (data.progress.operation_text) {
                    detailsText.push(data.progress.operation_text);
                }
                if (data.progress.time_left) {
                    detailsText.push(`Time left: ${Math.round(data.progress.time_left)}s`);
                }
                if (data.progress.cycles) {
                    detailsText.push(`Cycles: ${data.progress.cycles}`);
                }
                if (data.progress.errors !== undefined) {
                    detailsText.push(`Errors: ${data.progress.errors}`);
                }
                details.textContent = detailsText.join(' | ') || 'Running...';
            }

            // Update card styling
            card.className = `test-card ${(data.status || 'running').toLowerCase()}`;
        }

        // Add log entry to test card
        function addLogEntry(testType, message, timestamp = new Date()) {
            const logsContainer = document.getElementById(`logs-${testType}`);
            if (!logsContainer) return;

            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <span class="log-timestamp">${timestamp.toLocaleTimeString()}</span>
                <span class="log-message">${escapeHtml(message)}</span>
            `;

            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;

            // Limit log entries
            while (logsContainer.children.length > 20) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }

        // Setup real-time updates
        function setupRealTimeUpdates() {
            if (!currentAssetNumber) return;

            // Connect to WebSocket/SSE for real-time updates
            window.CrucibleWebSocket.connect(currentAssetNumber);

            // Listen for test progress updates
            window.CrucibleWebSocket.on('testProgress', (data) => {
                if (data.testType) {
                    updateTestCard(data.testType, data);
                }
            });

            // Listen for test logs
            window.CrucibleWebSocket.on('testLog', (data) => {
                // Parse log message to determine test type
                const testType = extractTestTypeFromLog(data.message);
                if (testType) {
                    addLogEntry(testType, data.message, data.timestamp);
                }
            });

            // Listen for test completion
            window.CrucibleWebSocket.on('testComplete', (data) => {
                updateOverallStatus('complete');
                showCompletionSummary(data);
            });

            // Update connection status
            window.CrucibleWebSocket.on('connected', () => {
                document.getElementById('connection-status').className = 'connection-status connected';
            });

            window.CrucibleWebSocket.on('disconnected', () => {
                document.getElementById('connection-status').className = 'connection-status disconnected';
            });
        }

        // Extract test type from log message
        function extractTestTypeFromLog(message) {
            const testTypes = ['cpu', 'ram', 'lcd', 'keyboard', 'pointing', 'touch'];
            for (const type of testTypes) {
                if (message.toLowerCase().includes(type)) {
                    return type;
                }
            }
            return 'general';
        }

        // Update overall status
        function updateOverallStatus(status) {
            overallTestStatus = status;
            document.getElementById('overall-status').textContent = status.charAt(0).toUpperCase() + status.slice(1);
            
            const statusElement = document.getElementById('overall-status');
            statusElement.className = `status-${status.toLowerCase()}`;
        }

        // Control functions
        function pauseTests() {
            // Implementation would depend on backend support
            console.log('Pause tests requested');
        }

        function resumeTests() {
            // Implementation would depend on backend support
            console.log('Resume tests requested');
        }

        function stopTests() {
            if (confirm('Are you sure you want to stop all tests?')) {
                window.CrucibleWebSocket.disconnect();
                updateOverallStatus('stopped');
            }
        }

        function returnToDashboard() {
            window.location.href = '/';
        }

        function showCompletionSummary(data) {
            // Create a summary modal or notification
            const summary = document.createElement('div');
            summary.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--bg-card);
                border: 2px solid var(--border-color);
                border-radius: var(--btn-border-radius);
                padding: var(--space-xl);
                z-index: 1000;
                max-width: 400px;
                text-align: center;
            `;
            
            summary.innerHTML = `
                <h3 style="color: var(--text-primary); margin-bottom: var(--space-md);">
                    Tests Complete
                </h3>
                <p style="color: var(--text-secondary); margin-bottom: var(--space-lg);">
                    All tests have finished. Check individual results above.
                </p>
                <button onclick="this.parentElement.remove(); returnToDashboard();" 
                        style="padding: var(--btn-padding-md); background: var(--accent-primary); 
                               color: white; border: none; border-radius: var(--btn-border-radius);">
                    Return to Dashboard
                </button>
            `;
            
            document.body.appendChild(summary);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize dashboard
        function initDashboard() {
            const { currentAssetNumber, operatorId, profileName } = initializeFromParams();
            
            // Create test cards for common test types
            const testTypes = [
                { type: 'cpu', name: 'CPU Test' },
                { type: 'ram', name: 'RAM Test' },
                { type: 'lcd', name: 'Display Test' },
                { type: 'keyboard', name: 'Keyboard Test' },
                { type: 'pointing', name: 'Pointing Device' },
                { type: 'touch', name: 'Touch Screen' }
            ];

            const testGrid = document.getElementById('test-grid');
            testTypes.forEach(test => {
                const card = createTestCard(test.type, test.name);
                testGrid.appendChild(card);
            });

            setupRealTimeUpdates();
            updateOverallStatus('ready');
        }

        // Start when page loads
        window.addEventListener('load', initDashboard);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (window.CrucibleWebSocket) {
                window.CrucibleWebSocket.disconnect();
            }
        });
    </script>
</body>
</html>
