<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Asset Entry - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .asset-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--container-padding);
            max-width: 800px;
            margin: 0 auto;
            width: 100%;
        }

        .asset-header {
            text-align: center;
            margin-bottom: var(--space-2xl);
        }

        .asset-title {
            font-size: var(--font-size-3xl);
            font-weight: 600;
            margin-bottom: var(--space-md);
            color: var(--accent-primary);
        }

        .asset-subtitle {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--space-xl);
        }

        .hardware-summary {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            margin-bottom: var(--space-2xl);
            width: 100%;
            max-width: 600px;
        }

        .hardware-summary h3 {
            margin: 0 0 var(--space-md) 0;
            color: var(--text-primary);
            font-size: var(--font-size-lg);
            display: flex;
            align-items: center;
        }

        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-sm);
        }

        .hardware-item {
            background: var(--bg-secondary);
            padding: var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-sm);
            text-align: center;
        }

        .entry-form {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            width: 100%;
            max-width: 600px;
            margin-bottom: var(--space-2xl);
        }

        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-label {
            display: block;
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--space-md);
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            color: var(--text-primary);
            font-size: var(--font-size-base);
            transition: border-color var(--transition-fast);
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--accent-primary);
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .form-input.valid {
            border-color: var(--status-pass);
        }

        .form-input.invalid {
            border-color: var(--status-fail);
        }

        .input-group {
            position: relative;
        }

        .input-addon {
            position: absolute;
            right: var(--space-sm);
            top: 50%;
            transform: translateY(-50%);
            background: var(--accent-secondary);
            color: white;
            border: none;
            border-radius: calc(var(--btn-border-radius) / 2);
            padding: var(--space-xs) var(--space-sm);
            font-size: var(--font-size-sm);
            cursor: pointer;
            transition: background-color var(--transition-fast);
        }

        .input-addon:hover {
            background: #1976D2;
        }

        .validation-message {
            font-size: var(--font-size-sm);
            margin-top: var(--space-xs);
            padding: var(--space-xs) var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            display: none;
        }

        .validation-message.error {
            background: rgba(244, 67, 54, 0.1);
            color: var(--status-fail);
            border: 1px solid var(--status-fail);
            display: block;
        }

        .validation-message.success {
            background: rgba(76, 175, 80, 0.1);
            color: var(--status-pass);
            border: 1px solid var(--status-pass);
            display: block;
        }

        .validation-message.info {
            background: rgba(33, 150, 243, 0.1);
            color: var(--accent-secondary);
            border: 1px solid var(--accent-secondary);
            display: block;
        }

        .device-condition-section {
            margin-top: var(--space-lg);
        }

        .condition-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-sm);
            margin-top: var(--space-sm);
        }

        .condition-option {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--space-sm);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: var(--font-size-sm);
        }

        .condition-option:hover {
            background: var(--bg-card);
            border-color: var(--accent-primary);
        }

        .condition-option.selected {
            background: var(--accent-primary);
            color: white;
            border-color: var(--accent-primary);
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            margin-top: var(--space-xl);
        }

        .btn {
            padding: var(--btn-padding-md);
            border: none;
            border-radius: var(--btn-border-radius);
            font-size: var(--btn-font-size);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            min-width: 140px;
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-card);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .shortcuts-help {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--space-sm);
            font-size: var(--font-size-xs);
            color: var(--text-muted);
            max-width: 200px;
        }

        .shortcuts-help h4 {
            margin: 0 0 var(--space-xs) 0;
            color: var(--text-primary);
            font-size: var(--font-size-sm);
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            margin: 2px 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .shortcuts-help {
                position: relative;
                bottom: auto;
                right: auto;
                margin-top: var(--space-lg);
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <div class="asset-container">
        <!-- Header -->
        <div class="asset-header">
            <h1 class="asset-title">Asset Entry</h1>
            <p class="asset-subtitle">Enter device information to begin testing</p>
        </div>

        <!-- Hardware Summary (populated from detection) -->
        <div class="hardware-summary" id="hardware-summary" style="display: none;">
            <h3>
                <span style="margin-right: var(--space-sm);">🖥️</span>
                Detected Hardware
            </h3>
            <div class="hardware-grid" id="hardware-grid">
                <!-- Populated dynamically -->
            </div>
        </div>

        <!-- Entry Form -->
        <div class="entry-form">
            <form id="asset-form">
                <!-- Asset Number -->
                <div class="form-group">
                    <label class="form-label" for="asset-number">
                        Asset Number *
                        <small style="font-weight: normal; color: var(--text-muted);">(Scan barcode or type)</small>
                    </label>
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="asset-number" 
                            class="form-input" 
                            placeholder="Enter or scan asset number..."
                            autocomplete="off"
                            autofocus
                        >
                        <button type="button" class="input-addon" onclick="focusAssetInput()" title="Focus for barcode scanner">
                            📷
                        </button>
                    </div>
                    <div class="validation-message" id="asset-validation"></div>
                </div>

                <!-- Operator ID -->
                <div class="form-group">
                    <label class="form-label" for="operator-id">
                        Operator ID *
                        <small style="font-weight: normal; color: var(--text-muted);">(Your technician ID)</small>
                    </label>
                    <input 
                        type="text" 
                        id="operator-id" 
                        class="form-input" 
                        placeholder="Enter your operator ID..."
                        autocomplete="off"
                    >
                    <div class="validation-message" id="operator-validation"></div>
                </div>

                <!-- Device Condition -->
                <div class="form-group device-condition-section">
                    <label class="form-label">
                        Device Condition *
                        <small style="font-weight: normal; color: var(--text-muted);">(Physical condition assessment)</small>
                    </label>
                    <div class="condition-grid">
                        <div class="condition-option" data-condition="excellent" onclick="selectCondition('excellent')">
                            <div>✨ Excellent</div>
                            <small>Like new</small>
                        </div>
                        <div class="condition-option" data-condition="good" onclick="selectCondition('good')">
                            <div>✅ Good</div>
                            <small>Minor wear</small>
                        </div>
                        <div class="condition-option" data-condition="fair" onclick="selectCondition('fair')">
                            <div>⚠️ Fair</div>
                            <small>Visible wear</small>
                        </div>
                        <div class="condition-option" data-condition="poor" onclick="selectCondition('poor')">
                            <div>❌ Poor</div>
                            <small>Significant damage</small>
                        </div>
                    </div>
                    <div class="validation-message" id="condition-validation"></div>
                </div>
            </form>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-secondary" onclick="goBack()">
                ← Back to Detection
            </button>
            <button class="btn btn-primary" onclick="proceedToTesting()" id="proceed-btn" disabled>
                Continue to Testing →
            </button>
        </div>
    </div>

    <!-- Keyboard Shortcuts Help -->
    <div class="shortcuts-help">
        <h4>Keyboard Shortcuts</h4>
        <div class="shortcut-item">
            <span>Tab</span>
            <span>Next field</span>
        </div>
        <div class="shortcut-item">
            <span>Enter</span>
            <span>Continue</span>
        </div>
        <div class="shortcut-item">
            <span>Esc</span>
            <span>Go back</span>
        </div>
        <div class="shortcut-item">
            <span>F1</span>
            <span>Focus asset</span>
        </div>
    </div>

    <script>
        let formData = {
            assetNumber: '',
            operatorId: '',
            deviceCondition: ''
        };

        let hardwareData = null;

        // Initialize page
        function initializePage() {
            loadHardwareData();
            setupEventListeners();
            setupValidation();
            
            // Focus asset input for immediate barcode scanning
            document.getElementById('asset-number').focus();
        }

        // Load hardware data from previous detection
        function loadHardwareData() {
            try {
                const stored = localStorage.getItem('crucibleHardwareData');
                if (stored) {
                    hardwareData = JSON.parse(stored);
                    displayHardwareSummary();
                }
            } catch (error) {
                console.log('No hardware data available:', error);
            }
        }

        // Display hardware summary
        function displayHardwareSummary() {
            if (!hardwareData) return;

            const summarySection = document.getElementById('hardware-summary');
            const hardwareGrid = document.getElementById('hardware-grid');

            const summary = {
                'Resolution': `${hardwareData.screen?.width || 'Unknown'}x${hardwareData.screen?.height || 'Unknown'}`,
                'Memory': hardwareData.memory ? `${hardwareData.memory.jsHeapSizeLimit} MB` : 'Unknown',
                'CPU Cores': hardwareData.cpu?.logicalProcessors || 'Unknown',
                'Graphics': hardwareData.capabilities?.webgl ? 'WebGL' : 'Canvas',
                'Touch': hardwareData.capabilities?.touch ? 'Yes' : 'No',
                'Network': hardwareData.platform?.onLine ? 'Online' : 'Offline'
            };

            hardwareGrid.innerHTML = Object.entries(summary)
                .map(([key, value]) => `
                    <div class="hardware-item">
                        <div style="font-weight: 600; margin-bottom: 2px;">${key}</div>
                        <div style="color: var(--text-secondary); font-size: var(--font-size-xs);">${value}</div>
                    </div>
                `).join('');

            summarySection.style.display = 'block';
        }

        // Setup event listeners
        function setupEventListeners() {
            const assetInput = document.getElementById('asset-number');
            const operatorInput = document.getElementById('operator-id');

            // Asset number input
            assetInput.addEventListener('input', (e) => {
                formData.assetNumber = e.target.value.trim();
                validateAssetNumber();
                updateProceedButton();
            });

            assetInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && validateAssetNumber()) {
                    operatorInput.focus();
                }
            });

            // Operator ID input
            operatorInput.addEventListener('input', (e) => {
                formData.operatorId = e.target.value.trim();
                validateOperatorId();
                updateProceedButton();
            });

            operatorInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' && validateOperatorId()) {
                    // Focus first condition option
                    document.querySelector('.condition-option').focus();
                }
            });

            // Global keyboard shortcuts
            document.addEventListener('keydown', (e) => {
                switch (e.key) {
                    case 'F1':
                        e.preventDefault();
                        focusAssetInput();
                        break;
                    case 'Enter':
                        if (e.target.tagName !== 'INPUT' && isFormValid()) {
                            proceedToTesting();
                        }
                        break;
                    case 'Escape':
                        goBack();
                        break;
                }
            });
        }

        // Setup real-time validation
        function setupValidation() {
            // Validation patterns
            window.validationPatterns = {
                assetNumber: /^[A-Z0-9]{3,20}$/i,
                operatorId: /^[A-Z0-9]{2,10}$/i
            };
        }

        // Validate asset number
        function validateAssetNumber() {
            const input = document.getElementById('asset-number');
            const validation = document.getElementById('asset-validation');
            const value = formData.assetNumber;

            if (!value) {
                input.className = 'form-input';
                validation.className = 'validation-message';
                return false;
            }

            if (value.length < 3) {
                input.className = 'form-input invalid';
                validation.className = 'validation-message error';
                validation.textContent = 'Asset number must be at least 3 characters';
                return false;
            }

            if (!window.validationPatterns.assetNumber.test(value)) {
                input.className = 'form-input invalid';
                validation.className = 'validation-message error';
                validation.textContent = 'Asset number should contain only letters and numbers';
                return false;
            }

            input.className = 'form-input valid';
            validation.className = 'validation-message success';
            validation.textContent = '✓ Valid asset number';
            return true;
        }

        // Validate operator ID
        function validateOperatorId() {
            const input = document.getElementById('operator-id');
            const validation = document.getElementById('operator-validation');
            const value = formData.operatorId;

            if (!value) {
                input.className = 'form-input';
                validation.className = 'validation-message';
                return false;
            }

            if (value.length < 2) {
                input.className = 'form-input invalid';
                validation.className = 'validation-message error';
                validation.textContent = 'Operator ID must be at least 2 characters';
                return false;
            }

            if (!window.validationPatterns.operatorId.test(value)) {
                input.className = 'form-input invalid';
                validation.className = 'validation-message error';
                validation.textContent = 'Operator ID should contain only letters and numbers';
                return false;
            }

            input.className = 'form-input valid';
            validation.className = 'validation-message success';
            validation.textContent = '✓ Valid operator ID';
            return true;
        }

        // Select device condition
        function selectCondition(condition) {
            // Remove previous selection
            document.querySelectorAll('.condition-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Select new condition
            const selectedOption = document.querySelector(`[data-condition="${condition}"]`);
            selectedOption.classList.add('selected');
            
            formData.deviceCondition = condition;
            
            // Clear any previous validation message
            const validation = document.getElementById('condition-validation');
            validation.className = 'validation-message success';
            validation.textContent = `✓ Condition set to ${condition}`;
            
            updateProceedButton();
        }

        // Check if form is valid
        function isFormValid() {
            return validateAssetNumber() && 
                   validateOperatorId() && 
                   formData.deviceCondition;
        }

        // Update proceed button state
        function updateProceedButton() {
            const proceedBtn = document.getElementById('proceed-btn');
            proceedBtn.disabled = !isFormValid();
        }

        // Focus asset input (for barcode scanner)
        function focusAssetInput() {
            const input = document.getElementById('asset-number');
            input.focus();
            input.select();
        }

        // Go back to hardware detection
        function goBack() {
            window.location.href = '/hardware_detection';
        }

        // Proceed to main testing interface
        function proceedToTesting() {
            if (!isFormValid()) {
                // Show validation errors
                validateAssetNumber();
                validateOperatorId();
                
                if (!formData.deviceCondition) {
                    const validation = document.getElementById('condition-validation');
                    validation.className = 'validation-message error';
                    validation.textContent = 'Please select a device condition';
                }
                return;
            }

            // Store form data for main dashboard
            const assetData = {
                assetNumber: formData.assetNumber,
                operatorId: formData.operatorId,
                deviceCondition: formData.deviceCondition,
                timestamp: new Date().toISOString(),
                hardwareData: hardwareData
            };

            if (typeof(Storage) !== "undefined") {
                localStorage.setItem('crucibleAssetData', JSON.stringify(assetData));
            }

            // Navigate to main dashboard with parameters
            const params = new URLSearchParams({
                asset_number: formData.assetNumber,
                operator_id: formData.operatorId,
                device_condition: formData.deviceCondition
            });

            window.location.href = `/? ${params.toString()}`;
        }

        // Auto-fill from URL parameters (if coming from main dashboard)
        function autoFillFromParams() {
            const params = new URLSearchParams(window.location.search);
            
            const assetNumber = params.get('asset_number');
            const operatorId = params.get('operator_id');
            
            if (assetNumber) {
                document.getElementById('asset-number').value = assetNumber;
                formData.assetNumber = assetNumber;
                validateAssetNumber();
            }
            
            if (operatorId) {
                document.getElementById('operator-id').value = operatorId;
                formData.operatorId = operatorId;
                validateOperatorId();
            }
            
            updateProceedButton();
        }

        // Initialize when page loads
        window.addEventListener('load', () => {
            initializePage();
            autoFillFromParams();
        });
    </script>
</body>
</html>
