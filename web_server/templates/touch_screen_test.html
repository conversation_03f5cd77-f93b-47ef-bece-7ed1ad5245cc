<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Touch Screen Test - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #111;
            color: #fff;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between; /* Push controls to bottom */
            touch-action: none; /* Disable browser default touch actions like pinch-zoom page */
        }
        #touchCanvas {
            border: 1px solid var(--border-color);
            background-color: #111; /* Darker for touch, good contrast */
            /* Canvas will be sized by J<PERSON> to fill available space */
            flex-grow: 0; align-self: center; /* Allow canvas to take up space */
            /* width handled by <PERSON><PERSON> to sync CSS and intrinsic size */
        }
        .touch-test-info-bar {
            position: fixed;
            bottom: 60px; /* Above main controls */
            left: 10px;
            right: 10px;
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap; /* Allow items to wrap on smaller screens */
            padding: 8px;
            background-color: rgba(0,0,0,0.8);
            border-radius: 5px;
            font-size: 0.85em; /* Slightly smaller for more items */
            z-index: 100;
            color: white; /* Ensure text is visible */
        }
        .info-item { margin: 2px 8px; }
        .info-item.pass { color: lightgreen; }
        .info-item.fail { color: lightcoral; }
        .info-item.neutral { color: lightgray; }
        .info-item.active { color: yellow; }


        .touch-test-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            padding: 10px;
            width: 100%;
            background-color: #111;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 100;
        }
        .instructions-header {
            position: fixed;
            top: 10px;
            left: 50%;
            transform: translateX(-50%);
            padding: 8px 15px;
            background-color: rgba(0,0,0,0.8);
            border-radius: 5px;
            font-size: 1em;
            z-index: 100;
            text-align: center;
            color: white;
        }
    .touch-test-controls button {
            background-color: #444;
            color: #fff;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 1em;
        }
        .touch-test-controls button:hover {
            background-color: #555;
        }
    </style>
</head>
<body>
    <div id="instructionsHeader" class="instructions-header">
        Touch the targets. Then use two fingers for multi-touch & pinch/zoom. Press ESC to complete.
    </div>

    <canvas id="touchCanvas"></canvas>

    <div class="touch-test-info-bar">
        <span id="infoTargetsHit" class="info-item neutral">Targets: 0/5</span>
        <span id="infoMultiTouch" class="info-item neutral">Multi-touch: No</span>
        <span id="infoPinch" class="info-item neutral">Pinch: No</span>
        <span id="infoZoom" class="info-item neutral">Zoom: No</span>
        <span id="infoTouchPoints" class="info-item neutral">Touches: 0</span>
    </div>

    <div class="touch-test-controls">
        <button id="forceCompleteBtn" class="btn btn-danger">Force Complete (Issue)</button>
        <button id="completeTestBtn" class="btn btn-primary">Test Finished</button>
    </div>

    <script>
        let currentSessionId = null;
        let eventSource = null;
        const canvas = document.getElementById('touchCanvas');
        const ctx = canvas.getContext('2d');

        const NUM_TARGETS = 5;
        let targets = []; // Stores {id, x, y, radius, status ('unhit', 'hit')}
        let feedbackCircle = { x: 0, y: 0, radius: 100, baseRadius: 100, color: 'cyan', visible: false };

        // For gesture detection
        let initialTouchDistance = 0;
        let activeTouches = new Map(); // Store active touch identifiers and their initial positions

        // Phase control for simplified technician workflow
        const PHASES = { TOUCH: 'touch', MULTI: 'multi_touch', PINCH: 'pinch' };
        let currentPhase = PHASES.TOUCH;
        const phaseCompleted = { touch: false, multi_touch: false, pinch: false };

        function setPhase(newPhase) {
            currentPhase = newPhase;
            updatePhaseUI();
            refreshInfoBar();
        }

        function updatePhaseUI() {
            const instructions = document.getElementById('instructionsHeader');
            switch (currentPhase) {
                case PHASES.TOUCH:
                    instructions.textContent = 'Phase 1: Tap each red circle.';
                    break;
                case PHASES.MULTI:
                    instructions.textContent = 'Phase 2: Place two fingers on the screen together.';
                    break;
                case PHASES.PINCH:
                    instructions.textContent = 'Phase 3: Pinch in or out to resize the cyan circle.';
                    break;
            }
        }

        function refreshInfoBar() {
            const targetsHitCount = targets.filter(t => t.status === 'hit').length;
            document.getElementById('infoTargetsHit').textContent = `Targets: ${targetsHitCount}/${NUM_TARGETS}`;
            document.getElementById('infoTargetsHit').className = 'info-item ' + (phaseCompleted.touch ? 'pass' : (currentPhase === PHASES.TOUCH ? 'active' : 'neutral'));

            document.getElementById('infoMultiTouch').textContent = `Multi-touch: ${phaseCompleted.multi_touch ? 'Yes' : 'No'}`;
            document.getElementById('infoMultiTouch').className = 'info-item ' + (phaseCompleted.multi_touch ? 'pass' : (currentPhase === PHASES.MULTI ? 'active' : 'neutral'));

            document.getElementById('infoPinch').textContent = `Pinch: ${phaseCompleted.pinch ? 'Yes' : 'No'}`;
            document.getElementById('infoPinch').className = 'info-item ' + (phaseCompleted.pinch ? 'pass' : (currentPhase === PHASES.PINCH ? 'active' : 'neutral'));

            // Hide the extra zoom details for a cleaner UI
            document.getElementById('infoZoom').style.display = 'none';
            document.getElementById('infoTouchPoints').textContent = `Touches: ${activeTouches.size}`;
        }

        function checkTouchPhaseCompletion() {
            if (!phaseCompleted.touch && targets.every(t => t.status === 'hit')) {
                phaseCompleted.touch = true;
                sendEventToServer({ action: 'phase_complete', phase: 'touch' });
                setPhase(PHASES.MULTI);
            }
        }

        function checkMultiTouchCompletion(touchCount) {
            if (currentPhase === PHASES.MULTI && touchCount >= 2 && !phaseCompleted.multi_touch) {
                phaseCompleted.multi_touch = true;
                sendEventToServer({ action: 'phase_complete', phase: 'multi_touch' });
                setPhase(PHASES.PINCH);
            }
        }

        function resizeCanvas() {
            // Adjust canvas size, leaving space for info bar and controls
            const headerHeight = document.getElementById('instructionsHeader').offsetHeight + 20; // 20 for margin
            const controlsHeight = document.querySelector('.touch-test-controls').offsetHeight + 10;
            const infoBarHeight = document.querySelector('.touch-test-info-bar').offsetHeight + 10;

            const availW = window.innerWidth;
            const availH = window.innerHeight - headerHeight - controlsHeight - infoBarHeight;
            const size = Math.min(availW, availH);
            canvas.width = size;
            canvas.height = size;
            // Ensure CSS size matches intrinsic size to avoid distortion
            canvas.style.width = `${canvas.width}px`;
            canvas.style.height = `${canvas.height}px`;

            defineTargets(); // Recalculate target positions
            feedbackCircle.x = canvas.width / 2;
            feedbackCircle.y = canvas.height / 2;
            drawTouchInterface();
        }

        function defineTargets() {
            targets = [];
            const W = canvas.width;
            const H = canvas.height;
            const targetRadius = Math.min(W, H) * 0.08; // Responsive radius

            const positions = [
                { x: W * 0.2, y: H * 0.2 },  // Top-left
                { x: W * 0.8, y: H * 0.2 },  // Top-right
                { x: W * 0.5, y: H * 0.5 },  // Center
                { x: W * 0.2, y: H * 0.8 },  // Bottom-left
                { x: W * 0.8, y: H * 0.8 }   // Bottom-right
            ];

            for (let i = 0; i < NUM_TARGETS; i++) {
                targets.push({
                    id: `target_${i + 1}`,
                    x: positions[i].x,
                    y: positions[i].y,
                    radius: targetRadius,
                    status: 'unhit'
                });
            }
        }

        function drawTouchInterface() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = `${Math.min(canvas.width, canvas.height) * 0.03}px Arial`; // Responsive font
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // Draw Targets
            targets.forEach(target => {
                ctx.beginPath();
                ctx.arc(target.x, target.y, target.radius, 0, Math.PI * 2);
                ctx.fillStyle = target.status === 'hit' ? 'green' : 'red';
                ctx.fill();
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.fillText((targets.indexOf(target) + 1).toString(), target.x, target.y);
            });

            // Draw Feedback Circle for gestures (if active)
            if (feedbackCircle.visible) {
                ctx.beginPath();
                ctx.arc(feedbackCircle.x, feedbackCircle.y, feedbackCircle.radius, 0, Math.PI * 2);
                ctx.strokeStyle = feedbackCircle.color;
                ctx.lineWidth = 3;
                ctx.stroke();
            }
        }

        function updateInfoBar(progress) {
            document.getElementById('infoTargetsHit').textContent = `Targets: ${progress.targets_hit_count || 0}/${progress.targets_total || NUM_TARGETS}`;
            document.getElementById('infoTargetsHit').className = (progress.targets_hit_count === (progress.targets_total || NUM_TARGETS)) ? 'info-item pass' : 'info-item neutral';

            document.getElementById('infoMultiTouch').textContent = `Multi-touch: ${progress.multi_touch_detected ? 'Yes' : 'No'}`;
            document.getElementById('infoMultiTouch').className = progress.multi_touch_detected ? 'info-item pass' :
                                                                (progress.active_touch_points >= 2 ? 'info-item active' : 'info-item neutral');

            // Main indicator for gesture phase completion
            document.getElementById('infoPinch').textContent = `Gesture: ${progress.gesture_performed ? 'Performed' : 'No'}`;
            document.getElementById('infoPinch').className = progress.gesture_performed ? 'info-item pass' :
                                                            (progress.multi_touch_detected ? 'info-item active': 'info-item neutral');

            // Specific indicator for zoom (can be combined or kept separate for detail)
            // For simplicity, we'll change the 'Zoom' label to show specific pinch/zoom events if desired,
            // or it can be removed if 'Gesture: Performed' is enough.
            let gestureDetails = "";
            if (progress.pinch_event_detected) gestureDetails += "Pinch ";
            if (progress.zoom_event_detected) gestureDetails += "Zoom ";
            if (gestureDetails === "") gestureDetails = "None";

            document.getElementById('infoZoom').textContent = `Events: ${gestureDetails.trim()}`;
            document.getElementById('infoZoom').className = (progress.pinch_event_detected || progress.zoom_event_detected) ? 'info-item active' : 'info-item neutral';
            if(progress.gesture_performed) document.getElementById('infoZoom').className = 'info-item pass';


            document.getElementById('infoTouchPoints').textContent = `Touches: ${progress.active_touch_points || 0}`;
        }

        async function initTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'TOUCHTEST001';

            resizeCanvas(); // Initial size and target definition
            window.addEventListener('resize', resizeCanvas);
            updatePhaseUI();
            refreshInfoBar();

            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test_type: 'touch', asset_number: assetNumber, test_params: {} })
                });
                const result = await response.json();
                if (!response.ok) throw new Error(result.error || 'Failed to start touch test');
                currentSessionId = result.session_id;
                connectToProgressStream();
            } catch (error) {
                console.error('Error starting test:', error);
                document.getElementById('instructionsHeader').textContent = 'Error: ' + error.message;
            }

            document.getElementById('completeTestBtn').addEventListener('click', () => completeTest(false));
            document.getElementById('forceCompleteBtn').addEventListener('click', () => completeTest(true));

            setupTouchListeners();
        }

        function connectToProgressStream() {
            if (!currentSessionId) return;
            eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.error) { console.error('SSE Error:', data.error); return; }

                // Update local target statuses from backend progress
                if (data.progress && data.progress.targets_status) {
                    targets.forEach(t => {
                        t.status = data.progress.targets_status[t.id] || 'unhit';
                    });
                }
                feedbackCircle.visible = data.progress.multi_touch_detected || data.progress.pinch_detected || data.progress.zoom_detected;

                updateInfoBar(data.progress);
                drawTouchInterface(); // Redraw based on updated statuses

                if (data.test_complete) handleTestComplete(data.test_result);
            };
            eventSource.onerror = function(err) {
                console.error('EventSource failed:', err);
                if (eventSource) eventSource.close();
            };
        }

        function setupTouchListeners() {
            canvas.addEventListener('touchstart', handleTouchStart, { passive: false });
            canvas.addEventListener('touchmove', handleTouchMove, { passive: false });
            canvas.addEventListener('touchend', handleTouchEnd, { passive: false });
            canvas.addEventListener('touchcancel', handleTouchEnd, { passive: false }); // Treat cancel like end
        }

        function handleTouchStart(event) {
            event.preventDefault();
            updateActiveTouches(event.touches);

            Array.from(event.changedTouches).forEach(touch => {
                const x = touch.clientX - canvas.offsetLeft;
                const y = touch.clientY - canvas.offsetTop;

                let hitTargetId = null;
                targets.forEach(target => {
                    if (currentPhase === PHASES.TOUCH && target.status === 'unhit') {
                        const dist = Math.sqrt(Math.pow(x - target.x, 2) + Math.pow(y - target.y, 2));
                        if (dist <= target.radius) {
                            hitTargetId = target.id;
                            // Optimistically update local status for immediate feedback
                            target.status = 'hit';
                        }
                    }
                });
                sendEventToServer({
                    action: 'touch_event',
                    target_id: hitTargetId,
                    touch_points_count: event.touches.length,
                    coordinates: { x, y } // Can be used for drawing touch points if needed
                });
            });
            if (event.touches.length >= 2) {
                 initializeGesture(event.touches);
            }
            checkTouchPhaseCompletion();
            checkMultiTouchCompletion(event.touches.length);
            refreshInfoBar();
            drawTouchInterface(); // For immediate feedback on hit target
        }

        function handleTouchMove(event) {
            event.preventDefault();
            updateActiveTouches(event.touches);

            if (event.touches.length >= 2) {
                processGesture(event.touches);
            }
            // Optionally send move events if needed for drawing paths or detailed tracking
        }

        function handleTouchEnd(event) {
            event.preventDefault();
            updateActiveTouches(event.touches); // Update based on remaining touches

            if (event.touches.length < 2) {
                resetGesture();
            }
        }

        function updateActiveTouches(touchList) {
             // Update map of active touches
            const currentTouchIds = new Set();
            Array.from(touchList).forEach(t => currentTouchIds.add(t.identifier));

            activeTouches.forEach((_, id) => {
                if (!currentTouchIds.has(id)) {
                    activeTouches.delete(id);
                }
            });
            Array.from(touchList).forEach(t => {
                if (!activeTouches.has(t.identifier)) {
                     activeTouches.set(t.identifier, {x: t.clientX, y: t.clientY});
                }
            });
            // Notify backend about the change in number of active touch points
            // This can be part of another event or a dedicated one if needed by backend logic.
            // For now, 'touch_event' sent on touchstart includes touch_points_count.
            refreshInfoBar();
        }

        function initializeGesture(touches) {
            if (touches.length < 2) return;
            const t0 = touches[0];
            const t1 = touches[1];
            initialTouchDistance = Math.sqrt(Math.pow(t1.clientX - t0.clientX, 2) + Math.pow(t1.clientY - t0.clientY, 2));
            feedbackCircle.baseRadius = 100; // Reset feedback circle base size
            feedbackCircle.radius = feedbackCircle.baseRadius;
            feedbackCircle.visible = true;
            drawTouchInterface();
        }

        function processGesture(touches) {
            if (touches.length < 2 || initialTouchDistance === 0) return;
            const t0 = touches[0];
            const t1 = touches[1];
            const currentDistance = Math.sqrt(Math.pow(t1.clientX - t0.clientX, 2) + Math.pow(t1.clientY - t0.clientY, 2));

            const deltaDistance = currentDistance - initialTouchDistance;
            const scaleFactor = currentDistance / initialTouchDistance;

            feedbackCircle.radius = feedbackCircle.baseRadius * scaleFactor;
            feedbackCircle.radius = Math.max(20, Math.min(feedbackCircle.radius, Math.min(canvas.width, canvas.height) / 2 - 20)); // Clamp size


            let gestureType = null;
            if (Math.abs(deltaDistance) > 30) { // Threshold for detecting a gesture
                if (deltaDistance > 0) gestureType = 'zoom'; // Fingers moved apart
                else gestureType = 'pinch'; // Fingers moved closer

                sendEventToServer({ action: 'gesture_event', gesture_type: gestureType, scale: scaleFactor });
                if (currentPhase === PHASES.PINCH && !phaseCompleted.pinch) {
                    phaseCompleted.pinch = true;
                    sendEventToServer({ action: 'phase_complete', phase: 'pinch' });
                    refreshInfoBar();
                    completeTest(false);
                }
                // Don't reset initialTouchDistance here, allow continuous gestures
            }
            drawTouchInterface();
        }

        function resetGesture() {
            initialTouchDistance = 0;
            feedbackCircle.visible = false; // Hide if not multi-touching
            drawTouchInterface();
        }

        async function sendEventToServer(eventData) {
            if (!currentSessionId) return;
            try {
                const response = await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(eventData)
                });
                
                if (!response.ok) {
                    console.error('Server error:', response.status, response.statusText);
                }
            } catch (error) {
                console.error('Error sending touch event to server:', error);
            }
        }

        async function completeTest(isForced) {
            if (!currentSessionId) return;
            document.getElementById('instructionsHeader').textContent = 'Completing test...';
            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'complete_test', force_complete: isForced })
                });
            } catch (error) {
                console.error('Error completing test:', error);
            }
        }

        function handleTestComplete(result) {
            if (eventSource) { eventSource.close(); eventSource = null; }

            canvas.removeEventListener('touchstart', handleTouchStart);
            canvas.removeEventListener('touchmove', handleTouchMove);
            canvas.removeEventListener('touchend', handleTouchEnd);
            canvas.removeEventListener('touchcancel', handleTouchEnd);

            document.getElementById('completeTestBtn').disabled = true;
            document.getElementById('forceCompleteBtn').disabled = true;

            let resultMessage = `Test ${result.status.toUpperCase()}. Notes: ${result.notes}`;
            document.getElementById('instructionsHeader').textContent = resultMessage;

            if (['pass', 'skipped', 'not_applicable'].includes((result.status || '').toLowerCase()) || result.force_completed) {
                 setTimeout(closeWindow, 5000);
            }

            // Proactively ask backend to persist the result now that we are done
            try {
                if (currentSessionId) {
                    fetch(`/api/visual_test/stop/${currentSessionId}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ result: 'completed' })
                    }).catch(() => {});
                }
            } catch (e) {
                // Non-fatal: persistence is also handled by orchestrator/other flows
                console.warn('Unable to notify backend to persist result:', e);
            }
        }

        function closeWindow() {
            if (window.opener && !window.opener.closed) { window.close(); }
            else { window.location.href = '/'; }
        }

        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') completeTest(false);
        });

        window.addEventListener('load', initTest);
        window.addEventListener('beforeunload', () => { if (eventSource) eventSource.close(); });

    </script>
</body>
</html>
