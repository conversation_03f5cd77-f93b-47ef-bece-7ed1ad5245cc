<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Profile Selection - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .profile-container {
            flex: 1;
            padding: var(--container-padding);
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .profile-header {
            text-align: center;
            margin-bottom: var(--space-2xl);
        }

        .profile-title {
            font-size: var(--font-size-3xl);
            font-weight: 600;
            margin-bottom: var(--space-md);
            color: var(--accent-primary);
        }

        .profile-subtitle {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--space-xl);
        }

        .asset-info {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            margin-bottom: var(--space-2xl);
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
        }

        .info-item {
            text-align: center;
        }

        .info-label {
            font-size: var(--font-size-sm);
            color: var(--text-muted);
            margin-bottom: var(--space-xs);
        }

        .info-value {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
        }

        .hardware-compatibility {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            margin-bottom: var(--space-2xl);
        }

        .compatibility-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
        }

        .compatibility-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-sm);
        }

        .compatibility-item {
            background: var(--bg-secondary);
            padding: var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            text-align: center;
            font-size: var(--font-size-sm);
            border: 1px solid var(--border-color);
        }

        .compatibility-item.supported {
            border-color: var(--status-pass);
            background: rgba(76, 175, 80, 0.1);
        }

        .compatibility-item.limited {
            border-color: var(--status-warning);
            background: rgba(255, 152, 0, 0.1);
        }

        .compatibility-item.unsupported {
            border-color: var(--status-fail);
            background: rgba(244, 67, 54, 0.1);
        }

        .profiles-section {
            margin-bottom: var(--space-2xl);
        }

        .section-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            margin-bottom: var(--space-lg);
            color: var(--text-primary);
        }

        .profiles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: var(--card-gap);
        }

        .profile-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
        }

        .profile-card:hover {
            border-color: var(--accent-primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-normal);
        }

        .profile-card.selected {
            border-color: var(--accent-primary);
            box-shadow: 0 0 12px rgba(76, 175, 80, 0.3);
        }

        .profile-card.recommended {
            border-color: var(--accent-secondary);
            background: linear-gradient(135deg, var(--bg-card) 0%, rgba(33, 150, 243, 0.05) 100%);
        }

        .profile-card.recommended::before {
            content: "⭐ RECOMMENDED";
            position: absolute;
            top: -8px;
            right: var(--space-md);
            background: var(--accent-secondary);
            color: white;
            padding: var(--space-xs) var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        .profile-header-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--space-md);
        }

        .profile-name {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-xs);
        }

        .profile-description {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            margin-bottom: var(--space-md);
            line-height: 1.4;
        }

        .profile-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: var(--space-md);
            font-size: var(--font-size-sm);
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .stat-label {
            color: var(--text-muted);
            font-size: var(--font-size-xs);
        }

        .test-list {
            margin-top: var(--space-md);
        }

        .test-list-title {
            font-size: var(--font-size-sm);
            font-weight: 600;
            margin-bottom: var(--space-xs);
            color: var(--text-primary);
        }

        .test-items {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-xs);
        }

        .test-item {
            background: var(--bg-secondary);
            padding: var(--space-xs) var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-xs);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
        }

        .test-item.supported {
            border-color: var(--status-pass);
            color: var(--status-pass);
        }

        .test-item.limited {
            border-color: var(--status-warning);
            color: var(--status-warning);
        }

        .test-item.unsupported {
            border-color: var(--status-fail);
            color: var(--status-fail);
            opacity: 0.6;
        }

        .compatibility-badge {
            background: var(--status-pass);
            color: white;
            padding: var(--space-xs) var(--space-sm);
            border-radius: calc(var(--btn-border-radius) / 2);
            font-size: var(--font-size-xs);
            font-weight: 600;
        }

        .compatibility-badge.partial {
            background: var(--status-warning);
        }

        .compatibility-badge.incompatible {
            background: var(--status-fail);
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            margin-top: var(--space-xl);
        }

        .btn {
            padding: var(--btn-padding-md);
            border: none;
            border-radius: var(--btn-border-radius);
            font-size: var(--btn-font-size);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            min-width: 140px;
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-card);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .profiles-grid {
                grid-template-columns: 1fr;
            }
            
            .asset-info {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <!-- Header -->
        <div class="profile-header">
            <h1 class="profile-title">Test Profile Selection</h1>
            <p class="profile-subtitle">Choose the appropriate test profile for this device</p>
        </div>

        <!-- Asset Information -->
        <div class="asset-info" id="asset-info">
            <div class="info-item">
                <div class="info-label">Asset Number</div>
                <div class="info-value" id="asset-number">Loading...</div>
            </div>
            <div class="info-item">
                <div class="info-label">Operator</div>
                <div class="info-value" id="operator-id">Loading...</div>
            </div>
            <div class="info-item">
                <div class="info-label">Device Condition</div>
                <div class="info-value" id="device-condition">Loading...</div>
            </div>
            <div class="info-item">
                <div class="info-label">Hardware Category</div>
                <div class="info-value" id="hardware-category">Loading...</div>
            </div>
        </div>

        <!-- Hardware Compatibility -->
        <div class="hardware-compatibility">
            <h3 class="compatibility-title">
                <span style="margin-right: var(--space-sm);">🔧</span>
                Hardware Compatibility
            </h3>
            <div class="compatibility-grid" id="compatibility-grid">
                <!-- Populated dynamically -->
            </div>
        </div>

        <!-- Profiles Section -->
        <div class="profiles-section">
            <h2 class="section-title">Available Test Profiles</h2>
            <div class="profiles-grid" id="profiles-grid">
                <!-- Populated dynamically -->
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-secondary" onclick="goBack()">
                ← Back to Asset Entry
            </button>
            <button class="btn btn-primary" onclick="startTesting()" id="start-btn" disabled>
                Start Testing →
            </button>
        </div>
    </div>

    <script>
        let assetData = null;
        let hardwareData = null;
        let availableProfiles = [];
        let selectedProfile = null;
        let testCompatibility = {};

        // Initialize page
        async function initializePage() {
            loadStoredData();
            await loadProfiles();
            displayAssetInfo();
            analyzeHardwareCompatibility();
            renderProfiles();
            autoSelectRecommended();
        }

        // Load stored data from previous screens
        function loadStoredData() {
            try {
                const assetStored = localStorage.getItem('crucibleAssetData');
                const hardwareStored = localStorage.getItem('crucibleHardwareData');
                
                if (assetStored) {
                    assetData = JSON.parse(assetStored);
                }
                
                if (hardwareStored) {
                    hardwareData = JSON.parse(hardwareStored);
                } else if (assetData && assetData.hardwareData) {
                    hardwareData = assetData.hardwareData;
                }
            } catch (error) {
                console.error('Error loading stored data:', error);
            }
        }

        // Load available profiles from API
        async function loadProfiles() {
            try {
                const response = await fetch('/api/profiles');
                if (response.ok) {
                    availableProfiles = await response.json();
                } else {
                    // Fallback profiles if API fails
                    availableProfiles = [
                        {
                            name: 'Quick Test',
                            description: 'Basic functionality tests for fast processing',
                            tests: ['cpu', 'ram', 'lcd'],
                            estimated_duration: 5
                        },
                        {
                            name: 'Standard Test',
                            description: 'Comprehensive testing for most devices',
                            tests: ['cpu', 'ram', 'lcd', 'keyboard', 'pointing'],
                            estimated_duration: 15
                        },
                        {
                            name: 'Full Test',
                            description: 'Complete testing including all available tests',
                            tests: ['cpu', 'ram', 'lcd', 'keyboard', 'pointing', 'touch'],
                            estimated_duration: 25
                        }
                    ];
                }
            } catch (error) {
                console.error('Error loading profiles:', error);
                // Use fallback profiles
                availableProfiles = [];
            }
        }

        // Display asset information
        function displayAssetInfo() {
            if (assetData) {
                document.getElementById('asset-number').textContent = assetData.assetNumber || 'Unknown';
                document.getElementById('operator-id').textContent = assetData.operatorId || 'Unknown';
                document.getElementById('device-condition').textContent = 
                    assetData.deviceCondition ? 
                    assetData.deviceCondition.charAt(0).toUpperCase() + assetData.deviceCondition.slice(1) : 
                    'Unknown';
            }

            if (hardwareData && hardwareData.performanceProfile) {
                document.getElementById('hardware-category').textContent = 
                    hardwareData.performanceProfile.category.charAt(0).toUpperCase() + 
                    hardwareData.performanceProfile.category.slice(1);
            }
        }

        // Analyze hardware compatibility for each test type
        function analyzeHardwareCompatibility() {
            if (!hardwareData || !hardwareData.capabilities) {
                return;
            }

            const capabilities = hardwareData.capabilities;
            
            testCompatibility = {
                'cpu': { supported: true, reason: 'Always supported' },
                'ram': { supported: true, reason: 'Always supported' },
                'lcd': { 
                    supported: capabilities.canvas, 
                    reason: capabilities.canvas ? 'Canvas supported' : 'No canvas support' 
                },
                'keyboard': { supported: true, reason: 'Always supported' },
                'pointing': { 
                    supported: capabilities.mouse, 
                    reason: capabilities.mouse ? 'Mouse detected' : 'Limited pointing support' 
                },
                'touch': { 
                    supported: capabilities.touch, 
                    reason: capabilities.touch ? 'Touch input detected' : 'No touch input detected' 
                }
            };

            displayCompatibilityGrid();
        }

        // Display hardware compatibility grid
        function displayCompatibilityGrid() {
            const grid = document.getElementById('compatibility-grid');
            
            const compatibilityItems = Object.entries(testCompatibility).map(([test, compat]) => {
                const status = compat.supported ? 'supported' : 'unsupported';
                const icon = compat.supported ? '✅' : '❌';
                
                return `
                    <div class="compatibility-item ${status}" title="${compat.reason}">
                        <div>${icon}</div>
                        <div style="font-weight: 600; margin-top: 2px;">${test.toUpperCase()}</div>
                        <div style="font-size: var(--font-size-xs); color: var(--text-muted);">
                            ${compat.supported ? 'Ready' : 'Limited'}
                        </div>
                    </div>
                `;
            }).join('');

            grid.innerHTML = compatibilityItems;
        }

        // Render available profiles
        function renderProfiles() {
            const grid = document.getElementById('profiles-grid');
            
            const profileCards = availableProfiles.map(profile => {
                const compatibility = analyzeProfileCompatibility(profile);
                const isRecommended = determineRecommendation(profile, compatibility);
                
                return createProfileCard(profile, compatibility, isRecommended);
            }).join('');

            grid.innerHTML = profileCards;
        }

        // Analyze compatibility for a specific profile
        function analyzeProfileCompatibility(profile) {
            if (!profile.tests || !testCompatibility) {
                return { score: 0, supported: 0, total: 0, issues: [] };
            }

            let supported = 0;
            let total = profile.tests.length;
            let issues = [];

            profile.tests.forEach(test => {
                if (testCompatibility[test] && testCompatibility[test].supported) {
                    supported++;
                } else {
                    issues.push(test);
                }
            });

            return {
                score: total > 0 ? (supported / total) * 100 : 0,
                supported,
                total,
                issues
            };
        }

        // Determine if profile should be recommended
        function determineRecommendation(profile, compatibility) {
            if (!hardwareData || !hardwareData.performanceProfile) {
                return false;
            }

            const category = hardwareData.performanceProfile.category;
            const score = compatibility.score;

            // Recommend based on hardware category and compatibility
            if (category === 'low-spec' && profile.name.toLowerCase().includes('quick') && score >= 80) {
                return true;
            }
            if (category === 'standard' && profile.name.toLowerCase().includes('standard') && score >= 90) {
                return true;
            }
            if (category === 'high-end' && profile.name.toLowerCase().includes('full') && score >= 95) {
                return true;
            }

            return false;
        }

        // Create profile card HTML
        function createProfileCard(profile, compatibility, isRecommended) {
            const compatibilityClass = compatibility.score >= 90 ? 'compatible' : 
                                     compatibility.score >= 70 ? 'partial' : 'incompatible';
            
            const testItems = profile.tests ? profile.tests.map(test => {
                const testCompat = testCompatibility[test];
                const status = testCompat && testCompat.supported ? 'supported' : 'unsupported';
                return `<span class="test-item ${status}">${test.toUpperCase()}</span>`;
            }).join('') : '';

            return `
                <div class="profile-card ${isRecommended ? 'recommended' : ''}" 
                     onclick="selectProfile('${profile.name}')" 
                     data-profile="${profile.name}">
                    <div class="profile-header-section">
                        <div>
                            <div class="profile-name">${profile.name}</div>
                            <div class="profile-description">${profile.description || 'No description available'}</div>
                        </div>
                        <div class="compatibility-badge ${compatibilityClass}">
                            ${Math.round(compatibility.score)}% Compatible
                        </div>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-value">${profile.tests ? profile.tests.length : 0}</div>
                            <div class="stat-label">Tests</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">${compatibility.supported}/${compatibility.total}</div>
                            <div class="stat-label">Supported</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">~${profile.estimated_duration || 10}m</div>
                            <div class="stat-label">Duration</div>
                        </div>
                    </div>
                    
                    <div class="test-list">
                        <div class="test-list-title">Included Tests:</div>
                        <div class="test-items">
                            ${testItems}
                        </div>
                    </div>
                </div>
            `;
        }

        // Select a profile
        function selectProfile(profileName) {
            // Remove previous selection
            document.querySelectorAll('.profile-card').forEach(card => {
                card.classList.remove('selected');
            });

            // Select new profile
            const selectedCard = document.querySelector(`[data-profile="${profileName}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
                selectedProfile = profileName;
                updateStartButton();
            }
        }

        // Auto-select recommended profile
        function autoSelectRecommended() {
            const recommendedCard = document.querySelector('.profile-card.recommended');
            if (recommendedCard) {
                const profileName = recommendedCard.dataset.profile;
                selectProfile(profileName);
            }
        }

        // Update start button state
        function updateStartButton() {
            const startBtn = document.getElementById('start-btn');
            startBtn.disabled = !selectedProfile;
        }

        // Go back to asset entry
        function goBack() {
            window.location.href = '/asset_entry';
        }

        // Start testing with selected profile
        function startTesting() {
            if (!selectedProfile || !assetData) {
                alert('Please select a profile and ensure asset data is available');
                return;
            }

            // Store selected profile
            const testingData = {
                ...assetData,
                selectedProfile: selectedProfile,
                timestamp: new Date().toISOString()
            };

            if (typeof(Storage) !== "undefined") {
                localStorage.setItem('crucibleTestingData', JSON.stringify(testingData));
            }

            // Navigate to test execution dashboard
            const params = new URLSearchParams({
                asset_number: assetData.assetNumber,
                operator_id: assetData.operatorId,
                profile_name: selectedProfile,
                device_condition: assetData.deviceCondition
            });

            window.location.href = `/test_execution?${params.toString()}`;
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'Enter':
                    if (selectedProfile) {
                        startTesting();
                    }
                    break;
                case 'Escape':
                    goBack();
                    break;
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                    const index = parseInt(e.key) - 1;
                    const cards = document.querySelectorAll('.profile-card');
                    if (cards[index]) {
                        const profileName = cards[index].dataset.profile;
                        selectProfile(profileName);
                    }
                    break;
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initializePage);
    </script>
</body>
</html>
