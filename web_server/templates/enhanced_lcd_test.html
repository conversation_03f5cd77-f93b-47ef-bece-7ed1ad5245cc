<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced LCD Test - Crucible</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        .lcd-test-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            transition: background-color 0.1s ease;
        }

        .lcd-hud {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(5px);
            color: #fff;
        }

        .hud-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: var(--space-sm);
            color: var(--accent-primary);
        }

        .hud-info {
            display: grid;
            gap: var(--space-xs);
            margin-bottom: var(--space-md);
        }

        .hud-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .hud-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
        }

        .hud-value {
            color: var(--text-primary);
            font-weight: 500;
            font-size: var(--font-size-sm);
        }

        .progress-container {
            margin: var(--space-md) 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--space-xs);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: var(--font-size-xs);
            color: var(--text-muted);
            text-align: center;
        }

        .test-controls {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: var(--space-sm);
            z-index: 1000;
        }

        .control-btn {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--btn-border-radius);
            cursor: pointer;
            font-size: var(--font-size-sm);
            transition: all var(--transition-fast);
            backdrop-filter: blur(10px);
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--accent-primary);
        }

        .control-btn.primary {
            background: var(--accent-primary);
            border-color: var(--accent-primary);
        }

        .control-btn.primary:hover {
            background: #45a049;
        }

        .pattern-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.5s ease;
        }

        .pattern-overlay.active {
            opacity: 1;
        }

        /* Test Pattern Styles */
        .gradient-pattern {
            background: linear-gradient(45deg, #ff0000, #00ff00, #0000ff, #ffff00, #ff00ff, #00ffff);
            background-size: 400% 400%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .checkerboard-pattern {
            background-image: 
                linear-gradient(45deg, #000 25%, transparent 25%), 
                linear-gradient(-45deg, #000 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #000 75%), 
                linear-gradient(-45deg, transparent 75%, #000 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }

        .grid-pattern {
            background-image: 
                linear-gradient(rgba(255,255,255,.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }

        .dead-pixel-pattern {
            background: #fff;
            position: relative;
        }

        .dead-pixel {
            position: absolute;
            width: 2px;
            height: 2px;
            background: #000;
        }

        .color-bands {
            background: linear-gradient(to right, 
                #ff0000 0%, #ff0000 16.66%,
                #00ff00 16.66%, #00ff00 33.33%,
                #0000ff 33.33%, #0000ff 50%,
                #ffff00 50%, #ffff00 66.66%,
                #ff00ff 66.66%, #ff00ff 83.33%,
                #00ffff 83.33%, #00ffff 100%);
        }

        .brightness-test {
            background: radial-gradient(circle, #fff 0%, #000 100%);
        }

        /* Animations */
        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.3; }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        /* Hardware-specific optimizations */
        @media (max-width: 1024px) {
            .test-hud {
                top: 10px;
                left: 10px;
                padding: var(--space-sm);
                min-width: 200px;
            }
            
            .test-controls {
                bottom: 10px;
                right: 10px;
                flex-direction: column;
            }
        }

        @media (max-height: 768px) {
            .test-hud {
                font-size: var(--font-size-xs);
            }
        }

        /* High DPI optimizations */
        @media (min-resolution: 2dppx) {
            .checkerboard-pattern {
                background-size: 10px 10px;
                background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
            }
            
            .grid-pattern {
                background-size: 25px 25px;
            }
        }

        /* Accessibility */
        .accessibility-overlay {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: var(--space-xl);
            border-radius: var(--btn-border-radius);
            text-align: center;
            z-index: 2000;
            display: none;
        }

        .accessibility-overlay.visible {
            display: block;
        }
    </style>
</head>
<body>
    <div class="lcd-test-container" id="test-container">
        <!-- Test HUD -->
        <div class="test-hud">
            <div class="hud-title">🖥️ LCD Display Test</div>
            <div class="hud-info">
                <div class="hud-row">
                    <span class="hud-label">Asset:</span>
                    <span class="hud-value" id="asset-number">Loading...</span>
                </div>
                <div class="hud-row">
                    <span class="hud-label">Resolution:</span>
                    <span class="hud-value" id="resolution">Detecting...</span>
                </div>
                <div class="hud-row">
                    <span class="hud-label">Current Test:</span>
                    <span class="hud-value" id="current-test">Initializing...</span>
                </div>
                <div class="hud-row">
                    <span class="hud-label">Phase:</span>
                    <span class="hud-value" id="test-phase">1/8</span>
                </div>
            </div>
            
            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-text" id="progress-text">Starting test...</div>
            </div>
        </div>

        <!-- Pattern Overlays -->
        <div class="pattern-overlay" id="pattern-overlay"></div>

        <!-- Test Controls -->
        <div class="test-controls">
            <button class="control-btn" onclick="previousTest()" id="prev-btn" disabled>← Previous</button>
            <button class="control-btn" onclick="pauseTest()" id="pause-btn">⏸️ Pause</button>
            <button class="control-btn" onclick="nextTest()" id="next-btn">Next →</button>
            <button class="control-btn primary" onclick="completeTest()" id="complete-btn">✓ Complete</button>
        </div>

        <!-- Accessibility Overlay -->
        <div class="accessibility-overlay" id="accessibility-overlay">
            <h3>LCD Test Instructions</h3>
            <p>Look for any dead pixels, color distortions, or brightness issues.</p>
            <p>Use the controls to navigate between test patterns.</p>
            <button class="control-btn" onclick="hideAccessibilityOverlay()">Continue Test</button>
        </div>
    </div>

    <!-- Include hardware detector for adaptive features -->
    <script src="{{ url_for('static', filename='js/modules/hardware_detector.js') }}"></script>
    
    <script>
        let currentSessionId = null;
        let testPhases = [];
        let currentPhaseIndex = 0;
        let testPaused = false;
        let autoAdvanceTimer = null;
        let hardwareData = null;

        // Test phases configuration
        const TEST_PHASES = [
            {
                name: 'Solid Colors',
                tests: [
                    { color: '#000000', name: 'Black', duration: 3000 },
                    { color: '#ffffff', name: 'White', duration: 3000 },
                    { color: '#ff0000', name: 'Red', duration: 2000 },
                    { color: '#00ff00', name: 'Green', duration: 2000 },
                    { color: '#0000ff', name: 'Blue', duration: 2000 }
                ]
            },
            {
                name: 'Color Gradients',
                pattern: 'gradient-pattern',
                duration: 5000
            },
            {
                name: 'Checkerboard',
                pattern: 'checkerboard-pattern',
                duration: 4000
            },
            {
                name: 'Grid Lines',
                pattern: 'grid-pattern',
                duration: 4000
            },
            {
                name: 'Color Bands',
                pattern: 'color-bands',
                duration: 4000
            },
            {
                name: 'Brightness Test',
                pattern: 'brightness-test',
                duration: 5000
            },
            {
                name: 'Dead Pixel Check',
                pattern: 'dead-pixel-pattern',
                duration: 8000,
                interactive: true
            },
            {
                name: 'Final Review',
                color: '#1a1a1a',
                duration: 3000
            }
        ];

        // Initialize test
        async function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'LCD_TEST';
            
            document.getElementById('asset-number').textContent = assetNumber;
            
            // Wait for hardware detection
            await waitForHardwareDetection();
            
            // Adapt test based on hardware
            adaptTestForHardware();
            
            // Start API session
            await startTestSession(assetNumber);
            
            // Show initial instructions
            showAccessibilityOverlay();
            
            // Start test sequence after brief delay
            setTimeout(() => {
                if (!document.getElementById('accessibility-overlay').classList.contains('visible')) {
                    startTestSequence();
                }
            }, 2000);
        }

        // Wait for hardware detection
        async function waitForHardwareDetection() {
            if (window.CrucibleHardwareDetector && window.CrucibleHardwareDetector.hardwareInfo.screen) {
                hardwareData = window.CrucibleHardwareDetector.hardwareInfo;
                updateHardwareInfo();
                return;
            }

            return new Promise((resolve) => {
                const checkHardware = () => {
                    if (window.CrucibleHardwareDetector && window.CrucibleHardwareDetector.hardwareInfo.screen) {
                        hardwareData = window.CrucibleHardwareDetector.hardwareInfo;
                        updateHardwareInfo();
                        resolve();
                    } else {
                        setTimeout(checkHardware, 100);
                    }
                };
                checkHardware();
            });
        }

        // Update hardware information display
        function updateHardwareInfo() {
            if (hardwareData && hardwareData.screen) {
                const resolution = `${hardwareData.screen.width}x${hardwareData.screen.height}`;
                document.getElementById('resolution').textContent = resolution;
                
                // Add pixel ratio info if available
                if (hardwareData.screen.devicePixelRatio > 1) {
                    document.getElementById('resolution').textContent += ` (@${hardwareData.screen.devicePixelRatio}x)`;
                }
            }
        }

        // Adapt test based on hardware capabilities
        function adaptTestForHardware() {
            if (!hardwareData) return;

            const category = hardwareData.performanceProfile?.category;
            
            // Adjust test durations based on hardware performance
            if (category === 'low-spec') {
                // Reduce durations for low-spec hardware
                TEST_PHASES.forEach(phase => {
                    if (phase.duration) phase.duration *= 0.7;
                    if (phase.tests) {
                        phase.tests.forEach(test => {
                            test.duration *= 0.7;
                        });
                    }
                });
            } else if (category === 'high-end') {
                // Extend durations for better analysis on high-end displays
                TEST_PHASES.forEach(phase => {
                    if (phase.duration) phase.duration *= 1.3;
                    if (phase.tests) {
                        phase.tests.forEach(test => {
                            test.duration *= 1.3;
                        });
                    }
                });
            }

            // Add dead pixels for high-resolution displays
            if (hardwareData.screen.width >= 1920) {
                const deadPixelPhase = TEST_PHASES.find(p => p.name === 'Dead Pixel Check');
                if (deadPixelPhase) {
                    deadPixelPhase.duration = 10000; // More time for high-res
                }
            }
        }

        // Start test session with backend
        async function startTestSession(assetNumber) {
            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        test_type: 'lcd', 
                        asset_number: assetNumber,
                        test_params: {
                            enhanced: true,
                            hardware_adapted: true,
                            resolution: hardwareData?.screen ? `${hardwareData.screen.width}x${hardwareData.screen.height}` : 'unknown'
                        }
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    currentSessionId = result.session_id;
                    console.log('Enhanced LCD test session started:', currentSessionId);
                } else {
                    throw new Error(result.error || 'Failed to start test session');
                }
            } catch (error) {
                console.error('Error starting test session:', error);
                // Continue without backend session
            }
        }

        // Start test sequence
        function startTestSequence() {
            currentPhaseIndex = 0;
            runCurrentPhase();
        }

        // Run current test phase
        function runCurrentPhase() {
            if (currentPhaseIndex >= TEST_PHASES.length) {
                completeTest();
                return;
            }

            const phase = TEST_PHASES[currentPhaseIndex];
            updateUI(phase);
            
            if (phase.tests) {
                // Run sub-tests within phase
                runSubTests(phase.tests, 0);
            } else {
                // Single test phase
                applyTestPattern(phase);
                
                if (!phase.interactive && !testPaused) {
                    autoAdvanceTimer = setTimeout(() => {
                        nextTest();
                    }, phase.duration);
                }
            }

            // Send progress to backend
            sendProgressUpdate();
        }

        // Run sub-tests (for solid colors, etc.)
        function runSubTests(tests, index) {
            if (index >= tests.length) {
                nextTest();
                return;
            }

            const test = tests[index];
            const container = document.getElementById('test-container');
            container.style.backgroundColor = test.color;
            
            document.getElementById('current-test').textContent = test.name;
            
            if (!testPaused) {
                autoAdvanceTimer = setTimeout(() => {
                    runSubTests(tests, index + 1);
                }, test.duration);
            }
        }

        // Apply test pattern
        function applyTestPattern(phase) {
            const container = document.getElementById('test-container');
            const overlay = document.getElementById('pattern-overlay');
            
            // Clear previous styles
            container.style.backgroundColor = '';
            overlay.className = 'pattern-overlay';
            
            if (phase.color) {
                container.style.backgroundColor = phase.color;
            } else if (phase.pattern) {
                overlay.className = `pattern-overlay ${phase.pattern} active`;
                
                // Special handling for dead pixel pattern
                if (phase.pattern === 'dead-pixel-pattern') {
                    generateDeadPixels();
                }
            }
            
            document.getElementById('current-test').textContent = phase.name;
        }

        // Generate dead pixels for testing
        function generateDeadPixels() {
            const overlay = document.getElementById('pattern-overlay');
            overlay.innerHTML = ''; // Clear previous pixels
            
            // Add some dead pixels at strategic locations
            const deadPixelLocations = [
                { x: '25%', y: '25%' },
                { x: '75%', y: '25%' },
                { x: '50%', y: '50%' },
                { x: '25%', y: '75%' },
                { x: '75%', y: '75%' }
            ];
            
            deadPixelLocations.forEach(location => {
                const pixel = document.createElement('div');
                pixel.className = 'dead-pixel';
                pixel.style.left = location.x;
                pixel.style.top = location.y;
                overlay.appendChild(pixel);
            });
        }

        // Update UI elements
        function updateUI(phase) {
            const phaseText = `${currentPhaseIndex + 1}/${TEST_PHASES.length}`;
            document.getElementById('test-phase').textContent = phaseText;
            
            const progress = ((currentPhaseIndex + 1) / TEST_PHASES.length) * 100;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            document.getElementById('progress-text').textContent = `${Math.round(progress)}% Complete`;
            
            // Update button states
            document.getElementById('prev-btn').disabled = currentPhaseIndex === 0;
            document.getElementById('next-btn').disabled = currentPhaseIndex === TEST_PHASES.length - 1;
        }

        // Navigation functions
        function previousTest() {
            if (currentPhaseIndex > 0) {
                clearTimeout(autoAdvanceTimer);
                currentPhaseIndex--;
                runCurrentPhase();
            }
        }

        function nextTest() {
            clearTimeout(autoAdvanceTimer);
            currentPhaseIndex++;
            runCurrentPhase();
        }

        function pauseTest() {
            const pauseBtn = document.getElementById('pause-btn');
            
            if (testPaused) {
                testPaused = false;
                pauseBtn.textContent = '⏸️ Pause';
                runCurrentPhase();
            } else {
                testPaused = true;
                pauseBtn.textContent = '▶️ Resume';
                clearTimeout(autoAdvanceTimer);
            }
        }

        function completeTest() {
            clearTimeout(autoAdvanceTimer);
            
            // Send completion to backend
            if (currentSessionId) {
                sendTestCompletion();
            }
            
            // Show completion message
            const container = document.getElementById('test-container');
            container.style.backgroundColor = '#1a1a1a';
            container.innerHTML = `
                <div style="text-align: center; padding: var(--space-2xl);">
                    <h2 style="color: var(--accent-primary); margin-bottom: var(--space-lg);">
                        ✅ LCD Test Complete
                    </h2>
                    <p style="color: var(--text-secondary); margin-bottom: var(--space-xl);">
                        Test results have been recorded. You may close this window.
                    </p>
                    <button class="control-btn primary" onclick="window.close()">
                        Close Test Window
                    </button>
                </div>
            `;
        }

        // Send progress update to backend
        async function sendProgressUpdate() {
            if (!currentSessionId) return;

            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'progress_update',
                        phase: currentPhaseIndex + 1,
                        total_phases: TEST_PHASES.length,
                        current_test: TEST_PHASES[currentPhaseIndex].name,
                        timestamp: new Date().toISOString()
                    })
                });
            } catch (error) {
                console.error('Error sending progress update:', error);
            }
        }

        // Send test completion to backend
        async function sendTestCompletion() {
            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'complete_test',
                        result: 'completed',
                        phases_completed: TEST_PHASES.length,
                        hardware_info: hardwareData ? {
                            resolution: `${hardwareData.screen.width}x${hardwareData.screen.height}`,
                            pixel_ratio: hardwareData.screen.devicePixelRatio,
                            color_depth: hardwareData.screen.colorDepth
                        } : null
                    })
                });
            } catch (error) {
                console.error('Error sending test completion:', error);
            }
        }

        // Accessibility functions
        function showAccessibilityOverlay() {
            document.getElementById('accessibility-overlay').classList.add('visible');
        }

        function hideAccessibilityOverlay() {
            document.getElementById('accessibility-overlay').classList.remove('visible');
            startTestSequence();
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            switch (e.key) {
                case 'ArrowLeft':
                    e.preventDefault();
                    previousTest();
                    break;
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextTest();
                    break;
                case 'p':
                case 'P':
                    pauseTest();
                    break;
                case 'Enter':
                    completeTest();
                    break;
                case 'Escape':
                    if (confirm('Are you sure you want to exit the LCD test?')) {
                        window.close();
                    }
                    break;
                case 'h':
                case 'H':
                case '?':
                    showAccessibilityOverlay();
                    break;
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initializeTest);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            clearTimeout(autoAdvanceTimer);
        });
    </script>
</body>
</html>
