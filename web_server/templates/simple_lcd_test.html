<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LCD Test - Crucible</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
            cursor: none;
        }

        .lcd-test-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .test-info {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(5px);
            color: #fff;
        }

        .progress-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            z-index: 1000;
            color: #fff;
        }

        .instructions {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            z-index: 1000;
            color: #fff;
            text-align: right;
        }

        .completion-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        .completion-content {
            background: rgba(40, 40, 40, 0.95);
            border: 2px solid #4CAF50;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            backdrop-filter: blur(10px);
            max-width: 500px;
        }

        .completion-title {
            font-size: 24px;
            color: #4CAF50;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .completion-text {
            font-size: 16px;
            color: #fff;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .completion-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
        }

        .completion-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 100px;
        }

        .btn-pass {
            background: #4CAF50;
            color: white;
        }

        .btn-pass:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .btn-fail {
            background: #f44336;
            color: white;
        }

        .btn-fail:hover {
            background: #da190b;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="lcd-test-container" id="lcd-container">
        <div class="test-info" id="test-info">
            <div>🖥️ LCD Display Test</div>
            <div style="font-size: 11px; opacity: 0.8;">Asset: <span id="asset-number">Loading...</span></div>
        </div>

        <div class="progress-info" id="progress-info">
            Color: <span id="current-color">Black</span> (<span id="color-progress">1 of 5</span>)
        </div>

        <div class="instructions">
            Press any key or click to advance<br>
            Look for dead pixels and color accuracy
        </div>

        <div class="completion-screen" id="completion-screen">
            <div class="completion-content">
                <div class="completion-title">LCD Test Complete</div>
                <div class="completion-text">
                    You have viewed all test colors.<br>
                    Did the display show all colors correctly without dead pixels or issues?
                </div>
                <div class="completion-buttons">
                    <button class="completion-btn btn-pass" onclick="completeTest('PASS')">✅ PASS</button>
                    <button class="completion-btn btn-fail" onclick="completeTest('FAIL')">❌ FAIL</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentColorIndex = 0;
        let currentSessionId = null;
        
        const colors = [
            { name: 'Black', hex: '#000000' },
            { name: 'White', hex: '#FFFFFF' },
            { name: 'Red', hex: '#FF0000' },
            { name: 'Green', hex: '#00FF00' },
            { name: 'Blue', hex: '#0000FF' }
        ];

        // Initialize test
        function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'LCD_TEST';
            currentSessionId = params.get('session_id');
            
            document.getElementById('asset-number').textContent = assetNumber;
            
            // Set initial color
            updateDisplay();
            
            // Setup event listeners
            document.addEventListener('keydown', advanceColor);
            document.addEventListener('click', advanceColor);
            
            console.log('Simple LCD test initialized for asset:', assetNumber);
        }

        // Update the display with current color
        function updateDisplay() {
            const container = document.getElementById('lcd-container');
            const colorName = document.getElementById('current-color');
            const progress = document.getElementById('color-progress');
            
            const currentColor = colors[currentColorIndex];
            
            container.style.backgroundColor = currentColor.hex;
            colorName.textContent = currentColor.name;
            progress.textContent = `${currentColorIndex + 1} of ${colors.length}`;
            
            // Adjust text color for visibility
            const elements = document.querySelectorAll('.test-info, .progress-info, .instructions');
            elements.forEach(el => {
                if (currentColor.hex === '#FFFFFF') {
                    el.style.color = '#000';
                    el.style.background = 'rgba(255, 255, 255, 0.8)';
                } else {
                    el.style.color = '#fff';
                    el.style.background = 'rgba(0, 0, 0, 0.8)';
                }
            });
        }

        // Advance to next color
        function advanceColor(event) {
            // Prevent default behavior
            if (event) {
                event.preventDefault();
            }
            
            currentColorIndex++;
            
            if (currentColorIndex >= colors.length) {
                // Test complete - show completion screen
                showCompletionScreen();
            } else {
                // Update to next color
                updateDisplay();
            }
        }

        // Show completion screen
        function showCompletionScreen() {
            document.getElementById('completion-screen').style.display = 'flex';
            document.body.style.cursor = 'default';
        }

        // Complete test with result
        async function completeTest(result) {
            console.log('LCD test completed with result:', result);
            
            // Send result to backend if session exists
            if (currentSessionId) {
                try {
                    await fetch(`/api/visual_test/input/${currentSessionId}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'complete_test',
                            result: result.toLowerCase(),
                            colors_tested: colors.length
                        })
                    });
                } catch (error) {
                    console.error('Error sending test result:', error);
                }
            }
            
            // Close window after short delay
            setTimeout(() => {
                window.close();
            }, 1000);
        }

        // Initialize when page loads
        window.addEventListener('load', initializeTest);
    </script>
</body>
</html>
