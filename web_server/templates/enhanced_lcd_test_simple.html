<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced LCD Test - Crucible</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #fff;
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            width: 100vw;
        }

        .lcd-test-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            transition: background-color 0.1s ease;
        }

        .lcd-hud {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            z-index: 1000;
            backdrop-filter: blur(5px);
            color: #fff;
        }

        .lcd-progress {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            z-index: 1000;
            color: #fff;
        }

        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            z-index: 1000;
            color: #fff;
        }

        .status-running {
            background: rgba(40, 167, 69, 0.8);
        }

        .status-complete {
            background: rgba(0, 123, 255, 0.8);
        }

        .status-error {
            background: rgba(220, 53, 69, 0.8);
        }
    </style>
</head>
<body>
    <div class="lcd-test-container" id="lcd-container">
        <!-- Simple HUD like original -->
        <div class="lcd-hud" id="lcd-hud">
            <div id="lcd-color-name">Black</div>
            <div style="font-size: 11px; opacity: 0.8;">Press H to toggle HUD</div>
        </div>

        <!-- Progress indicator -->
        <div class="lcd-progress" id="lcd-progress">Initializing...</div>

        <!-- Status indicator -->
        <div class="status-indicator" id="status-indicator">
            <span id="status-text">Ready</span>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let eventSource = null;
        let hudVisible = true;

        // Initialize test - use existing session like original
        async function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'LCD_TEST';
            currentSessionId = params.get('session_id');
            
            // Connect to existing session if provided
            if (currentSessionId) {
                connectToProgressStream();
                console.log('Enhanced LCD test connected to session:', currentSessionId);
            } else {
                console.log('No session ID provided, test may not work properly');
            }
            
            // Setup keyboard shortcuts
            document.addEventListener('keydown', (event) => {
                if (event.key.toLowerCase() === 'h') {
                    event.preventDefault();
                    toggleHUD();
                }
            });
            
            console.log('Enhanced LCD test initialized for asset:', assetNumber);
        }

        // Connect to progress stream and auto-advance colors
        function connectToProgressStream() {
            if (!currentSessionId) return;
            
            eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);
            
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateProgress(data);
                
                // Auto-advance colors after a short delay (make it automatic)
                if (data.awaiting_user_input && data.current_color) {
                    setTimeout(() => {
                        autoAdvanceColor();
                    }, 1500); // 1.5 seconds per color for quick testing
                }
            };
            
            eventSource.onerror = function(event) {
                console.error('EventSource failed:', event);
            };
        }
        
        // Automatically advance to next color (no clicking required)
        async function autoAdvanceColor() {
            if (!currentSessionId) return;
            
            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'color_result',
                        result: 'ok' // Always mark as OK for automatic testing
                    })
                });
            } catch (error) {
                console.error('Error auto-advancing color:', error);
            }
        }

        // Update progress - exactly like original
        function updateProgress(data) {
            // Update status first
            if (data.status) {
                updateStatus(data.status);
            }
            
            // Handle LCD-specific updates (like original updateLcdProgress)
            updateLcdProgress(data);
            
            // Handle test completion
            if (data.test_complete) {
                setTimeout(() => {
                    window.close();
                }, 2000);
            }
        }
        
        // LCD-specific progress updates - exactly like original
        function updateLcdProgress(progress) {
            const lcdContainer = document.getElementById('lcd-container');
            const lcdProgress = document.getElementById('lcd-progress');
            const lcdColorName = document.getElementById('lcd-color-name');
            
            // Update background color immediately, no transition
            if (progress.current_color_hex) {
                lcdContainer.style.backgroundColor = progress.current_color_hex;
            }
            
            // Update progress text
            if (progress.total_colors > 0) {
                const txt = `Color ${progress.color_index + 1} of ${progress.total_colors}`;
                lcdProgress.textContent = txt;
            }
            
            // Update color name
            if (progress.current_color) {
                lcdColorName.textContent = progress.current_color;
            }
        }

        // Update status indicator
        function updateStatus(status) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('status-text');
            
            indicator.className = 'status-indicator';
            
            switch(status) {
                case 'running':
                    indicator.classList.add('status-running');
                    statusText.textContent = 'Running';
                    break;
                case 'complete':
                    indicator.classList.add('status-complete');
                    statusText.textContent = 'Complete';
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    statusText.textContent = 'Error';
                    break;
                default:
                    statusText.textContent = status;
            }
        }

        // Toggle HUD visibility
        function toggleHUD() {
            hudVisible = !hudVisible;
            const hud = document.getElementById('lcd-hud');
            const progress = document.getElementById('lcd-progress');
            const status = document.getElementById('status-indicator');
            
            hud.style.display = hudVisible ? 'block' : 'none';
            progress.style.display = hudVisible ? 'block' : 'none';
            status.style.display = hudVisible ? 'block' : 'none';
        }

        // Initialize when page loads
        window.addEventListener('load', initializeTest);
    </script>
</body>
</html>
