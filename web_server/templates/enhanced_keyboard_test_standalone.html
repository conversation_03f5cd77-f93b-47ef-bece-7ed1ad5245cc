<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Keyboard Test - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .keyboard-test-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding: 20px 24px;
            background: rgba(40, 40, 40, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .test-title {
            font-size: 28px;
            font-weight: 700;
            color: #4CAF50;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .test-title::before {
            content: '⌨️';
            font-size: 32px;
            filter: drop-shadow(0 2px 4px rgba(76, 175, 80, 0.3));
        }

        .test-stats {
            display: flex;
            gap: 32px;
            text-align: center;
        }
        
        .stat-item {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            min-width: 80px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #4CAF50;
            display: block;
            line-height: 1;
        }

        .stat-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 4px;
        }

        .keyboard-layout {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: rgba(30, 30, 30, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(10px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .keyboard-layout::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.5), transparent);
        }

        .keyboard-visual {
            display: grid;
            gap: 8px;
            margin-bottom: 24px;
        }

        .keyboard-row {
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .key {
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 45px;
            min-width: 45px;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .key::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .key:hover::before {
            opacity: 1;
        }

        .key.pressed {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            transform: scale(0.95) translateY(1px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: #4CAF50;
        }

        .key.tested {
            background: linear-gradient(145deg, #2196F3, #1976D2);
            color: white;
            border-color: #2196F3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .key.tested::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 8px;
            color: rgba(255, 255, 255, 0.8);
        }

        .key.stuck {
            background: linear-gradient(145deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
            animation: stuckKeyPulse 1s infinite;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4);
        }
        
        @keyframes stuckKeyPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4); }
            50% { transform: scale(1.05); box-shadow: 0 4px 16px rgba(244, 67, 54, 0.6); }
        }

        .key.space { min-width: 240px; }
        .key.enter { min-width: 90px; }
        .key.shift { min-width: 110px; }
        .key.tab { min-width: 70px; }
        .key.caps { min-width: 85px; }
        .key.ctrl { min-width: 60px; }
        .key.alt { min-width: 55px; }
        .key.backspace { min-width: 95px; }
        .key.function { min-width: 45px; font-size: 11px; }
        .key.esc { min-width: 45px; }
        .key.numpad { min-width: 40px; }
        .key.numpad-enter { min-height: 80px; }
        .key.numpad-zero { min-width: 85px; }
        .key.numpad-plus { min-height: 80px; }

        .keyboard-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: flex-start;
        }

        .main-keyboard {
            flex: 1;
            max-width: 800px;
        }

        .numpad-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-left: 20px;
        }

        .numpad-section .keyboard-visual {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 4px;
            width: 180px;
        }

        .numpad-section .keyboard-row {
            display: contents;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #66BB6A);
            width: 0%;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
        }

        .control-btn {
            background: linear-gradient(145deg, #404040, #303030);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .control-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .control-btn.primary {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            border-color: #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }
        
        .control-btn.primary:hover {
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        @media (max-width: 1024px) {
            .keyboard-container {
                flex-direction: column;
                align-items: center;
            }
            
            .numpad-section {
                margin-left: 0;
                margin-top: 20px;
            }
        }

        @media (max-width: 768px) {
            .key { 
                min-height: 30px; 
                min-width: 30px; 
                font-size: 10px;
            }
            .key.function { 
                min-width: 35px; 
                font-size: 9px; 
            }
            .key.numpad { 
                min-width: 35px; 
            }
        }
    </style>
</head>
<body>
    <div class="keyboard-test-container">
        <div class="test-header">
            <div class="test-title">Enhanced Keyboard Test</div>
            <div class="test-stats">
                <div class="stat-item">
                    <div class="stat-value" id="keys-tested">0</div>
                    <div class="stat-label">Keys Tested</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stuck-keys">0</div>
                    <div class="stat-label">Issues</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="progress-percent">0%</div>
                    <div class="stat-label">Complete</div>
                </div>
            </div>
        </div>

        <div class="keyboard-layout">
            <div style="text-align: center; margin-bottom: 24px;">
                <div style="font-size: 18px; margin-bottom: 12px; color: rgba(255, 255, 255, 0.9); font-weight: 500;" id="instruction">Press any key to begin testing</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6); margin-top: 8px;" id="progress-text">Ready to start</div>
            </div>

            <div class="keyboard-container">
                <div class="main-keyboard">
                    <div class="keyboard-visual" id="keyboard-visual"></div>
                </div>
                <div class="numpad-section">
                    <div style="text-align: center; margin-bottom: 8px; color: rgba(255, 255, 255, 0.7); font-size: 12px;">Numpad</div>
                    <div class="keyboard-visual" id="numpad-visual"></div>
                </div>
            </div>

            <div style="display: flex; justify-content: center; gap: 16px;">
                <button class="control-btn" onclick="resetTest()">🔄 Reset</button>
                <button class="control-btn" onclick="completeTest(true)" id="complete-any-btn">⚠️ Complete Anyway</button>
                <button class="control-btn primary" onclick="completeTest()" id="complete-btn" disabled>✅ Complete</button>
            </div>
        </div>
    </div>

    <script>
        let testedKeys = new Set();
        let stuckKeys = new Set();
        let keyStates = new Map();
        let currentSessionId = null;
        let assetNumber = null;

        // Simple readable keyboard layout with unique identifiers for modifier keys
        const keyboardLayout = [
            // Function keys
            ['esc', 'f1', 'f2', 'f3', 'f4', 'f5', 'f6', 'f7', 'f8', 'f9', 'f10', 'f11', 'f12'],
            // Number row
            ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'backspace'],
            // QWERTY rows
            ['tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
            ['capslock', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'enter'],
            ['shiftleft', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'shiftright'],
            ['ctrlleft', 'altleft', ' ', 'altright', 'ctrlright']
        ];

        // Improved numpad layout (always visible)
        const numpadLayout = [
            ['numlock', 'numpaddivide', 'numpadmultiply', 'numpadsubtract'],
            ['numpad7', 'numpad8', 'numpad9', 'numpadadd'],
            ['numpad4', 'numpad5', 'numpad6', 'numpadadd'],
            ['numpad1', 'numpad2', 'numpad3', 'numpadenter'],
            ['numpad0', 'numpaddecimal', 'numpadenter']
        ];

        // Initialize test
        function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            assetNumber = params.get('asset_number') || 'KEYBOARD_TEST';
            
            currentSessionId = `enhanced_keyboard_${assetNumber}_${Date.now()}`;
            console.log('Enhanced keyboard test started:', currentSessionId);
            
            // Request fullscreen for better testing experience
            requestFullscreen();
            
            generateKeyboardLayout();
            generateNumpadLayout();
            setupEventListeners();
        }

        // Request fullscreen mode
        function requestFullscreen() {
            try {
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } catch (error) {
                console.log('Fullscreen request failed:', error);
                // Continue without fullscreen if it fails
            }
        }

        function generateKeyboardLayout() {
            const keyboardVisual = document.getElementById('keyboard-visual');
            keyboardVisual.innerHTML = '';

            keyboardLayout.forEach(row => {
                const rowElement = document.createElement('div');
                rowElement.className = 'keyboard-row';
                
                row.forEach(keyCode => {
                    const keyElement = document.createElement('div');
                    keyElement.className = 'key';
                    keyElement.dataset.key = keyCode.toLowerCase();
                    
                    // Add special classes for different key types
                    if (keyCode === ' ') keyElement.classList.add('space');
                    else if (keyCode === 'enter') keyElement.classList.add('enter');
                    else if (keyCode === 'shiftleft' || keyCode === 'shiftright') keyElement.classList.add('shift');
                    else if (keyCode === 'tab') keyElement.classList.add('tab');
                    else if (keyCode === 'capslock') keyElement.classList.add('caps');
                    else if (keyCode === 'ctrlleft' || keyCode === 'ctrlright') keyElement.classList.add('ctrl');
                    else if (keyCode === 'altleft' || keyCode === 'altright') keyElement.classList.add('alt');
                    else if (keyCode === 'backspace') keyElement.classList.add('backspace');
                    else if (keyCode === 'esc') keyElement.classList.add('esc');
                    else if (keyCode.startsWith('f') && keyCode.length <= 3) keyElement.classList.add('function');
                    
                    // Display text
                    if (keyCode === ' ') {
                        keyElement.textContent = 'Space';
                    } else if (keyCode === 'shiftleft' || keyCode === 'shiftright') {
                        keyElement.textContent = 'Shift';
                    } else if (keyCode === 'ctrlleft' || keyCode === 'ctrlright') {
                        keyElement.textContent = 'Ctrl';
                    } else if (keyCode === 'altleft' || keyCode === 'altright') {
                        keyElement.textContent = 'Alt';
                    } else if (keyCode === 'capslock') {
                        keyElement.textContent = 'Caps';
                    } else if (keyCode === 'backspace') {
                        keyElement.textContent = '⌫';
                    } else if (keyCode === 'enter') {
                        keyElement.textContent = '↵';
                    } else if (keyCode === 'tab') {
                        keyElement.textContent = '⇥';
                    } else if (keyCode.length === 1) {
                        keyElement.textContent = keyCode.toUpperCase();
                    } else {
                        keyElement.textContent = keyCode.toUpperCase();
                    }
                    
                    rowElement.appendChild(keyElement);
                });
                
                keyboardVisual.appendChild(rowElement);
            });
        }

        function generateNumpadLayout() {
            const numpadVisual = document.getElementById('numpad-visual');
            numpadVisual.innerHTML = '';

            numpadLayout.forEach((row, rowIndex) => {
                row.forEach((keyCode, keyIndex) => {
                    // Skip duplicate + and Enter keys for cleaner layout
                    if ((keyCode === 'numpadadd' && rowIndex === 2) || 
                        (keyCode === 'numpadenter' && rowIndex === 4)) {
                        return;
                    }
                    
                    const keyElement = document.createElement('div');
                    keyElement.className = 'key numpad';
                    keyElement.dataset.key = keyCode.toLowerCase();
                    
                    // Special numpad key styling
                    if (keyCode === 'numpadenter') {
                        keyElement.classList.add('numpad-enter');
                    } else if (keyCode === 'numpad0') {
                        keyElement.classList.add('numpad-zero');
                    } else if (keyCode === 'numpadadd') {
                        keyElement.classList.add('numpad-plus');
                    }
                    
                    // Set display text for numpad keys
                    if (keyCode === 'numlock') {
                        keyElement.textContent = 'NumLock';
                    } else if (keyCode === 'numpaddivide') {
                        keyElement.textContent = '/';
                    } else if (keyCode === 'numpadmultiply') {
                        keyElement.textContent = '*';
                    } else if (keyCode === 'numpadsubtract') {
                        keyElement.textContent = '-';
                    } else if (keyCode === 'numpadadd') {
                        keyElement.textContent = '+';
                    } else if (keyCode === 'numpadenter') {
                        keyElement.textContent = '↵';
                    } else if (keyCode === 'numpaddecimal') {
                        keyElement.textContent = '.';
                    } else if (keyCode.startsWith('numpad')) {
                        // Extract number from numpad0-9
                        keyElement.textContent = keyCode.replace('numpad', '');
                    } else {
                        keyElement.textContent = keyCode.toUpperCase();
                    }
                    
                    numpadVisual.appendChild(keyElement);
                });
            });
        }

        function setupEventListeners() {
            document.addEventListener('keydown', handleKeyDown);
            document.addEventListener('keyup', handleKeyUp);
            document.body.tabIndex = 0;
            document.body.focus();
        }

        function handleKeyDown(event) {
            event.preventDefault();
            const keyCode = event.code;
            const keyChar = event.key;
            
            console.log(`Key pressed: code="${keyCode}" key="${keyChar}"`);
            
            // Map browser event.code to our simple layout keys
            const normalizedKey = mapBrowserKeyToLayout(keyCode, keyChar);
            
            if (!normalizedKey) {
                console.warn(`Could not map key: code="${keyCode}" key="${keyChar}"`);
                return;
            }
            
            console.log(`Mapped to layout key: "${normalizedKey}"`);
            
            // Prevent stuck key detection on first press
            if (keyStates.has(normalizedKey)) return;
            keyStates.set(normalizedKey, Date.now());
            
            // Try to find and highlight the key
            const highlighted = highlightKey(normalizedKey, true);
            if (!highlighted) {
                console.warn(`Key not found in layout: "${normalizedKey}"`);
                return;
            }
            
            // Mark key as tested if not already
            if (!testedKeys.has(normalizedKey)) {
                testedKeys.add(normalizedKey);
                markKeyAsTested(normalizedKey);
                updateProgress();
            }
        }

        function handleKeyUp(event) {
            const keyCode = event.code;
            const keyChar = event.key;
            const normalizedKey = mapBrowserKeyToLayout(keyCode, keyChar);
            if (normalizedKey) {
                keyStates.delete(normalizedKey);
                highlightKey(normalizedKey, false);
            }
        }

        function mapBrowserKeyToLayout(code, key) {
            // Map browser event.code to our layout keys with unique identifiers for modifiers
            const mapping = {
                // Function keys
                'Escape': 'esc',
                'F1': 'f1', 'F2': 'f2', 'F3': 'f3', 'F4': 'f4', 'F5': 'f5', 'F6': 'f6',
                'F7': 'f7', 'F8': 'f8', 'F9': 'f9', 'F10': 'f10', 'F11': 'f11', 'F12': 'f12',
                
                // Number row
                'Backquote': '`',
                'Digit1': '1', 'Digit2': '2', 'Digit3': '3', 'Digit4': '4', 'Digit5': '5',
                'Digit6': '6', 'Digit7': '7', 'Digit8': '8', 'Digit9': '9', 'Digit0': '0',
                'Minus': '-', 'Equal': '=', 'Backspace': 'backspace',
                
                // Top row
                'Tab': 'tab',
                'KeyQ': 'q', 'KeyW': 'w', 'KeyE': 'e', 'KeyR': 'r', 'KeyT': 't',
                'KeyY': 'y', 'KeyU': 'u', 'KeyI': 'i', 'KeyO': 'o', 'KeyP': 'p',
                'BracketLeft': '[', 'BracketRight': ']', 'Backslash': '\\',
                
                // Home row
                'CapsLock': 'capslock',
                'KeyA': 'a', 'KeyS': 's', 'KeyD': 'd', 'KeyF': 'f', 'KeyG': 'g',
                'KeyH': 'h', 'KeyJ': 'j', 'KeyK': 'k', 'KeyL': 'l',
                'Semicolon': ';', 'Quote': "'", 'Enter': 'enter',
                
                // Bottom row - distinguish left and right modifiers
                'ShiftLeft': 'shiftleft', 'ShiftRight': 'shiftright',
                'KeyZ': 'z', 'KeyX': 'x', 'KeyC': 'c', 'KeyV': 'v', 'KeyB': 'b',
                'KeyN': 'n', 'KeyM': 'm', 'Comma': ',', 'Period': '.', 'Slash': '/',
                
                // Bottom modifier row - distinguish left and right
                'ControlLeft': 'ctrlleft', 'ControlRight': 'ctrlright',
                'AltLeft': 'altleft', 'AltRight': 'altright',
                'Space': ' ',
                
                // Numpad - use unique identifiers to avoid conflicts with main keyboard
                'NumLock': 'numlock',
                'NumpadDivide': 'numpaddivide', 'NumpadMultiply': 'numpadmultiply', 
                'NumpadSubtract': 'numpadsubtract', 'NumpadAdd': 'numpadadd',
                'Numpad7': 'numpad7', 'Numpad8': 'numpad8', 'Numpad9': 'numpad9',
                'Numpad4': 'numpad4', 'Numpad5': 'numpad5', 'Numpad6': 'numpad6',
                'Numpad1': 'numpad1', 'Numpad2': 'numpad2', 'Numpad3': 'numpad3',
                'Numpad0': 'numpad0', 'NumpadDecimal': 'numpaddecimal', 'NumpadEnter': 'numpadenter'
            };
            
            return mapping[code] || null;
        }

        // Safely locate a key element by its data-key, even for special characters (e.g., backslash)
        function getKeyElementByDataKey(key) {
            // Prefer CSS.escape where available to produce a valid attribute selector
            if (window.CSS && typeof CSS.escape === 'function') {
                const escaped = CSS.escape(String(key));
                return document.querySelector(`[data-key="${escaped}"]`);
            }
            // Fallback: linear scan of rendered keys
            const all = document.querySelectorAll('.key');
            for (const el of all) {
                if (el.dataset && el.dataset.key === String(key)) return el;
            }
            return null;
        }

        function highlightKey(key, pressed) {
            const keyElement = getKeyElementByDataKey(key);
            if (keyElement) {
                keyElement.classList.toggle('pressed', pressed);
                keyElement.classList.toggle('highlighted', pressed);
                return true;
            }
            return false;
        }

        function findAlternativeKeyMatch(originalKey) {
            // Alternative key mappings for common issues
            const alternatives = {
                // Special characters that might have different event.key values
                'Dead': '`',  // Dead key often maps to backtick
                'Unidentified': null,
                // Numpad keys
                'Numpad0': '0', 'Numpad1': '1', 'Numpad2': '2', 'Numpad3': '3', 'Numpad4': '4',
                'Numpad5': '5', 'Numpad6': '6', 'Numpad7': '7', 'Numpad8': '8', 'Numpad9': '9',
                'NumpadDivide': '/', 'NumpadMultiply': '*', 'NumpadSubtract': '-', 
                'NumpadAdd': '+', 'NumpadDecimal': '.', 'NumpadEnter': 'enter',
                // Arrow keys and other special keys
                'ArrowUp': 'arrowup', 'ArrowDown': 'arrowdown', 'ArrowLeft': 'arrowleft', 'ArrowRight': 'arrowright',
                'Home': 'home', 'End': 'end', 'PageUp': 'pageup', 'PageDown': 'pagedown',
                'Insert': 'insert', 'Delete': 'delete',
                // Function keys with different casing
                'f1': 'f1', 'f2': 'f2', 'f3': 'f3', 'f4': 'f4', 'f5': 'f5', 'f6': 'f6',
                'f7': 'f7', 'f8': 'f8', 'f9': 'f9', 'f10': 'f10', 'f11': 'f11', 'f12': 'f12'
            };
            
            return alternatives[originalKey] || null;
        }

        function markKeyAsTested(key) {
            const keyElement = getKeyElementByDataKey(key);
            if (keyElement) {
                keyElement.classList.add('tested');
            }
        }

        function updateProgress() {
            const totalKeys = document.querySelectorAll('.key').length;
            const testedCount = testedKeys.size;
            const progress = (testedCount / totalKeys) * 100;
            
            document.getElementById('keys-tested').textContent = testedCount;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            document.getElementById('progress-text').textContent = `${Math.round(progress)}% Complete`;
            document.getElementById('progress-percent').textContent = `${Math.round(progress)}%`;
            
            // Update instruction text
            if (progress === 0) {
                document.getElementById('instruction').textContent = 'Press any key to begin testing';
            } else if (progress < 80) {
                document.getElementById('instruction').textContent = `Keep testing keys - ${totalKeys - testedCount} remaining`;
            } else {
                document.getElementById('instruction').textContent = 'Great! Most keys tested - ready to complete';
            }
            
            document.getElementById('complete-btn').disabled = progress < 80;
        }

        function resetTest() {
            testedKeys.clear();
            stuckKeys.clear();
            keyStates.clear();
            
            document.querySelectorAll('.key').forEach(key => {
                key.classList.remove('tested', 'pressed', 'stuck');
            });
            
            updateProgress();
        }

        function completeTest(force = false) {
            console.log('Enhanced keyboard test completed', { force });
            const totalKeys = document.querySelectorAll('.key').length;
            const testedCount = testedKeys.size;
            const allKeys = Array.from(document.querySelectorAll('.key')).map(el => el.dataset.key);
            const untestedKeys = allKeys.filter(k => !testedKeys.has(k));
            const percentage = totalKeys > 0 ? Math.round((testedCount / totalKeys) * 100) : 0;

            // Keep default PASS threshold aligned with UI enablement (80%)
            const status = force ? 'PASS' : (percentage >= 80 ? 'PASS' : 'FAIL');
            const notes = `${force ? 'Force-completed' : 'Completed'} for asset ${assetNumber}. Tested ${testedCount}/${totalKeys} keys (${percentage}%). Missing: ${untestedKeys.join(', ') || 'none'}`;

            const payload = {
                session_id: currentSessionId,
                result: {
                    status,
                    notes,
                    pressed_keys_count: testedCount,
                    total_keys: totalKeys,
                    percentage,
                    untested_keys: untestedKeys,
                    force_completed: !!force
                }
            };

            try {
                fetch('/api/keyboard_test_complete', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(payload)
                }).catch((err) => console.warn('keyboard_test_complete request failed:', err));
            } catch (err) {
                console.warn('keyboard_test_complete error:', err);
            }

            document.getElementById('instruction').textContent = `✅ Test Complete${force ? ' (Forced)' : ''}! Tested ${testedCount}/${totalKeys} keys`;
            setTimeout(() => {
                window.close();
            }, 1000);
        }

        // Initialize when page loads
        window.addEventListener('load', initializeTest);
    </script>
</body>
</html>
