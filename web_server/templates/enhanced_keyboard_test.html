<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Keyboard Test - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: var(--text-primary);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .keyboard-test-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: var(--container-padding);
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-xl);
            padding: 20px 24px;
            background: rgba(40, 40, 40, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .test-title {
            font-size: 28px;
            font-weight: 700;
            color: #4CAF50;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .test-title::before {
            content: '⌨️';
            font-size: 32px;
            filter: drop-shadow(0 2px 4px rgba(76, 175, 80, 0.3));
        }

        .test-stats {
            display: flex;
            gap: 32px;
            text-align: center;
        }
        
        .stat-item {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid rgba(76, 175, 80, 0.2);
            border-radius: 8px;
            padding: 12px 16px;
            min-width: 80px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #4CAF50;
            display: block;
            line-height: 1;
        }

        .stat-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-top: 4px;
        }

        .keyboard-layout {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: rgba(30, 30, 30, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(10px);
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .keyboard-layout::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.5), transparent);
        }

        .keyboard-visual {
            display: grid;
            gap: var(--space-xs);
            margin-bottom: var(--space-xl);
        }

        .keyboard-row {
            display: flex;
            gap: var(--space-xs);
            justify-content: center;
        }

        .key {
            background: linear-gradient(145deg, #3a3a3a, #2a2a2a);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #ffffff;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 45px;
            min-width: 45px;
            position: relative;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }
        
        .key::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 8px;
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .key:hover::before {
            opacity: 1;
        }

        .key.pressed {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            transform: scale(0.95) translateY(1px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: #4CAF50;
        }

        .key.tested {
            background: linear-gradient(145deg, #2196F3, #1976D2);
            color: white;
            border-color: #2196F3;
            box-shadow: 0 2px 8px rgba(33, 150, 243, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }
        
        .key.tested::after {
            content: '✓';
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 8px;
            color: rgba(255, 255, 255, 0.8);
        }

        .key.stuck {
            background: linear-gradient(145deg, #f44336, #d32f2f);
            color: white;
            border-color: #f44336;
            animation: stuckKeyPulse 1s infinite;
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4);
        }
        
        @keyframes stuckKeyPulse {
            0%, 100% { transform: scale(1); box-shadow: 0 2px 8px rgba(244, 67, 54, 0.4); }
            50% { transform: scale(1.05); box-shadow: 0 4px 16px rgba(244, 67, 54, 0.6); }
        }

        .key.space { min-width: 240px; }
        .key.enter { min-width: 90px; }
        .key.shift { min-width: 110px; }
        .key.tab { min-width: 70px; }
        .key.caps { min-width: 85px; }
        .key.ctrl { min-width: 60px; }
        .key.alt { min-width: 55px; }
        .key.backspace { min-width: 95px; }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            overflow: hidden;
            margin: 16px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #66BB6A);
            width: 0%;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
        }

        .control-btn {
            background: linear-gradient(145deg, #404040, #303030);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #ffffff;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }
        
        .control-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }
        
        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .control-btn.primary {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            border-color: #4CAF50;
            box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
        }
        
        .control-btn.primary:hover {
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .key { min-height: 30px; min-width: 30px; }
        }
    </style>
</head>
<body>
    <div class="keyboard-test-container">
        <div class="test-header">
            <div class="test-title">Enhanced Keyboard Test</div>
            <div class="test-stats">
                <div class="stat-item">
                    <div class="stat-value" id="keys-tested">0</div>
                    <div class="stat-label">Keys Tested</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="stuck-keys">0</div>
                    <div class="stat-label">Issues</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="progress-percent">0%</div>
                    <div class="stat-label">Complete</div>
                </div>
            </div>
        </div>

        <div class="keyboard-layout">
            <div style="text-align: center; margin-bottom: 24px;">
                <div style="font-size: 18px; margin-bottom: 12px; color: rgba(255, 255, 255, 0.9); font-weight: 500;" id="instruction">Press any key to begin testing</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div style="font-size: 12px; color: rgba(255, 255, 255, 0.6); margin-top: 8px;" id="progress-text">Ready to start</div>
            </div>

            <div class="keyboard-visual" id="keyboard-visual"></div>

            <div style="display: flex; justify-content: center; gap: var(--space-md);">
                <button class="control-btn" onclick="resetTest()">🔄 Reset</button>
                <button class="control-btn primary" onclick="completeTest()" id="complete-btn" disabled>✅ Complete</button>
            </div>
        </div>
    </div>

    <script>
        let testedKeys = new Set();
        let stuckKeys = new Set();
        let keyStates = new Map();
        let currentSessionId = null;

        const KEYBOARD_LAYOUT = [
            ['Escape', 'F1', 'F2', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12'],
            ['`', '1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '=', 'Backspace'],
            ['Tab', 'q', 'w', 'e', 'r', 't', 'y', 'u', 'i', 'o', 'p', '[', ']', '\\'],
            ['CapsLock', 'a', 's', 'd', 'f', 'g', 'h', 'j', 'k', 'l', ';', "'", 'Enter'],
            ['Shift', 'z', 'x', 'c', 'v', 'b', 'n', 'm', ',', '.', '/', 'Shift'],
            ['Control', 'Meta', 'Alt', ' ', 'Alt', 'Meta', 'Control']
        ];

        async function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'KEYBOARD_TEST';
            
            await startTestSession(assetNumber);
            generateKeyboardLayout();
            setupEventListeners();
        }

        async function startTestSession(assetNumber) {
            currentSessionId = `enhanced_keyboard_${assetNumber}_${Date.now()}`;
            console.log('Enhanced keyboard test session started:', currentSessionId);
            
            // Update instruction to start testing
            document.getElementById('instruction').textContent = 'Press any key to begin testing';
        }
        
        // Enhanced keyboard test doesn't need backend progress stream
        function connectToProgressStream() {
            // No-op for standalone enhanced test
        }

        function generateKeyboardLayout() {
            const keyboardVisual = document.getElementById('keyboard-visual');
            
            KEYBOARD_LAYOUT.forEach(row => {
                const rowElement = document.createElement('div');
                rowElement.className = 'keyboard-row';
                
                row.forEach(keyCode => {
                    const keyElement = document.createElement('div');
                    keyElement.className = 'key';
                    keyElement.dataset.key = keyCode;
                    
                    if (keyCode === ' ') keyElement.classList.add('space');
                    else if (keyCode === 'Enter') keyElement.classList.add('enter');
                    else if (keyCode === 'Shift') keyElement.classList.add('shift');
                    
                    keyElement.textContent = keyCode === ' ' ? 'Space' : keyCode.toUpperCase();
                    rowElement.appendChild(keyElement);
                });
                
                keyboardVisual.appendChild(rowElement);
            });
        }

        function setupEventListeners() {
            document.addEventListener('keydown', handleKeyDown);
            document.addEventListener('keyup', handleKeyUp);
            document.body.tabIndex = 0;
            document.body.focus();
        }

        function handleKeyDown(event) {
            const key = normalizeKey(event.key);
            
            if (keyStates.has(key)) return;
            keyStates.set(key, Date.now());
            
            highlightKey(key, true);
            
            if (!testedKeys.has(key)) {
                testedKeys.add(key);
                markKeyAsTested(key);
                updateProgress();
            }
            
            sendKeyEvent(key, 'down');
        }

        function handleKeyUp(event) {
            const key = normalizeKey(event.key);
            keyStates.delete(key);
            highlightKey(key, false);
            sendKeyEvent(key, 'up');
        }

        function normalizeKey(key) {
            const keyMap = {
                ' ': 'Space',
                'Control': 'Control',
                'Meta': 'Meta',
                'Alt': 'Alt'
            };
            return keyMap[key] || key.toLowerCase();
        }

        function highlightKey(key, pressed) {
            const keyElement = document.querySelector(`[data-key="${key}"]`) || 
                             document.querySelector(`[data-key="${key.toLowerCase()}"]`);
            
            if (keyElement) {
                keyElement.classList.toggle('pressed', pressed);
            }
        }

        function markKeyAsTested(key) {
            const keyElement = document.querySelector(`[data-key="${key}"]`) || 
                             document.querySelector(`[data-key="${key.toLowerCase()}"]`);
            
            if (keyElement) {
                keyElement.classList.add('tested');
            }
        }

        function updateProgressFromBackend(data) {
            if (data.operation_text) {
                document.getElementById('instruction').textContent = data.operation_text;
            }
            
            if (data.pressed_keys_count !== undefined && data.total_keys) {
                const progress = (data.pressed_keys_count / data.total_keys) * 100;
                document.getElementById('keys-tested').textContent = data.pressed_keys_count;
                document.getElementById('progress-fill').style.width = `${progress}%`;
                document.getElementById('progress-text').textContent = `${Math.round(progress)}% Complete`;
                document.getElementById('progress-percent').textContent = `${Math.round(progress)}%`;
                
                if (data.pressed_keys) {
                    data.pressed_keys.forEach(key => {
                        markKeyAsTested(key);
                    });
                }
                
                document.getElementById('complete-btn').disabled = progress < 80;
            }
            
            if (data.debug_log && data.debug_log.length > 0) {
                data.debug_log.forEach(logMessage => {
                    addDebugLog(logMessage);
                });
            }
            
            if (data.test_complete) {
                setTimeout(() => {
                    window.close();
                }, 2000);
            }
        }
        
        function updateProgress() {
            const totalKeys = document.querySelectorAll('.key').length;
            const testedCount = testedKeys.size;
            const progress = (testedCount / totalKeys) * 100;
            
            row.forEach(keyCode => {
                const keyElement = document.createElement('div');
                keyElement.className = 'key';
                keyElement.dataset.key = keyCode;
                
                if (keyCode === ' ') keyElement.classList.add('space');
                else if (keyCode === 'Enter') keyElement.classList.add('enter');
                else if (keyCode === 'Shift') keyElement.classList.add('shift');
                
                keyElement.textContent = keyCode === ' ' ? 'Space' : keyCode.toUpperCase();
                rowElement.appendChild(keyElement);
            });
            
            keyboardVisual.appendChild(rowElement);
        });
    }

    function setupEventListeners() {
        document.addEventListener('keydown', handleKeyDown);
        document.addEventListener('keyup', handleKeyUp);
        document.body.tabIndex = 0;
        document.body.focus();
    }

    function handleKeyDown(event) {
        const key = normalizeKey(event.key);
        
        if (keyStates.has(key)) return;
        keyStates.set(key, Date.now());
        
        highlightKey(key, true);
        
        if (!testedKeys.has(key)) {
            testedKeys.add(key);
            markKeyAsTested(key);
            updateProgress();
        }
        
        sendKeyEvent(key, 'down');
    }

    function handleKeyUp(event) {
        const key = normalizeKey(event.key);
        keyStates.delete(key);
        highlightKey(key, false);
        sendKeyEvent(key, 'up');
    }

    function normalizeKey(key) {
        const keyMap = {
            ' ': 'Space',
            'Control': 'Control',
            'Meta': 'Meta',
            'Alt': 'Alt'
        };
        return keyMap[key] || key.toLowerCase();
    }

    function highlightKey(key, pressed) {
        const keyElement = document.querySelector(`[data-key="${key}"]`) || 
                         document.querySelector(`[data-key="${key.toLowerCase()}"]`);
        
        if (keyElement) {
            keyElement.classList.toggle('pressed', pressed);
        }
    }

    function markKeyAsTested(key) {
        const keyElement = document.querySelector(`[data-key="${key}"]`) || 
                         document.querySelector(`[data-key="${key.toLowerCase()}"]`);
        
        if (keyElement) {
            keyElement.classList.add('tested');
        }
    }

    function updateProgressFromBackend(data) {
        if (data.operation_text) {
            document.getElementById('instruction').textContent = data.operation_text;
        }
        
        if (data.pressed_keys_count !== undefined && data.total_keys) {
            const progress = (data.pressed_keys_count / data.total_keys) * 100;
            document.getElementById('keys-tested').textContent = data.pressed_keys_count;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            document.getElementById('progress-text').textContent = `${Math.round(progress)}% Complete`;
            document.getElementById('progress-percent').textContent = `${Math.round(progress)}%`;
            
            if (data.pressed_keys) {
                data.pressed_keys.forEach(key => {
                    markKeyAsTested(key);
                });
            }
            
            document.getElementById('complete-btn').disabled = progress < 80;
        }
        
        if (data.debug_log && data.debug_log.length > 0) {
            data.debug_log.forEach(logMessage => {
                addDebugLog(logMessage);
            });
            
            updateProgress();
        }

        async function completeTest() {
            console.log('Enhanced keyboard test completed');
            
            // Show completion message
            document.getElementById('instruction').textContent = '✅ Keyboard Test Complete - Well done!';
            
            // Close window after delay
            setTimeout(() => {
                window.close();
            }, 2000);
        }

        window.addEventListener('load', initializeTest);
    </script>
</body>
</html>
