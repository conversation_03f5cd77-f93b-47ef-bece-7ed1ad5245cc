<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pointing Device Test - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dark-theme.css') }}">
    <style>
        /* Mirror visual_test.html fullscreen overlay behavior */
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --background: #1E1E1E;
            --overlay: rgba(0, 0, 0, 0.45);
            --overlay-weak: rgba(0, 0, 0, 0.35);
            --text: #FFFFFF;
            --muted: #bbbbbb;
            --accent: #5565f7;
            --danger: #dc3545;
            --success: #28a745;
            --warning: #ff9800;
            --outline: #d9d9d9;
            --panel-radius: 8px;
        }

        body {
            background-color: var(--background);
            color: var(--text);
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Use same overlay pattern as visual_test */
        .fullscreen-overlay {
            position: fixed;
            top: 0; left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--background);
            z-index: 9999;
            display: flex;
            flex-direction: column;
        }

        .instructions-header {
            position: absolute;
            top: 16px;
            left: 50%;
            transform: translateX(-50%);
            padding: 10px 16px;
            background-color: var(--overlay);
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            z-index: 10000;
            user-select: none;
        }

        /* Canvas sits in the main area like memory canvas in visual_test */
        #testCanvas {
            flex: 1;
            background-color: var(--background);
            display: block;
        }

        .custom-cursor {
            position: absolute;
            width: 20px;
            height: 20px;
            background-image: url("{{ url_for('static', filename='assets/custom_cursor.png') }}");
            background-size: contain;
            background-repeat: no-repeat;
            pointer-events: none;
            z-index: 10001;
            transform: translate(-50%, -50%);
            image-rendering: pixelated;
        }

        .pointing-test-info-bar {
            position: absolute;
            bottom: 64px;
            left: 16px;
            right: 16px;
            display: flex;
            justify-content: space-around;
            padding: 8px 10px;
            background-color: var(--overlay-weak);
            border-radius: 6px;
            font-size: 12px;
            z-index: 10000;
            user-select: none;
            backdrop-filter: blur(2px);
        }
        .info-item { margin: 0 10px; }
        .info-item.pass { color: var(--success); }
        .info-item.fail { color: var(--danger); }
        .info-item.neutral { color: var(--muted); }
        .info-item.active { color: var(--warning); }

        .pointing-test-controls {
            position: absolute;
            bottom: 16px;
            right: 16px;
            display: flex;
            gap: 12px;
            z-index: 10000;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: var(--panel-radius);
            cursor: pointer;
            transition: transform 80ms ease, filter 150ms ease, background-color 150ms ease;
        }
        .btn:focus-visible {
            outline: 2px solid var(--outline);
            outline-offset: 2px;
        }
        .btn:hover { filter: brightness(1.05); }
        .btn:active { transform: translateY(1px); }
        .btn.btn-danger { background-color: var(--danger); color: #fff; }
        .btn.btn-primary { background-color: var(--accent); color: #fff; }
        .btn:disabled { background-color: #555; cursor: not-allowed; filter: none; }

        @media (prefers-reduced-motion: reduce) {
            .btn { transition: none; }
        }
    </style>
</head>
<body>
    <div class="fullscreen-overlay" id="overlayRoot">
        <div id="customCursor" class="custom-cursor" style="display:none;"></div>
        <div id="instructionsHeader" class="instructions-header" aria-live="polite" role="status">
            Move pointer, click buttons, and use touchpad. Press ESC to complete.
        </div>

        <canvas id="testCanvas"></canvas>

        <div class="pointing-test-info-bar" id="infoBar">
            <span class="info-item neutral" id="infoPointerMoved">Pointer Movement</span>
            <span class="info-item neutral" id="infoTouchpad">Touchpad Interaction</span>
            <span class="info-item neutral" id="infoStickTarget">Pointer Stick Target</span>
            <span class="info-item neutral" id="infoLeftBtn">Left Btn</span>
            <span class="info-item neutral" id="infoMidBtn">Middle Btn</span>
            <span class="info-item neutral" id="infoRightBtn">Right Btn</span>
        </div>

        <div class="pointing-test-controls">
            <button id="forceCompleteBtn" class="btn btn-danger" title="Mark test complete with issues" aria-label="Force complete with issue">Force Complete (Issue)</button>
            <button id="completeTestBtn" class="btn btn-primary" title="Mark test complete" aria-label="Test finished">Test Finished</button>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let eventSource = null;
        let completing = false;
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const customCursor = document.getElementById('customCursor');
        const DPR = Math.max(1, Math.floor(window.devicePixelRatio || 1));
        let resizeTimer = null;

        // Ensure the overlay doesn't block pointer interactions with the canvas/buttons.
        // These styles are safe to enforce here in case any global CSS interferes.
        (function ensurePointerEvents() {
            const overlay = document.getElementById('overlayRoot');
            const infoBar = document.getElementById('infoBar');
            const instructions = document.getElementById('instructionsHeader');
            const controls = document.querySelector('.pointing-test-controls');
            if (overlay) overlay.style.pointerEvents = 'none';
            if (canvas) canvas.style.pointerEvents = 'auto';
            if (customCursor) customCursor.style.pointerEvents = 'none';
            if (infoBar) infoBar.style.pointerEvents = 'auto';
            if (instructions) instructions.style.pointerEvents = 'auto';
            if (controls) controls.style.pointerEvents = 'auto';
        })();
        // Hide custom cursor if asset fails to load
        (function guardCustomCursor() {
            if (!customCursor) return;
            const testImg = new Image();
            testImg.onload = () => {};
            testImg.onerror = () => { customCursor.style.display = 'none'; };
            testImg.src = "{{ url_for('static', filename='assets/custom_cursor.png') }}";
        })();

        // Test element definitions (positions, sizes, colors)
        const testElements = {
            pointerStickTarget: { x: 0, y: 0, radius: 40, color: '#CC3333', outline: 'white', status: 'untested', text: '≡' },
            touchpadZone: { x: 0, y: 0, width: 400, height: 180, color: '#222222', outline: '#444444', status: 'untested', text: 'TOUCHPAD AREA' },
            buttons: [
                { id: 'button_ui_left_top', text: 'L-CLICK', physical: 'left', x_offset: -100, y_offset: -50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad' },
                { id: 'button_ui_middle_top', text: 'M-CLICK', physical: 'middle', x_offset: 0, y_offset: -50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad' },
                { id: 'button_ui_right_top', text: 'R-CLICK', physical: 'right', x_offset: 100, y_offset: -50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad' },
                { id: 'button_ui_left_bottom', text: 'L-CLICK', physical: 'left', x_offset: -100, y_offset: 50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad_bottom' },
                { id: 'button_ui_middle_bottom', text: 'M-CLICK', physical: 'middle', x_offset: 0, y_offset: 50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad_bottom' },
                { id: 'button_ui_right_bottom', text: 'R-CLICK', physical: 'right', x_offset: 100, y_offset: 50, width: 100, height: 40, color: '#222222', outline: 'white', status: 'untested', position_base: 'touchpad_bottom' }
            ]
        };
        let calculatedElements = {};

        function resizeCanvas() {
            const doResize = () => {
                const overlay = document.getElementById('overlayRoot');
                if (!overlay) return;
                const rect = overlay.getBoundingClientRect();

                canvas.style.position = 'absolute';
                canvas.style.left = '0px';
                canvas.style.top = '0px';

                const headerEl = document.getElementById('instructionsHeader');
                const infoBarEl = document.getElementById('infoBar');
                const controlsEl = document.querySelector('.pointing-test-controls');

                const headerBox = headerEl ? headerEl.getBoundingClientRect() : { height: 48 };
                const infoBox = infoBarEl ? infoBarEl.getBoundingClientRect() : { height: 64 };
                const controlsBox = controlsEl ? controlsEl.getBoundingClientRect() : { height: 56 };

                const verticalReserve = Math.round((headerBox.height || 48) + (infoBox.height || 64) + (controlsBox.height || 56) + 24);

                const cssW = Math.max(0, Math.floor(rect.width));
                const cssH = Math.max(0, Math.floor(rect.height - verticalReserve));

                // Apply CSS size
                canvas.style.width = cssW + 'px';
                canvas.style.height = cssH + 'px';

                // Backing store size for crisp rendering
                const targetW = Math.max(1, Math.floor(cssW * DPR));
                const targetH = Math.max(1, Math.floor(cssH * DPR));
                if (canvas.width !== targetW || canvas.height !== targetH) {
                    canvas.width = targetW;
                    canvas.height = targetH;
                    ctx.setTransform(DPR, 0, 0, DPR, 0, 0); // scale drawing back to CSS pixels
                }

                window.__overlayOffset = { x: rect.left, y: rect.top };
                calculateElementPositions(cssW, cssH);
                drawTestElements();
            };

            if (resizeTimer) clearTimeout(resizeTimer);
            resizeTimer = setTimeout(doResize, 50);
        }

        function calculateElementPositions(cssW, cssH) {
            const W = Math.max(0, cssW ?? Math.floor(canvas.width / DPR));
            const H = Math.max(0, cssH ?? Math.floor(canvas.height / DPR));
            const centerX = W / 2;

            const round = (v) => Math.round(v);

            calculatedElements.pointerStickTarget = {
                ...testElements.pointerStickTarget,
                x: round(centerX),
                y: round(Math.max(50, H * 0.15))
            };

            calculatedElements.touchpadZone = {
                ...testElements.touchpadZone,
                x: round(centerX - testElements.touchpadZone.width / 2),
                y: round(Math.min(H - testElements.touchpadZone.height - 60, H * 0.55))
            };
            const tz = calculatedElements.touchpadZone;

            calculatedElements.buttons = testElements.buttons.map(btn => {
                let baseX, baseY;
                if (btn.position_base === 'touchpad') {
                    baseX = tz.x + tz.width / 2;
                    baseY = tz.y;
                } else {
                    baseX = tz.x + tz.width / 2;
                    baseY = tz.y + tz.height;
                }
                return {
                    ...btn,
                    rect_x: round(baseX + btn.x_offset - btn.width / 2),
                    rect_y: round(baseY + btn.y_offset - btn.height / 2),
                    text_x: round(baseX + btn.x_offset),
                    text_y: round(baseY + btn.y_offset)
                };
            });
        }

        function drawTestElements() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.save();
            ctx.scale(1, 1); // drawing already scaled via setTransform
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            const pst = calculatedElements.pointerStickTarget;
            if (pst) {
                ctx.beginPath();
                ctx.arc(pst.x, pst.y, pst.radius, 0, Math.PI * 2);
                ctx.fillStyle = pst.status === 'pass' ? 'green' : (pst.status === 'active' ? 'orange' : pst.color);
                ctx.fill();
                ctx.strokeStyle = pst.outline;
                ctx.lineWidth = 2;
                ctx.stroke();
                ctx.fillStyle = 'white';
                ctx.fillText(pst.text, pst.x, pst.y);
            }

            const tz = calculatedElements.touchpadZone;
            if (tz) {
                ctx.fillStyle = tz.status === 'pass' ? 'darkgreen' : tz.color;
                ctx.strokeStyle = tz.outline;
                ctx.lineWidth = 2;
                ctx.fillRect(tz.x, tz.y, tz.width, tz.height);
                ctx.strokeRect(tz.x, tz.y, tz.width, tz.height);
                ctx.fillStyle = 'white';
                ctx.fillText(tz.text, tz.x + tz.width / 2, tz.y + tz.height / 2);
            }

            if (calculatedElements.buttons) {
                calculatedElements.buttons.forEach(btn => {
                    ctx.fillStyle = btn.status === 'pass' ? 'green' : (btn.status === 'fail' ? 'red' : btn.color);
                    ctx.strokeStyle = btn.outline;
                    ctx.lineWidth = 2;
                    ctx.fillRect(btn.rect_x, btn.rect_y, btn.width, btn.height);
                    ctx.strokeRect(btn.rect_x, btn.rect_y, btn.width, btn.height);
                    ctx.fillStyle = 'white';
                    ctx.fillText(btn.text, btn.text_x, btn.text_y);
                });
            }
            ctx.restore();
        }

        function updateInfoBar(progress) {
            const setStatusClass = (elementId, status) => {
                const el = document.getElementById(elementId);
                if (!el) return;
                if (status === 'pass') el.className = 'info-item pass';
                else if (status === 'fail') el.className = 'info-item fail';
                else if (status === 'active' || status === 'entered') el.className = 'info-item active';
                else el.className = 'info-item neutral';
            };

            setStatusClass('infoPointerMoved', progress["Pointer Movement"]);
            setStatusClass('infoTouchpad', progress["Touchpad Interaction"]);

            let stickDisplayStatus = progress["Pointer Stick Target"];
            if (stickDisplayStatus === 'untested' && progress._pointer_stick_target_entered_flag) {
                stickDisplayStatus = 'active';
            }
            setStatusClass('infoStickTarget', stickDisplayStatus);

            setStatusClass('infoLeftBtn', progress["Left Button Physical"]);
            setStatusClass('infoMidBtn', progress["Middle Button Physical"]);
            setStatusClass('infoRightBtn', progress["Right Button Physical"]);
        }

        async function initTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'POINTTEST001';

            // Request fullscreen from an explicit user-gesture right away to satisfy Chromium gesture policy.
            // Mimic LCD/keyboard success path by attaching to the first pointerdown/keydown.
            const requestFs = async () => {
                try {
                    if (!document.fullscreenElement && document.documentElement.requestFullscreen) {
                        await document.documentElement.requestFullscreen();
                    }
                } catch (e) {
                    // ignore, layout still fills viewport
                }
            };
            // Attach once; remove after it fires to avoid duplicate calls
            const fsOnce = async (ev) => {
                document.removeEventListener('pointerdown', fsOnce, true);
                document.removeEventListener('keydown', fsOnce, true);
                await requestFs();
            };
            document.addEventListener('pointerdown', fsOnce, true);
            document.addEventListener('keydown', fsOnce, true);
            // Also try immediately (works if opener already granted gesture context)
            requestFs();

            // React to fullscreen transitions to keep canvas sized perfectly
            const onFsChange = () => resizeCanvas();
            document.addEventListener('fullscreenchange', onFsChange, { passive: true });

            resizeCanvas();
            window.addEventListener('resize', resizeCanvas, { passive: true });

            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ test_type: 'pointing', asset_number: assetNumber, test_params: {} })
                });
                const result = await response.json();
                if (!response.ok) throw new Error(result.error || 'Failed to start pointing test');
                currentSessionId = result.session_id;
                connectToProgressStream();
            } catch (error) {
                console.error('Error starting test:', error);
                document.getElementById('instructionsHeader').textContent = 'Error: ' + error.message;
            }

            document.getElementById('completeTestBtn').addEventListener('click', () => completeTest(false), { once: false });
            document.getElementById('forceCompleteBtn').addEventListener('click', () => completeTest(true), { once: false });
            
            // Ensure initial geometry is correct before binding listeners
            resizeCanvas();
            setupEventListeners();
            document.addEventListener('contextmenu', event => event.preventDefault());
        }

        function connectToProgressStream() {
            if (!currentSessionId) return;
            const connect = () => {
                eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);
                eventSource.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.error) {
                        console.error('SSE Error:', data.error);
                        return;
                    }
                    updateUiFromProgress(data.progress);
                    if (data.test_complete) handleTestComplete(data.test_result);
                };
                eventSource.onerror = function(err) {
                    console.error('EventSource failed:', err);
                    if (eventSource) {
                        eventSource.close();
                        eventSource = null;
                    }
                    // simple backoff reconnect
                    setTimeout(() => {
                        if (!completing) connect();
                    }, 1000);
                };
            };
            connect();
        }

        function updateUiFromProgress(progress) {
            if (calculatedElements.pointerStickTarget && progress["Pointer Stick Target"]) {
                if (progress["Pointer Stick Target"] === 'pass') {
                    calculatedElements.pointerStickTarget.status = 'pass';
                } else if (progress._pointer_stick_target_entered_flag) {
                    calculatedElements.pointerStickTarget.status = 'active';
                } else {
                    calculatedElements.pointerStickTarget.status = 'untested';
                }
            }
            if (calculatedElements.touchpadZone && progress["Touchpad Interaction"]) {
                calculatedElements.touchpadZone.status = progress["Touchpad Interaction"] === 'pass' ? 'pass' : 'untested';
            }
            if (calculatedElements.buttons && progress.ui_test_elements_status) {
                calculatedElements.buttons.forEach(btn => {
                    btn.status = progress.ui_test_elements_status[btn.id] || 'untested';
                });
            }
            updateInfoBar(progress);
            drawTestElements();
        }

        function setupEventListeners() {
            // Only attach on the canvas to avoid interference from overlay containers that might block bubbling
            canvas.addEventListener('mousemove', handleMouseMove, { capture: true, passive: true });
            canvas.addEventListener('mousedown', handleMouseDown, { capture: true });

            // Also attach to document as fallback if canvas sizing changes
            document.addEventListener('mousemove', handleMouseMove, { capture: true, passive: true });
            document.addEventListener('mousedown', handleMouseDown, { capture: true });

            // Prevent context menu globally for test determinism - attach once
            const preventContext = (event) => event.preventDefault();
            document.addEventListener('contextmenu', preventContext, { capture: true });

            // ESC to complete
            const escHandler = function(event) {
                if (event.key === 'Escape') {
                    completeTest(false);
                }
            };
            document.addEventListener('keydown', escHandler, true);

            // Store for cleanup
            window.__pd_handlers = { preventContext, escHandler };
        }

        function handleMouseMove(event) {
            // Normalize to CSS pixel space regardless of DPR
            const rect = canvas.getBoundingClientRect();
            let x = event.clientX - rect.left;
            let y = event.clientY - rect.top;

            // Clamp
            x = Math.max(0, Math.min(rect.width, x));
            y = Math.max(0, Math.min(rect.height, y));

            // Always show custom cursor based on actual mouse position
            if (customCursor) {
                customCursor.style.left = event.clientX + 'px';
                customCursor.style.top = event.clientY + 'px';
                customCursor.style.display = 'block';
            }

            let event_type = null;

            const tz = calculatedElements.touchpadZone;
            if (tz && x >= tz.x && x <= tz.x + tz.width && y >= tz.y && y <= tz.y + tz.height) {
                event_type = 'touchpad_enter';
            }

            const pst = calculatedElements.pointerStickTarget;
            if (pst) {
                const dist = Math.hypot(x - pst.x, y - pst.y);
                if (dist <= pst.radius) {
                    event_type = event_type || 'pointer_stick_target_enter';
                } else if (pst.status === 'active') {
                    event_type = event_type || 'pointer_stick_target_exit';
                }
            }

            sendEventToServer({ action: 'mouse_move', coordinates: { x, y }, event_type });
        }

        function handleMouseDown(event) {
            const rect = canvas.getBoundingClientRect();

            let clickX = event.clientX - rect.left;
            let clickY = event.clientY - rect.top;

            // Clamp
            clickX = Math.max(0, Math.min(rect.width, clickX));
            clickY = Math.max(0, Math.min(rect.height, clickY));

            let buttonType;
            switch (event.button) {
                case 0: buttonType = 'left'; break;
                case 1: buttonType = 'middle'; break;
                case 2: buttonType = 'right'; break;
                default: buttonType = `button_${event.button}`;
            }

            let uiElementId = null;
            if (calculatedElements.buttons) {
                for (const btn of calculatedElements.buttons) {
                    if (clickX >= btn.rect_x && clickX <= btn.rect_x + btn.width &&
                        clickY >= btn.rect_y && clickY <= btn.rect_y + btn.height) {
                        uiElementId = btn.id;
                        if (btn.physical !== buttonType) {
                            console.warn(`UI element ${uiElementId} clicked with ${buttonType}, expected ${btn.physical}`);
                        }
                        break;
                    }
                }
            }
            sendEventToServer({ action: 'button_click', button_type: buttonType, ui_element_id: uiElementId, coordinates: { x: clickX, y: clickY } });
        }

        async function sendEventToServer(eventData) {
            if (!currentSessionId) return;
            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(eventData)
                });
            } catch (error) {
                console.error('Error sending event to server:', error);
            }
        }

        async function completeTest(isForced) {
            if (!currentSessionId || completing) return;
            completing = true;

            const hdr = document.getElementById('instructionsHeader');
            if (hdr) hdr.textContent = 'Completing test...';

            const completeBtn = document.getElementById('completeTestBtn');
            const forceBtn = document.getElementById('forceCompleteBtn');
            if (completeBtn) completeBtn.disabled = true;
            if (forceBtn) forceBtn.disabled = true;

            try {
                if (eventSource) {
                    eventSource.close();
                    eventSource = null;
                }

                const response = await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'complete_test', force_complete: isForced })
                });

                const result = await response.json();

                if (result && result.test_result) {
                    handleTestComplete(result.test_result);
                } else {
                    console.error('Unexpected response structure:', result);
                    if (hdr) hdr.textContent = 'Test completed - closing window...';
                    setTimeout(closeWindow, 1500);
                }

                fetch(`/api/visual_test/stop/${currentSessionId}`, { method: 'POST' })
                    .catch(err => console.warn("Could not send stop signal:", err));
            } catch (error) {
                console.error('Error completing test:', error);
                if (hdr) hdr.textContent = 'Test completed with error - closing window...';
                setTimeout(closeWindow, 1500);
            }
        }

        function handleTestComplete(result) {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }

            canvas.removeEventListener('mousemove', handleMouseMove, true);
            canvas.removeEventListener('mousedown', handleMouseDown, true);
            document.removeEventListener('mousemove', handleMouseMove, true);
            document.removeEventListener('mousedown', handleMouseDown, true);

            if (window.__pd_handlers) {
                document.removeEventListener('contextmenu', window.__pd_handlers.preventContext, true);
                document.removeEventListener('keydown', window.__pd_handlers.escHandler, true);
                window.__pd_handlers = null;
            }

            const completeBtn = document.getElementById('completeTestBtn');
            const forceBtn = document.getElementById('forceCompleteBtn');
            if (completeBtn) completeBtn.disabled = true;
            if (forceBtn) forceBtn.disabled = true;

            if (customCursor) customCursor.style.display = 'none';

            const hdr = document.getElementById('instructionsHeader');
            if (hdr) hdr.textContent = 'Test completed - closing window...';

            setTimeout(closeWindow, 1500);
        }

        function closeWindow() {
            if (window.opener && !window.opener.closed) {
                window.close();
            } else {
                window.location.href = '/';
            }
        }

        window.addEventListener('load', initTest);
        window.addEventListener('beforeunload', () => {
            if (eventSource) eventSource.close();
        });
    </script>
    <script type="module">
      import { initKiosk } from '/static/js/modules/kiosk_fullscreen.js';
      const kiosk = initKiosk({ allowEscToClose: true, suppressQuitCombos: true, reenterOnVisibility: true, stickyPointerLock: false });
      window.addEventListener('load', () => kiosk.ensureFullscreen && kiosk.ensureFullscreen());
    </script>
</body>
</html>
