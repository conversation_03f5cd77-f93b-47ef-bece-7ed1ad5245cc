<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hardware Detection - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .detection-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: var(--container-padding);
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .detection-header {
            text-align: center;
            margin-bottom: var(--space-2xl);
        }

        .detection-title {
            font-size: var(--font-size-3xl);
            font-weight: 600;
            margin-bottom: var(--space-md);
            color: var(--accent-primary);
        }

        .detection-subtitle {
            font-size: var(--font-size-lg);
            color: var(--text-secondary);
            margin-bottom: var(--space-xl);
        }

        .progress-section {
            width: 100%;
            max-width: 600px;
            margin-bottom: var(--space-2xl);
        }

        .progress-bar-container {
            background: var(--bg-secondary);
            border-radius: var(--btn-border-radius);
            height: 12px;
            overflow: hidden;
            margin-bottom: var(--space-md);
            border: 1px solid var(--border-color);
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
            transition: width var(--transition-normal);
            border-radius: var(--btn-border-radius);
        }

        .progress-text {
            text-align: center;
            font-size: var(--font-size-base);
            color: var(--text-secondary);
        }

        .hardware-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: var(--card-gap);
            width: 100%;
            max-width: 1000px;
            margin-bottom: var(--space-2xl);
        }

        .hardware-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
            transition: border-color var(--transition-fast);
        }

        .hardware-card.detected {
            border-color: var(--status-pass);
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.2);
        }

        .hardware-card.warning {
            border-color: var(--status-warning);
            box-shadow: 0 0 8px rgba(255, 152, 0, 0.2);
        }

        .hardware-card.error {
            border-color: var(--status-fail);
            box-shadow: 0 0 8px rgba(244, 67, 54, 0.2);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-md);
        }

        .card-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--status-pending);
            transition: background-color var(--transition-fast);
        }

        .status-indicator.detecting {
            background: var(--status-running);
            animation: pulse 2s infinite;
        }

        .status-indicator.detected {
            background: var(--status-pass);
        }

        .status-indicator.warning {
            background: var(--status-warning);
        }

        .status-indicator.error {
            background: var(--status-fail);
        }

        .card-content {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            line-height: 1.5;
        }

        .spec-item {
            display: flex;
            justify-content: space-between;
            margin: var(--space-xs) 0;
            padding: var(--space-xs) 0;
            border-bottom: 1px solid var(--bg-secondary);
        }

        .spec-item:last-child {
            border-bottom: none;
        }

        .spec-label {
            font-weight: 500;
            color: var(--text-primary);
        }

        .spec-value {
            color: var(--text-secondary);
            text-align: right;
        }

        .recommendations-section {
            width: 100%;
            max-width: 800px;
            margin-bottom: var(--space-2xl);
        }

        .recommendations-card {
            background: var(--bg-card);
            border: 1px solid var(--border-color);
            border-radius: var(--btn-border-radius);
            padding: var(--card-padding);
        }

        .recommendations-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--space-md);
            display: flex;
            align-items: center;
        }

        .recommendation-item {
            display: flex;
            align-items: flex-start;
            margin: var(--space-sm) 0;
            padding: var(--space-sm);
            background: var(--bg-secondary);
            border-radius: calc(var(--btn-border-radius) / 2);
        }

        .recommendation-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: var(--space-sm);
            flex-shrink: 0;
            margin-top: 2px;
        }

        .recommendation-icon.info {
            background: var(--accent-secondary);
        }

        .recommendation-icon.warning {
            background: var(--status-warning);
        }

        .recommendation-icon.error {
            background: var(--status-fail);
        }

        .recommendation-text {
            flex: 1;
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            margin-top: var(--space-xl);
        }

        .btn {
            padding: var(--btn-padding-md);
            border: none;
            border-radius: var(--btn-border-radius);
            font-size: var(--btn-font-size);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            min-width: 120px;
        }

        .btn-primary {
            background: var(--accent-primary);
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .btn-secondary:hover {
            background: var(--bg-card);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .detection-footer {
            text-align: center;
            padding: var(--space-lg);
            color: var(--text-muted);
            font-size: var(--font-size-sm);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hardware-grid {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .detection-title {
                font-size: var(--font-size-2xl);
            }
        }
    </style>
</head>
<body>
    <div class="detection-container">
        <!-- Header -->
        <div class="detection-header">
            <h1 class="detection-title">Hardware Detection</h1>
            <p class="detection-subtitle">Analyzing system components and capabilities...</p>
        </div>

        <!-- Progress Section -->
        <div class="progress-section">
            <div class="progress-bar-container">
                <div class="progress-bar" id="detection-progress" style="width: 0%"></div>
            </div>
            <div class="progress-text" id="progress-text">Initializing hardware detection...</div>
        </div>

        <!-- Hardware Grid -->
        <div class="hardware-grid" id="hardware-grid">
            <!-- Hardware cards will be populated dynamically -->
        </div>

        <!-- Recommendations Section -->
        <div class="recommendations-section" id="recommendations-section" style="display: none;">
            <div class="recommendations-card">
                <h3 class="recommendations-title">
                    <span style="margin-right: var(--space-sm);">💡</span>
                    System Recommendations
                </h3>
                <div id="recommendations-list">
                    <!-- Recommendations will be populated dynamically -->
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <button class="btn btn-secondary" onclick="retryDetection()" id="retry-btn" style="display: none;">
                Retry Detection
            </button>
            <button class="btn btn-primary" onclick="proceedToTesting()" id="proceed-btn" disabled>
                Continue to Testing
            </button>
        </div>
    </div>

    <!-- Footer -->
    <div class="detection-footer">
        Crucible Hardware Detection System | Press F5 to refresh
    </div>

    <!-- Include Hardware Detector -->
    <script src="{{ url_for('static', filename='js/modules/hardware_detector.js') }}"></script>
    
    <script>
        let detectionComplete = false;
        let hardwareData = {};

        // Hardware component templates
        const hardwareComponents = [
            { id: 'display', name: 'Display', icon: '🖥️' },
            { id: 'memory', name: 'Memory', icon: '🧠' },
            { id: 'cpu', name: 'Processor', icon: '⚡' },
            { id: 'graphics', name: 'Graphics', icon: '🎨' },
            { id: 'input', name: 'Input Devices', icon: '⌨️' },
            { id: 'network', name: 'Network', icon: '🌐' }
        ];

        // Initialize detection process
        async function initializeDetection() {
            createHardwareCards();
            await runDetectionSequence();
        }

        // Create hardware cards
        function createHardwareCards() {
            const grid = document.getElementById('hardware-grid');
            
            hardwareComponents.forEach(component => {
                const card = document.createElement('div');
                card.className = 'hardware-card';
                card.id = `card-${component.id}`;
                
                card.innerHTML = `
                    <div class="card-header">
                        <div class="card-title">
                            <span style="margin-right: var(--space-sm);">${component.icon}</span>
                            ${component.name}
                        </div>
                        <div class="status-indicator detecting" id="status-${component.id}"></div>
                    </div>
                    <div class="card-content" id="content-${component.id}">
                        <div style="color: var(--text-muted);">Detecting...</div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // Run detection sequence
        async function runDetectionSequence() {
            const progressBar = document.getElementById('detection-progress');
            const progressText = document.getElementById('progress-text');
            
            try {
                // Wait for hardware detector to initialize
                if (!window.CrucibleHardwareDetector) {
                    throw new Error('Hardware detector not available');
                }

                await window.CrucibleHardwareDetector.initialize();
                hardwareData = window.CrucibleHardwareDetector.hardwareInfo;

                // Simulate progressive detection for better UX
                const detectionSteps = [
                    { component: 'display', progress: 20, delay: 500 },
                    { component: 'memory', progress: 35, delay: 300 },
                    { component: 'cpu', progress: 50, delay: 400 },
                    { component: 'graphics', progress: 70, delay: 600 },
                    { component: 'input', progress: 85, delay: 300 },
                    { component: 'network', progress: 100, delay: 200 }
                ];

                for (const step of detectionSteps) {
                    await new Promise(resolve => setTimeout(resolve, step.delay));
                    
                    progressBar.style.width = `${step.progress}%`;
                    progressText.textContent = `Detecting ${step.component}...`;
                    
                    updateHardwareCard(step.component);
                }

                // Complete detection
                progressText.textContent = 'Hardware detection complete!';
                showRecommendations();
                enableProceedButton();
                detectionComplete = true;

            } catch (error) {
                console.error('Detection failed:', error);
                handleDetectionError(error);
            }
        }

        // Update individual hardware card
        function updateHardwareCard(componentId) {
            const card = document.getElementById(`card-${componentId}`);
            const status = document.getElementById(`status-${componentId}`);
            const content = document.getElementById(`content-${componentId}`);
            
            if (!hardwareData || !card) return;

            let cardData = {};
            let cardStatus = 'detected';
            
            switch (componentId) {
                case 'display':
                    cardData = {
                        'Resolution': `${hardwareData.screen?.width || 'Unknown'}x${hardwareData.screen?.height || 'Unknown'}`,
                        'Color Depth': `${hardwareData.screen?.colorDepth || 'Unknown'} bit`,
                        'Pixel Ratio': hardwareData.screen?.devicePixelRatio || 'Unknown',
                        'Orientation': hardwareData.screen?.orientation || 'Unknown'
                    };
                    break;
                    
                case 'memory':
                    if (hardwareData.memory) {
                        cardData = {
                            'JS Heap Used': `${hardwareData.memory.usedJSHeapSize} MB`,
                            'JS Heap Total': `${hardwareData.memory.totalJSHeapSize} MB`,
                            'JS Heap Limit': `${hardwareData.memory.jsHeapSizeLimit} MB`
                        };
                    } else {
                        cardData = { 'Status': 'Memory info not available' };
                        cardStatus = 'warning';
                    }
                    break;
                    
                case 'cpu':
                    cardData = {
                        'Logical Processors': hardwareData.cpu?.logicalProcessors || 'Unknown',
                        'Platform': hardwareData.platform?.platform || 'Unknown'
                    };
                    break;
                    
                case 'graphics':
                    cardData = {
                        'WebGL Support': hardwareData.capabilities?.webgl ? 'Yes' : 'No',
                        'Canvas Support': hardwareData.capabilities?.canvas ? 'Yes' : 'No',
                        'Performance': hardwareData.capabilities?.graphics?.category || 'Unknown'
                    };
                    if (!hardwareData.capabilities?.webgl) {
                        cardStatus = 'warning';
                    }
                    break;
                    
                case 'input':
                    cardData = {
                        'Touch Support': hardwareData.capabilities?.touch ? 'Yes' : 'No',
                        'Mouse Support': hardwareData.capabilities?.mouse ? 'Yes' : 'No',
                        'Keyboard': 'Available'
                    };
                    break;
                    
                case 'network':
                    if (hardwareData.network) {
                        cardData = {
                            'Connection Type': hardwareData.network.effectiveType || 'Unknown',
                            'Downlink': `${hardwareData.network.downlink || 'Unknown'} Mbps`,
                            'RTT': `${hardwareData.network.rtt || 'Unknown'} ms`
                        };
                    } else {
                        cardData = {
                            'Status': hardwareData.platform?.onLine ? 'Online' : 'Offline'
                        };
                    }
                    break;
            }

            // Update card content
            content.innerHTML = Object.entries(cardData)
                .map(([key, value]) => `
                    <div class="spec-item">
                        <span class="spec-label">${key}:</span>
                        <span class="spec-value">${value}</span>
                    </div>
                `).join('');

            // Update status indicator and card styling
            status.className = `status-indicator ${cardStatus}`;
            card.className = `hardware-card ${cardStatus}`;
        }

        // Show recommendations based on detected hardware
        function showRecommendations() {
            const recommendationsSection = document.getElementById('recommendations-section');
            const recommendationsList = document.getElementById('recommendations-list');
            
            if (!window.CrucibleHardwareDetector) return;

            const recommendations = window.CrucibleHardwareDetector.getCompatibilityRecommendations();
            
            if (recommendations.length === 0) {
                recommendationsList.innerHTML = `
                    <div class="recommendation-item">
                        <div class="recommendation-icon info"></div>
                        <div class="recommendation-text">
                            <strong>System Ready:</strong> All hardware components are compatible with Crucible testing suite.
                        </div>
                    </div>
                `;
            } else {
                recommendationsList.innerHTML = recommendations.map(rec => `
                    <div class="recommendation-item">
                        <div class="recommendation-icon ${rec.type}"></div>
                        <div class="recommendation-text">
                            <strong>${rec.message}</strong><br>
                            <small>${rec.action}</small>
                        </div>
                    </div>
                `).join('');
            }
            
            recommendationsSection.style.display = 'block';
        }

        // Handle detection errors
        function handleDetectionError(error) {
            const progressText = document.getElementById('progress-text');
            const retryBtn = document.getElementById('retry-btn');
            
            progressText.textContent = `Detection failed: ${error.message}`;
            progressText.style.color = 'var(--status-fail)';
            retryBtn.style.display = 'inline-block';
            
            // Mark all cards as error
            hardwareComponents.forEach(component => {
                const card = document.getElementById(`card-${component.id}`);
                const status = document.getElementById(`status-${component.id}`);
                const content = document.getElementById(`content-${component.id}`);
                
                if (card && status && content) {
                    card.className = 'hardware-card error';
                    status.className = 'status-indicator error';
                    content.innerHTML = '<div style="color: var(--status-fail);">Detection failed</div>';
                }
            });
        }

        // Enable proceed button
        function enableProceedButton() {
            const proceedBtn = document.getElementById('proceed-btn');
            proceedBtn.disabled = false;
        }

        // Retry detection
        function retryDetection() {
            location.reload();
        }

        // Proceed to main testing interface
        function proceedToTesting() {
            if (detectionComplete) {
                // Store hardware data for main dashboard
                if (typeof(Storage) !== "undefined") {
                    localStorage.setItem('crucibleHardwareData', JSON.stringify(hardwareData));
                }
                
                // Navigate to main dashboard
                window.location.href = '/';
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F5') {
                e.preventDefault();
                retryDetection();
            }
            if (e.key === 'Enter' && detectionComplete) {
                proceedToTesting();
            }
            if (e.key === 'Escape') {
                // Skip detection and go directly to dashboard
                window.location.href = '/';
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initializeDetection);
    </script>
</body>
</html>
