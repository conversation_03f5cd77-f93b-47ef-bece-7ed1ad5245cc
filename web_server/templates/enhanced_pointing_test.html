<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Pointing Device Test - Crucible</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            background: var(--bg-primary);
            color: var(--text-primary);
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            cursor: crosshair;
        }

        .pointing-test-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-md);
            background: var(--bg-card);
            border-bottom: 1px solid var(--border-color);
            z-index: 100;
        }

        .test-title {
            font-size: var(--font-size-xl);
            font-weight: 600;
            color: var(--accent-primary);
        }

        .test-stats {
            display: flex;
            gap: var(--space-lg);
            font-size: var(--font-size-sm);
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--accent-primary);
        }

        .test-area {
            flex: 1;
            position: relative;
            background: var(--bg-primary);
            overflow: hidden;
        }

        .target {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            transition: all var(--transition-fast);
            user-select: none;
        }

        .target.large {
            width: 80px;
            height: 80px;
            background: var(--status-warning);
            color: white;
            font-size: var(--font-size-lg);
        }

        .target.medium {
            width: 50px;
            height: 50px;
            background: var(--accent-secondary);
            color: white;
            font-size: var(--font-size-base);
        }

        .target.small {
            width: 30px;
            height: 30px;
            background: var(--status-fail);
            color: white;
            font-size: var(--font-size-sm);
        }

        .target:hover {
            transform: scale(1.1);
            box-shadow: 0 0 20px currentColor;
        }

        .target.hit {
            background: var(--status-pass) !important;
            transform: scale(0.8);
            opacity: 0.7;
        }

        .crosshair {
            position: absolute;
            width: 20px;
            height: 20px;
            pointer-events: none;
            z-index: 50;
        }

        .crosshair::before,
        .crosshair::after {
            content: '';
            position: absolute;
            background: var(--accent-primary);
        }

        .crosshair::before {
            left: 9px;
            top: 0;
            width: 2px;
            height: 20px;
        }

        .crosshair::after {
            left: 0;
            top: 9px;
            width: 20px;
            height: 2px;
        }

        .trail {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--accent-primary);
            border-radius: 50%;
            pointer-events: none;
            opacity: 0.6;
            z-index: 10;
        }

        .instructions {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            background: var(--bg-card);
            padding: var(--space-xl);
            border-radius: var(--btn-border-radius);
            border: 1px solid var(--border-color);
            z-index: 200;
        }

        .instruction-title {
            font-size: var(--font-size-xl);
            color: var(--accent-primary);
            margin-bottom: var(--space-md);
        }

        .instruction-text {
            font-size: var(--font-size-base);
            color: var(--text-secondary);
            margin-bottom: var(--space-lg);
        }

        .phase-indicator {
            position: absolute;
            top: var(--space-md);
            left: 50%;
            transform: translateX(-50%);
            background: var(--bg-card);
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--btn-border-radius);
            border: 1px solid var(--border-color);
            font-size: var(--font-size-sm);
            z-index: 100;
        }

        .control-btn {
            background: var(--accent-primary);
            border: none;
            color: white;
            padding: var(--btn-padding-md);
            border-radius: var(--btn-border-radius);
            cursor: pointer;
            font-size: var(--btn-font-size);
            transition: all var(--transition-fast);
            margin: var(--space-sm);
        }

        .control-btn:hover {
            background: #45a049;
        }

        .control-btn.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }

        .accuracy-indicator {
            position: absolute;
            bottom: var(--space-md);
            right: var(--space-md);
            background: var(--bg-card);
            padding: var(--space-md);
            border-radius: var(--btn-border-radius);
            border: 1px solid var(--border-color);
            font-size: var(--font-size-sm);
            z-index: 100;
        }

        .accuracy-bar {
            width: 100px;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: 4px;
            overflow: hidden;
            margin-top: var(--space-xs);
        }

        .accuracy-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--status-fail), var(--status-warning), var(--status-pass));
            width: 0%;
            transition: width var(--transition-normal);
        }

        .calibration-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 5;
            display: none;
        }

        .grid-line {
            position: absolute;
            background: var(--border-color);
            opacity: 0.3;
        }

        .grid-line.vertical {
            width: 1px;
            height: 100%;
        }

        .grid-line.horizontal {
            width: 100%;
            height: 1px;
        }

        @keyframes fadeOut {
            to { opacity: 0; transform: scale(0.5); }
        }

        .target.removing {
            animation: fadeOut 0.3s ease forwards;
        }

        @media (max-width: 768px) {
            .test-header {
                flex-direction: column;
                gap: var(--space-sm);
            }
            
            .target.large { width: 60px; height: 60px; }
            .target.medium { width: 40px; height: 40px; }
            .target.small { width: 25px; height: 25px; }
        }
    </style>
</head>
<body>
    <div class="pointing-test-container">
        <div class="test-header">
            <div class="test-title">🖱️ Enhanced Pointing Device Test</div>
            <div class="test-stats">
                <div class="stat-item">
                    <div class="stat-value" id="targets-hit">0</div>
                    <div>Targets Hit</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="accuracy">0%</div>
                    <div>Accuracy</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="avg-time">0ms</div>
                    <div>Avg Time</div>
                </div>
            </div>
        </div>

        <div class="phase-indicator" id="phase-indicator">
            Phase 1: Large Targets - Click to begin
        </div>

        <div class="test-area" id="test-area">
            <div class="crosshair" id="crosshair"></div>
            <div class="calibration-grid" id="calibration-grid"></div>
            
            <div class="instructions" id="instructions">
                <div class="instruction-title">Pointing Device Calibration Test</div>
                <div class="instruction-text">
                    This test will evaluate your pointing device accuracy and responsiveness.<br>
                    Click targets as they appear, starting with large targets and progressing to smaller ones.
                </div>
                <button class="control-btn" onclick="startTest()">Start Test</button>
                <button class="control-btn secondary" onclick="showCalibration()">Show Grid</button>
            </div>
        </div>

        <div class="accuracy-indicator">
            <div>Precision: <span id="precision">0%</span></div>
            <div class="accuracy-bar">
                <div class="accuracy-fill" id="accuracy-fill"></div>
            </div>
        </div>
    </div>

    <script>
        let currentSessionId = null;
        let testPhase = 0;
        let targetsHit = 0;
        let totalTargets = 0;
        let targetTimes = [];
        let currentTarget = null;
        let targetStartTime = 0;
        let testStarted = false;
        let showGrid = false;
        let mouseTrail = [];
        let trailElements = [];

        const PHASES = [
            { name: 'Large Targets', size: 'large', count: 8, description: 'Click the large targets' },
            { name: 'Medium Targets', size: 'medium', count: 12, description: 'Click the medium targets' },
            { name: 'Small Targets', size: 'small', count: 16, description: 'Click the small targets' },
            { name: 'Mixed Precision', size: 'mixed', count: 20, description: 'Click targets of varying sizes' }
        ];

        async function initializeTest() {
            const params = new URLSearchParams(window.location.search);
            const assetNumber = params.get('asset_number') || 'POINTING_TEST';
            
            await startTestSession(assetNumber);
            setupEventListeners();
            createCalibrationGrid();
        }

        async function startTestSession(assetNumber) {
            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        test_type: 'pointing', 
                        asset_number: assetNumber,
                        test_params: { enhanced: true }
                    })
                });
                
                const result = await response.json();
                if (response.ok) {
                    currentSessionId = result.session_id;
                }
            } catch (error) {
                console.error('Error starting test session:', error);
            }
        }

        function setupEventListeners() {
            const testArea = document.getElementById('test-area');
            
            testArea.addEventListener('mousemove', handleMouseMove);
            testArea.addEventListener('click', handleClick);
            
            document.addEventListener('keydown', (e) => {
                switch(e.key) {
                    case 'Escape':
                        completeTest();
                        break;
                    case 'r':
                    case 'R':
                        if (e.ctrlKey) resetTest();
                        break;
                    case 'g':
                    case 'G':
                        showCalibration();
                        break;
                }
            });
        }

        function handleMouseMove(event) {
            if (!testStarted) return;
            
            const crosshair = document.getElementById('crosshair');
            crosshair.style.left = (event.clientX - 10) + 'px';
            crosshair.style.top = (event.clientY - 10) + 'px';
            
            addMouseTrail(event.clientX, event.clientY);
            sendPointerEvent('move', event.clientX, event.clientY);
        }

        function handleClick(event) {
            if (!testStarted) return;
            
            const clickTime = Date.now();
            let hitTarget = false;
            
            if (currentTarget && isClickOnTarget(event, currentTarget)) {
                hitTarget = true;
                targetsHit++;
                
                const responseTime = clickTime - targetStartTime;
                targetTimes.push(responseTime);
                
                currentTarget.classList.add('hit');
                
                setTimeout(() => {
                    if (currentTarget) {
                        currentTarget.remove();
                        currentTarget = null;
                    }
                    setTimeout(() => generateNextTarget(), 500);
                }, 300);
                
                updateStats();
            }
            
            sendPointerEvent('click', event.clientX, event.clientY, hitTarget);
        }

        function isClickOnTarget(event, target) {
            const rect = target.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            const radius = rect.width / 2;
            
            const distance = Math.sqrt(
                Math.pow(event.clientX - centerX, 2) + 
                Math.pow(event.clientY - centerY, 2)
            );
            
            return distance <= radius;
        }

        function addMouseTrail(x, y) {
            mouseTrail.push({ x, y, time: Date.now() });
            
            if (mouseTrail.length > 10) {
                mouseTrail.shift();
            }
            
            trailElements.forEach(el => el.remove());
            trailElements = [];
            
            mouseTrail.forEach((point, index) => {
                const trail = document.createElement('div');
                trail.className = 'trail';
                trail.style.left = (point.x - 2) + 'px';
                trail.style.top = (point.y - 2) + 'px';
                trail.style.opacity = (index / mouseTrail.length) * 0.6;
                
                document.getElementById('test-area').appendChild(trail);
                trailElements.push(trail);
            });
        }

        function startTest() {
            document.getElementById('instructions').style.display = 'none';
            testStarted = true;
            testPhase = 0;
            targetsHit = 0;
            totalTargets = 0;
            targetTimes = [];
            
            updatePhaseIndicator();
            generateNextTarget();
        }

        function generateNextTarget() {
            if (testPhase >= PHASES.length) {
                completeTest();
                return;
            }
            
            const phase = PHASES[testPhase];
            
            if (totalTargets >= phase.count) {
                testPhase++;
                if (testPhase < PHASES.length) {
                    updatePhaseIndicator();
                    setTimeout(() => generateNextTarget(), 1000);
                } else {
                    completeTest();
                }
                return;
            }
            
            let targetSize = phase.size;
            if (targetSize === 'mixed') {
                const sizes = ['large', 'medium', 'small'];
                targetSize = sizes[Math.floor(Math.random() * sizes.length)];
            }
            
            const target = document.createElement('div');
            target.className = `target ${targetSize}`;
            target.textContent = totalTargets + 1;
            
            const testArea = document.getElementById('test-area');
            const areaRect = testArea.getBoundingClientRect();
            const margin = 100;
            
            const maxX = areaRect.width - margin * 2;
            const maxY = areaRect.height - margin * 2;
            
            target.style.left = (Math.random() * maxX + margin) + 'px';
            target.style.top = (Math.random() * maxY + margin) + 'px';
            
            testArea.appendChild(target);
            currentTarget = target;
            targetStartTime = Date.now();
            totalTargets++;
            
            setTimeout(() => {
                if (currentTarget === target) {
                    target.classList.add('removing');
                    setTimeout(() => {
                        if (target.parentNode) {
                            target.remove();
                        }
                        if (currentTarget === target) {
                            currentTarget = null;
                            generateNextTarget();
                        }
                    }, 300);
                }
            }, 5000);
        }

        function updatePhaseIndicator() {
            const indicator = document.getElementById('phase-indicator');
            if (testPhase < PHASES.length) {
                const phase = PHASES[testPhase];
                indicator.textContent = `Phase ${testPhase + 1}: ${phase.name} - ${phase.description}`;
            } else {
                indicator.textContent = 'Test Complete!';
            }
        }

        function updateStats() {
            document.getElementById('targets-hit').textContent = targetsHit;
            
            const accuracy = totalTargets > 0 ? Math.round((targetsHit / totalTargets) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';
            
            const avgTime = targetTimes.length > 0 ? 
                Math.round(targetTimes.reduce((a, b) => a + b, 0) / targetTimes.length) : 0;
            document.getElementById('avg-time').textContent = avgTime + 'ms';
            
            document.getElementById('precision').textContent = accuracy + '%';
            document.getElementById('accuracy-fill').style.width = accuracy + '%';
        }

        function createCalibrationGrid() {
            const grid = document.getElementById('calibration-grid');
            
            for (let i = 1; i < 10; i++) {
                const line = document.createElement('div');
                line.className = 'grid-line vertical';
                line.style.left = (i * 10) + '%';
                grid.appendChild(line);
            }
            
            for (let i = 1; i < 10; i++) {
                const line = document.createElement('div');
                line.className = 'grid-line horizontal';
                line.style.top = (i * 10) + '%';
                grid.appendChild(line);
            }
        }

        function showCalibration() {
            showGrid = !showGrid;
            const grid = document.getElementById('calibration-grid');
            grid.style.display = showGrid ? 'block' : 'none';
        }

        async function sendPointerEvent(action, x, y, hit = false) {
            if (!currentSessionId) return;
            
            try {
                await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'pointer_event',
                        event_type: action,
                        x: x,
                        y: y,
                        hit: hit,
                        phase: testPhase,
                        timestamp: new Date().toISOString()
                    })
                });
            } catch (error) {
                console.error('Error sending pointer event:', error);
            }
        }

        function resetTest() {
            testStarted = false;
            testPhase = 0;
            targetsHit = 0;
            totalTargets = 0;
            targetTimes = [];
            
            if (currentTarget) {
                currentTarget.remove();
                currentTarget = null;
            }
            
            trailElements.forEach(el => el.remove());
            trailElements = [];
            mouseTrail = [];
            
            document.getElementById('instructions').style.display = 'block';
            updateStats();
        }

        async function completeTest() {
            testStarted = false;
            
            if (currentSessionId) {
                try {
                    const accuracy = totalTargets > 0 ? (targetsHit / totalTargets) * 100 : 0;
                    const avgTime = targetTimes.length > 0 ? 
                        targetTimes.reduce((a, b) => a + b, 0) / targetTimes.length : 0;
                    
                    await fetch(`/api/visual_test/input/${currentSessionId}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            action: 'complete_test',
                            targets_hit: targetsHit,
                            total_targets: totalTargets,
                            accuracy: accuracy,
                            average_time: avgTime,
                            result: 'completed'
                        })
                    });
                } catch (error) {
                    console.error('Error completing test:', error);
                }
            }
            
            const instructions = document.getElementById('instructions');
            const accuracy = totalTargets > 0 ? Math.round((targetsHit / totalTargets) * 100) : 0;
            const avgTime = targetTimes.length > 0 ? Math.round(targetTimes.reduce((a, b) => a + b, 0) / targetTimes.length) : 0;
            
            instructions.innerHTML = `
                <div class="instruction-title">✅ Pointing Device Test Complete</div>
                <div class="instruction-text">
                    Accuracy: ${accuracy}%<br>
                    Targets Hit: ${targetsHit}/${totalTargets}<br>
                    Average Response Time: ${avgTime}ms
                </div>
                <button class="control-btn" onclick="window.close()">Close Test</button>
            `;
            instructions.style.display = 'block';
        }

        window.addEventListener('load', initializeTest);
    </script>
</body>
</html>
