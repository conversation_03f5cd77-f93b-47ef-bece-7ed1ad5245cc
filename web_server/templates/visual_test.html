<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visual Test - Crucible</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: #1E1E1E;
            color: #FFFFFF;
            font-family: Arial, sans-serif;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .test-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            justify-content: center;
            align-items: center;
        }

        .test-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .test-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .system-info, .test-info {
            font-size: 12px;
            color: #FFFFFF;
            margin-bottom: 10px;
        }

        .progress-section {
            width: 100%;
            max-width: 800px;
            margin-bottom: 30px;
        }

        .progress-label {
            font-size: 12px;
            color: #FFFFFF;
            margin-bottom: 5px;
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #333;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background-color: #5565f7;
            width: 0%;
            transition: width 0.3s ease;
        }

        .operation-text {
            font-size: 10px;
            color: #AAAAAA;
            margin-bottom: 5px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
            max-width: 600px;
            margin-bottom: 30px;
        }

        .stat-item {
            font-size: 12px;
            color: #FFFFFF;
            text-align: left;
        }

        .memory-visualization {
            width: 800px;
            height: 200px;
            background-color: #2D2D2D;
            border-radius: 4px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 14px;
        }

        /* LCD Test Specific Styles */
        .lcd-test-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            /* remove transition to avoid perceived delay when changing solid colors */
            transition: none;
        }

        /* Make the info unobtrusive and movable */
        .lcd-color-info {
            position: absolute;
            top: 16px;
            left: 16px;
            transform: none;
            text-align: left;
            font-size: 16px;
            font-weight: 600;
            padding: 8px 12px;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.35);
            color: #fff;
            z-index: 10;
            pointer-events: auto;
            user-select: none;
        }
        .lcd-color-info small {
            display: block;
            font-weight: 400;
            opacity: 0.9;
        }
        .lcd-hud {
            position: absolute;
            right: 16px;
            top: 16px;
            display: flex;
            gap: 8px;
            z-index: 11;
        }
        .lcd-chip {
            background: rgba(0,0,0,0.35);
            color: #fff;
            border-radius: 999px;
            padding: 6px 10px;
            font-size: 12px;
            font-weight: 600;
        }
        /* Optional guide to help spot stuck/dead pixels without blocking view */
        .lcd-border-guide {
            position: absolute;
            inset: 0;
            pointer-events: none;
            z-index: 5;
            border: 6px solid rgba(255,255,255,0.15);
            outline: 6px solid rgba(0,0,0,0.2);
            box-shadow: inset 0 0 0 1px rgba(0,0,0,0.35);
        }

        .lcd-controls {
            position: absolute;
            bottom: 16px;
            right: 16px;
            display: flex;
            gap: 12px;
            z-index: 10;
        }

        .lcd-btn {
            padding: 15px 30px;
            font-size: 18px;
            font-weight: bold;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lcd-btn-ok {
            background-color: #28a745;
            color: white;
        }

        .lcd-btn-ok:hover {
            background-color: #218838;
        }

        .lcd-btn-fail {
            background-color: #dc3545;
            color: white;
        }

        .lcd-btn-fail:hover {
            background-color: #c82333;
        }

        .lcd-progress {
            position: absolute;
            bottom: 16px;
            left: 16px;
            color: #fff;
            font-size: 12px;
            background-color: rgba(0, 0, 0, 0.35);
            padding: 6px 10px;
            border-radius: 6px;
            z-index: 10;
            font-weight: 600;
        }

        /* Hide non-LCD elements during LCD test */
        .lcd-test-active .test-container > *:not(.lcd-test-container) {
            display: none;
        }

        .controls {
            display: flex;
            gap: 15px;
        }

        .btn {
            background-color: #3256b0;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #7c94f7;
        }

        .btn:disabled {
            background-color: #555;
            cursor: not-allowed;
        }

        .btn-danger {
            background-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-running {
            background-color: #28a745;
        }

        .status-complete {
            background-color: #007bff;
        }

        .status-error {
            background-color: #dc3545;
        }

        .status-stopping {
            background-color: #ffc107;
            color: #000;
        }

        .fullscreen-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #1E1E1E;
            z-index: 9999;
            display: none;
        }

        .error-message {
            color: #dc3545;
            text-align: center;
            margin: 20px 0;
            font-size: 14px;
        }

        .close-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            background: none;
            border: none;
            color: #FFFFFF;
            font-size: 18px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .close-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Responsive adjustments */
        @media (max-width: 900px) {
            .memory-visualization {
                width: 100%;
                max-width: 600px;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="fullscreen-overlay" id="testOverlay">
        <button class="close-btn" onclick="closeTest()" title="Close Test (ESC)">✕ Close</button>
        
        <div class="status-indicator" id="statusIndicator">
            <span id="statusText">Initializing</span>
        </div>

        <div class="test-container">
            <div class="test-header">
                <div class="test-title" id="testTitle">RAM Test</div>
                <div class="system-info" id="systemInfo">Loading system information...</div>
                <div class="test-info" id="testInfo">Test Size: 1024 MB | Duration: 30 seconds</div>
            </div>

            <div class="progress-section">
                <label class="progress-label">Overall Progress:</label>
                <div class="progress-bar">
                    <div class="progress-fill" id="overallProgress"></div>
                </div>

                <label class="progress-label">Current Operation:</label>
                <div class="operation-text" id="operationText">Initializing...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="operationProgress"></div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-item">Cycles: <span id="cyclesCount">0</span></div>
                <div class="stat-item">Patterns: <span id="patternsCount">0</span></div>
                <div class="stat-item">Errors: <span id="errorsCount">0</span></div>
                <div class="stat-item">Speed: <span id="speedValue">0 MB/s</span></div>
                <div class="stat-item">Time Left: <span id="timeLeftValue">0s</span></div>
                <div class="stat-item">Status: <span id="statusValue">Initializing</span></div>
            </div>

            <div class="memory-visualization" id="memoryViz">
                <canvas id="memoryCanvas" width="760" height="180"></canvas>
            </div>

            <div class="controls">
                <button class="btn" id="startBtn" onclick="startTest()" disabled>Start Test</button>
                <button class="btn btn-danger" id="stopBtn" onclick="stopTest()">Stop Test</button>
                <button class="btn" onclick="closeTest()">Close</button>
            </div>

            <div class="error-message" id="errorMessage" style="display: none;"></div>
        </div>

        <!-- LCD Test Interface -->
        <div class="lcd-test-container" id="lcdTestContainer" style="display: none;">
            <!-- thin border guide that doesn't occlude center -->
            <div class="lcd-border-guide" id="lcdBorderGuide"></div>

            <!-- Minimal HUD chips -->
            <div class="lcd-hud" id="lcdHud">
                <div class="lcd-chip" id="lcdProgress">Color 1 of 5</div>
                <div class="lcd-chip" id="lcdColorChip">Black</div>
                <div class="lcd-chip" id="lcdHintChip">Enter=OK • Esc=Fail • H=Hide HUD</div>
            </div>

            <!-- Compact info badge, can be toggled -->
            <div class="lcd-color-info" id="lcdColorInfo" title="Press H to toggle HUD">
                <div id="lcdColorName">Black</div>
                <small>Enter=OK • Esc=Fail • H=Hide HUD</small>
            </div>

            <div class="lcd-controls">
                <button class="lcd-btn lcd-btn-ok" onclick="submitLcdInput('ok')" id="lcdOkBtn" title="Enter">
                    ✓ OK
                </button>
                <button class="lcd-btn lcd-btn-fail" onclick="submitLcdInput('fail')" id="lcdFailBtn" title="Esc">
                    ✗ FAIL
                </button>
            </div>
        </div>
    </div>

    <script type="module">
      import { initKiosk } from '/static/js/modules/kiosk_fullscreen.js';
      const kiosk = initKiosk({ allowEscToClose: true, suppressQuitCombos: true, reenterOnVisibility: true });
      window.addEventListener('load', () => kiosk.ensureFullscreen && kiosk.ensureFullscreen());
    </script>

    <!-- Preload JS module to reduce first-paint delay -->
    <link rel="modulepreload" href="/static/js/modules/kiosk_fullscreen.js" />

    <script>
        let currentSessionId = null;
        let eventSource = null;
        let testParams = {};
        let memoryCanvas, memoryCtx;
        const MEMORY_BLOCKS = 100;

        // Get URL parameters
        function getUrlParams() {
            const params = new URLSearchParams(window.location.search);
            return {
                test_type: params.get('test_type') || 'ram',
                asset_number: params.get('asset_number') || 'TEST001',
                test_size_mb: parseInt(params.get('test_size_mb')) || 1024,
                duration_seconds: parseInt(params.get('duration_seconds')) || 30
            };
        }

        // Initialize test
        function initTest() {
            testParams = getUrlParams();

            // Update UI based on test type
            document.getElementById('testTitle').textContent =
                testParams.test_type.toUpperCase() + ' Test';

            if (testParams.test_type === 'lcd') {
                // Hide RAM/CPU specific info for LCD test
                document.getElementById('testInfo').style.display = 'none';
                // Show LCD test interface
                setupLcdTest();
            } else if (testParams.test_type === 'keyboard') {
                // Hide RAM/CPU specific info for keyboard test
                document.getElementById('testInfo').style.display = 'none';
                // Show keyboard test interface
                setupKeyboardTest();
            } else {
                document.getElementById('testInfo').textContent =
                    `Test Size: ${testParams.test_size_mb} MB | Duration: ${testParams.duration_seconds} seconds`;
            }

            initMemoryCanvas();
            // Show overlay
            document.getElementById('testOverlay').style.display = 'block';

            // Auto-start test immediately to avoid perceived lag
            startTest();
        }

        // Setup LCD test interface
        function setupLcdTest() {
            const testContainer = document.querySelector('.test-container');
            const lcdContainer = document.getElementById('lcdTestContainer');

            // Hide regular test interface and show LCD interface
            testContainer.classList.add('lcd-test-active');
            lcdContainer.style.display = 'flex';

            // Set initial background color
            lcdContainer.style.backgroundColor = '#000000';

            // HUD visibility state
            window.__lcdHudVisible = true;
        }

        // Setup keyboard test interface
        function setupKeyboardTest() {
            // Hide memory visualization and stats for keyboard test
            document.getElementById('memoryViz').style.display = 'none';
            document.querySelector('.stats-grid').style.display = 'none';
            
            // Update operation text to inform about native test
            document.getElementById('operationText').textContent = 'Native keyboard test window will open automatically...';
            
            // Hide start button since test starts automatically
            document.getElementById('startBtn').style.display = 'none';
            
            // Add debug logging area for keyboard test
            const debugArea = document.createElement('div');
            debugArea.id = 'keyboardDebugArea';
            debugArea.style.cssText = `
                background-color: #2D2D2D;
                color: #FFFFFF;
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
                max-height: 300px;
                overflow-y: auto;
                white-space: pre-wrap;
                border: 1px solid #555;
            `;
            debugArea.innerHTML = 'Keyboard Test Debug Log:\n';
            
            // Insert debug area after the operation text
            const operationDiv = document.getElementById('operationText').parentElement;
            operationDiv.appendChild(debugArea);
            
            // Function to add debug messages
            window.addKeyboardDebug = function(message) {
                const timestamp = new Date().toLocaleTimeString();
                debugArea.textContent += `[${timestamp}] ${message}\n`;
                debugArea.scrollTop = debugArea.scrollHeight;
            };
            
            addKeyboardDebug('Keyboard test interface initialized');
        }

        // Start test
        async function startTest() {
            try {
                const response = await fetch('/api/visual_test/start', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        test_type: testParams.test_type,
                        asset_number: testParams.asset_number,
                        test_params: {
                            test_size_mb: testParams.test_size_mb,
                            duration_seconds: testParams.duration_seconds
                        }
                    })
                });

                const result = await response.json();
                
                if (response.ok) {
                    currentSessionId = result.session_id;
                    connectToProgressStream();
                    
                    // Update UI
                    document.getElementById('startBtn').disabled = true;
                    document.getElementById('stopBtn').disabled = false;
                } else {
                    showError('Failed to start test: ' + result.error);
                }
            } catch (error) {
                showError('Failed to start test: ' + error.message);
            }
        }

        // Connect to progress stream
        function connectToProgressStream() {
            if (!currentSessionId) return;
            
            eventSource = new EventSource(`/api/visual_test/progress/${currentSessionId}`);
            
            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                updateProgress(data);
            };
            
            eventSource.onerror = function(event) {
                console.error('EventSource failed:', event);
                showError('Connection to test progress lost');
            };
        }

        // Update progress display
        function updateProgress(data) {
            if (data.error) {
                showError(data.error);
                return;
            }

            const progress = data.progress;
            const status = data.status;

            // Update status
            updateStatus(status);

            if (testParams.test_type === 'lcd') {
                // Update LCD test interface
                updateLcdProgress(progress);
            } else if (testParams.test_type === 'keyboard') {
                // Update keyboard test interface and debug log
                document.getElementById('operationText').textContent = progress.operation_text;
                
                // Update debug log if available
                if (progress.debug_log && progress.debug_log.length > 0 && window.addKeyboardDebug) {
                    progress.debug_log.forEach(logMessage => {
                        window.addKeyboardDebug(logMessage);
                    });
                }
            } else {
                // Update progress bars for RAM/CPU tests
                document.getElementById('overallProgress').style.width = progress.overall_progress + '%';
                document.getElementById('operationProgress').style.width = progress.operation_progress + '%';

                // Update text fields
                document.getElementById('operationText').textContent = progress.operation_text;
                document.getElementById('cyclesCount').textContent = progress.cycles;
                document.getElementById('patternsCount').textContent = progress.patterns;
                document.getElementById('errorsCount').textContent = progress.errors;
                document.getElementById('speedValue').textContent = progress.speed.toFixed(1) + ' MB/s';
                document.getElementById('timeLeftValue').textContent = progress.time_left.toFixed(1) + 's';
                document.getElementById('statusValue').textContent = status;

                // Update system info if available
                if (progress.system_info && progress.system_info.total_mb) {
                    document.getElementById('systemInfo').textContent =
                        `System Memory: ${progress.system_info.total_mb} MB total, ${progress.system_info.available_mb} MB available`;
                }
            }

            // Update visualizations
            if (testParams.test_type === 'ram') {
                updateMemoryVisualization(progress);
            } else if (testParams.test_type === 'cpu') {
                updateCpuVisualization(progress);
            }

            // Handle test completion
            if (data.test_complete) {
                handleTestComplete(data.test_result);
            }
        }

        // Memory Visualization Functions
        function initMemoryCanvas() {
            const vizContainer = document.getElementById('memoryViz');
            memoryCanvas = document.getElementById('memoryCanvas');
            if (!memoryCanvas) {
                memoryCanvas = document.createElement('canvas');
                memoryCanvas.id = 'memoryCanvas';
                memoryCanvas.width = vizContainer.clientWidth - 20;
                memoryCanvas.height = vizContainer.clientHeight - 20;
                vizContainer.innerHTML = '';
                vizContainer.appendChild(memoryCanvas);
            }
            memoryCtx = memoryCanvas.getContext('2d');
            drawMemoryBlocks(0, '#2d2d2d');
        }

        function drawMemoryBlocks(fillCount, fillColor) {
            const width = memoryCanvas.width;
            const height = memoryCanvas.height;
            const blocksPerRow = 20;
            const blockWidth = width / blocksPerRow;
            const blockHeight = height / Math.ceil(MEMORY_BLOCKS / blocksPerRow);
            memoryCtx.clearRect(0, 0, width, height);
            for (let i = 0; i < MEMORY_BLOCKS; i++) {
                const row = Math.floor(i / blocksPerRow);
                const col = i % blocksPerRow;
                const x = col * blockWidth;
                const y = row * blockHeight;
                memoryCtx.fillStyle = i < fillCount ? fillColor : '#444';
                memoryCtx.fillRect(x + 1, y + 1, blockWidth - 2, blockHeight - 2);
            }
        }

        function updateMemoryVisualization(progress) {
            if (!memoryCtx) return;
            const fillBlocks = Math.floor((progress.operation_progress || 0) / 100 * MEMORY_BLOCKS);
            const match = (progress.operation_text || '').match(/0x[0-9A-Fa-f]{2}/);
            let pattern = match ? match[0].toUpperCase() : '';
            const patternColors = { '0XAA': '#f1c40f', '0X55': '#3498db', '0XFF': '#e74c3c', '0X00': '#2ecc71' };
            const fillColor = patternColors[pattern] || '#5565f7';
            drawMemoryBlocks(fillBlocks, fillColor);
        }

        // --- CPU Visualization ---
        let cpuData = []; // Array of {t: elapsedSeconds, u: usage}
        const CPU_MAX_POINTS = 400;

        function updateCpuVisualization(progress) {
            if (!memoryCtx) return;
                        const usage = progress.cpu_usage !== undefined ? progress.cpu_usage : 0;
            const elapsed = (testParams.duration_seconds || 1) - (progress.time_left || 0);
            cpuData.push({t: elapsed, u: usage});
            if (cpuData.length > CPU_MAX_POINTS) cpuData.shift();
            drawCpuGraph();
        }

        function drawCpuGraph() {
            const width = memoryCanvas.width;
            const height = memoryCanvas.height;
            memoryCtx.clearRect(0, 0, width, height);
            // Grid & labels
            memoryCtx.strokeStyle = '#333';
            memoryCtx.lineWidth = 1;
            memoryCtx.fillStyle = '#777';
            memoryCtx.font = '10px monospace';
            const gridRows = 4; // 0,25,50,75,100
            for (let i = 0; i <= gridRows; i++) {
                const y = (i / gridRows) * height;
                // Horizontal grid line
                memoryCtx.beginPath();
                memoryCtx.moveTo(0, y);
                memoryCtx.lineTo(width, y);
                memoryCtx.stroke();
                // Label
                const label = `${(100 - i * 25)}%`;
                memoryCtx.fillText(label, 2, y - 2);
            }
            // Plot line
            if (cpuData.length < 2) return;
            memoryCtx.strokeStyle = '#56d5f7';
            memoryCtx.lineWidth = 2;
            memoryCtx.beginPath();
            const totalDuration = testParams.duration_seconds || 1;
            cpuData.forEach((point, idx) => {
                const x = (point.t / totalDuration) * width;
                const y = height - (point.u / 100) * height;
                if (idx === 0) memoryCtx.moveTo(x, y);
                else memoryCtx.lineTo(x, y);
            });
            memoryCtx.stroke();
            // Current value label
            const latest = cpuData[cpuData.length - 1].u;
            memoryCtx.fillStyle = '#56d5f7';
            memoryCtx.fillText(`${latest.toFixed(0)}%`, width - 40, 14);
        }

        // Update LCD test progress
        function updateLcdProgress(progress) {
            const lcdContainer = document.getElementById('lcdTestContainer');
            const lcdProgress = document.getElementById('lcdProgress');
            const lcdColorInfo = document.getElementById('lcdColorInfo');
            const lcdColorName = document.getElementById('lcdColorName');
            const lcdOkBtn = document.getElementById('lcdOkBtn');
            const lcdFailBtn = document.getElementById('lcdFailBtn');
            const lcdColorChip = document.getElementById('lcdColorChip');

            // Update background color immediately, no transition
            if (progress.current_color_hex) {
                lcdContainer.style.backgroundColor = progress.current_color_hex;
            }

            // Update progress text
            if (progress.total_colors > 0) {
                const txt = `Color ${progress.color_index + 1} of ${progress.total_colors}`;
                lcdProgress.textContent = txt;
            }

            // Update color name
            if (progress.current_color) {
                lcdColorName.textContent = progress.current_color;
                if (lcdColorChip) lcdColorChip.textContent = progress.current_color;
            }

            // Enable/disable buttons based on whether we're awaiting input
            const awaitingInput = !!progress.awaiting_user_input;
            lcdOkBtn.disabled = !awaitingInput;
            lcdFailBtn.disabled = !awaitingInput;

            // Compact badge contrast handling
            const darkBg = progress.current_color_hex === '#000000';
            const infoBG = darkBg ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.35)';
            const infoFG = darkBg ? '#fff' : '#fff';
            lcdColorInfo.style.backgroundColor = infoBG;
            lcdColorInfo.style.color = infoFG;
        }

        // Update status indicator
        function updateStatus(status) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');
            
            // Remove all status classes
            indicator.className = 'status-indicator';
            
            // Add appropriate status class
            switch (status) {
                case 'running':
                    indicator.classList.add('status-running');
                    statusText.textContent = 'Running';
                    break;
                case 'complete':
                    indicator.classList.add('status-complete');
                    statusText.textContent = 'Complete';
                    break;
                case 'error':
                    indicator.classList.add('status-error');
                    statusText.textContent = 'Error';
                    break;
                case 'stopping':
                    indicator.classList.add('status-stopping');
                    statusText.textContent = 'Stopping';
                    break;
                default:
                    statusText.textContent = 'Initializing';
            }
        }

        // Handle test completion
        function handleTestComplete(result) {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            if (result) {
                const message = result.status === 'PASS' ? 
                    'Test completed successfully!' : 
                    result.status === 'launched' ?
                    'Native test launched successfully!' :
                    `Test failed: ${result.notes}`;
                
                // Auto-close after 3 seconds for successful tests or launched native tests
                if (result.status === 'PASS' || result.status === 'launched') {
                    setTimeout(() => {
                        closeTest();
                    }, 3000);
                }
            }
        }

        // Stop test
        async function stopTest() {
            if (!currentSessionId) return;
            
            try {
                await fetch(`/api/visual_test/stop/${currentSessionId}`, {
                    method: 'POST'
                });
                
                document.getElementById('stopBtn').disabled = true;
            } catch (error) {
                console.error('Failed to stop test:', error);
            }
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        // Close test
        function closeTest() {
            if (eventSource) {
                eventSource.close();
                eventSource = null;
            }
            
            if (currentSessionId) {
                // Try to stop the test
                stopTest();
            }
            
            // Close window or redirect
            if (window.opener) {
                window.close();
            } else {
                window.location.href = '/';
            }
        }

        // Prevent default browser quit/navigation combos inside visual_test page (defense in depth)
        window.addEventListener('keydown', function(e) {
            const key = (e.key || '').toLowerCase();
            const ctrl = e.ctrlKey || e.metaKey;
            const alt = e.altKey;
            if (key === 'f11') { e.preventDefault(); e.stopPropagation(); }
            if (ctrl && (key === 'w' || key === 'q' || key === 'f4')) { e.preventDefault(); e.stopPropagation(); }
            if (alt && key === 'f4') { e.preventDefault(); e.stopPropagation(); }
        }, true);

        // Submit LCD test input
        async function submitLcdInput(action) {
            if (!currentSessionId) return;

            try {
                const response = await fetch(`/api/visual_test/input/${currentSessionId}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: action })
                });

                const result = await response.json();

                if (!response.ok) {
                    showError('Failed to submit input: ' + result.error);
                }
            } catch (error) {
                showError('Failed to submit input: ' + error.message);
            }
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                if (testParams.test_type === 'lcd') {
                    submitLcdInput('fail');
                } else {
                    closeTest();
                }
            } else if (event.key === 'Enter') {
                if (testParams.test_type === 'lcd') {
                    submitLcdInput('ok');
                }
            } else if (event.key === 'h' || event.key === 'H') {
                if (testParams.test_type === 'lcd') {
                    // toggle HUD visibility to completely clear view if desired
                    window.__lcdHudVisible = !window.__lcdHudVisible;
                    const hud = document.getElementById('lcdHud');
                    const info = document.getElementById('lcdColorInfo');
                    const controls = document.querySelector('.lcd-controls');
                    const borderGuide = document.getElementById('lcdBorderGuide');
                    const show = window.__lcdHudVisible;
                    if (hud) hud.style.display = show ? 'flex' : 'none';
                    if (info) info.style.display = show ? 'block' : 'none';
                    if (controls) controls.style.display = show ? 'flex' : 'none';
                    if (borderGuide) borderGuide.style.display = show ? 'block' : 'none';
                }
            }
        });

        // Initialize when page loads
        window.addEventListener('load', initTest);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (eventSource) {
                eventSource.close();
            }
        });
    </script>
</body>
</html>
