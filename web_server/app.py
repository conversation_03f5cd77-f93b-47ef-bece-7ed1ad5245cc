from flask import Flask, jsonify, request, abort, send_from_directory, render_template, Response, redirect
from typing import Any, Dict, List # Added Any, Dict, List
from agent.hardware.system_info import get_system_info
from agent.tests.profiles import Profile, get_all_profiles, load_profile, save_profile, delete_profile, get_profile_path
import os
import threading
import time
import json
from agent.core.result_manager import ResultManager
import glob
from agent.core.device_condition_manager import load_device_conditions, save_device_conditions, DEFAULT_DEVICE_CONDITIONS_STRUCTURE
import logging
from agent.core.test_orchestrator import TestOrchestrator
from agent.tests.profiles import load_profile, Profile
RESULTS_DIR = 'results'
app = Flask(__name__, template_folder='templates', static_folder='static')

# In-memory store for live test logs. Key: asset_number, Value: list of log strings
asset_test_logs: dict[str, list[str]] = {}
# TODO: Consider a more robust solution for production (e.g., Redis, DB, or log files per asset)

# Global lock to prevent multiple keyboard test launches
keyboard_test_lock = threading.Lock()
active_keyboard_sessions = set()
# and a mechanism to clean up old logs to prevent memory exhaustion.

# In-memory store for visual test sessions. Key: session_id, Value: test progress data
visual_test_sessions: dict[str, dict] = {}
# Lock for thread-safe access to visual test sessions
visual_test_lock = threading.Lock()

@app.route('/')
def index():
    from version import __version__
    return render_template('index.html', version=__version__)


@app.route('/visual_test')
def visual_test():
    """Serve the visual test interface."""
    return render_template('visual_test.html')

@app.route('/keyboard_test')
def keyboard_test_page():
    """Redirect to visual test interface for keyboard test."""
    # Get asset number from query params or use default
    asset_number = request.args.get('asset_number', 'WEBUI_DEF_ASSET')
    # Redirect to visual test with keyboard test type
    return redirect(f'/visual_test?test_type=keyboard&asset_number={asset_number}')

@app.route('/pointing_device_test')
def pointing_device_test_page():
    """Serve the pointing device test interface."""
    return render_template('pointing_device_test.html')

@app.route('/touch_screen_test')
def touch_screen_test_page():
    """Serve the touch screen test interface."""
    return render_template('touch_screen_test.html')

@app.route('/test_execution')
def test_execution_page():
    """Serve the enhanced test execution dashboard."""
    return render_template('test_execution.html')

@app.route('/hardware_detection')
def hardware_detection_page():
    """Serve the hardware detection screen for PXE boot sequence."""
    return render_template('hardware_detection.html')

@app.route('/asset_entry')
def asset_entry_page():
    """Serve the enhanced asset entry screen with barcode support."""
    return render_template('asset_entry.html')

@app.route('/profile_selection')
def profile_selection_page():
    """Serve the enhanced profile selection screen with hardware recommendations."""
    return render_template('profile_selection.html')

@app.route('/enhanced_lcd_test')
def enhanced_lcd_test_page():
    """Serve the enhanced LCD test interface with hardware-adaptive features."""
    return render_template('enhanced_lcd_test.html')

@app.route('/enhanced_keyboard_test')
def enhanced_keyboard_test_page():
    """Serve the enhanced keyboard test interface with visual feedback."""
    return render_template('enhanced_keyboard_test_standalone.html')
@app.route('/enhanced_pointing_test')
def enhanced_pointing_test_page():
    """Serve the enhanced pointing device test interface with calibration."""
    return render_template('enhanced_pointing_test.html')

@app.route('/simple_lcd_test')
def simple_lcd_test():
    return render_template('simple_lcd_test.html')

@app.route('/enhanced_lcd_test_simple')
def enhanced_lcd_test_simple_page():
    """Serve the simplified enhanced LCD test with professional look but original functionality."""
    return render_template('enhanced_lcd_test_simple.html')

server_logger = logging.getLogger('web_server')
# Basic logging config if not already set up by a higher-level runner
if not server_logger.hasHandlers():
    # Check if running in a context that might already configure logging (e.g. Gunicorn)
    if not app.config.get('TESTING', False) and not app.debug: # Avoid double logging in some dev/test setups
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.INFO)
    elif app.debug: # More verbose in debug
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.DEBUG)
        server_logger.debug("Debug mode logging enabled for web_server.")


@app.route('/api/run_tests', methods=['POST'])
def run_tests_route():
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    data = request.json
    asset_number = data.get('asset_number')
    operator_id = data.get('operator_id')
    profile_name = data.get('profile_name')
    if not all([asset_number, operator_id, profile_name]):
        return (jsonify({'error': 'Missing required fields: asset_number, operator_id, profile_name'}), 400)
    profile_to_run = load_profile(profile_name)
    if not profile_to_run:
        return (jsonify({'error': f"Profile '{profile_name}' not found"}), 404)

    # Prepare for log collection for this specific run
    asset_test_logs[asset_number] = []
    def current_run_log_callback(message, level='info'):
        log_entry = f"[{level.upper()}] {message}"
        asset_test_logs[asset_number].append(log_entry)
        # Also log to server console for debugging/monitoring
        if level == 'error':
            server_logger.error(f'[Orchestrator:{asset_number}] {message}')
        elif level == 'warning':
            server_logger.warning(f'[Orchestrator:{asset_number}] {message}')
        else:
            server_logger.info(f'[Orchestrator:{asset_number}] {message}')

    try:
        # ResultManager's log_callback can also use the current_run_log_callback
        # It does not take asset_number or operator_id in constructor.
        result_manager = ResultManager(log_callback=current_run_log_callback)
        orchestrator = TestOrchestrator(
            log_callback=current_run_log_callback,
            result_manager_instance=result_manager,
            main_app_ref=None, # No Tkinter app in headless
            get_current_profile_callback=lambda: profile_to_run,
            get_asset_number_callback=lambda: asset_number,
            get_operator_id_callback=lambda: operator_id
        )
        # Launch orchestrator in a background thread so the request returns quickly
        def _run_orchestrator():
            try:
                # Run in headless mode (web-safe tests only) for now.
                # When visual CPU/RAM versions are ready, expose a flag to switch.
                orchestrator.execute_tests(headless_mode=True)
            except Exception as e_inner:
                server_logger.error(f'Error in orchestrator thread for asset {asset_number}: {e_inner!s}', exc_info=True)
                asset_test_logs[asset_number].append(f"[ERROR] Orchestrator internal error: {str(e_inner)}")

        threading.Thread(target=_run_orchestrator, daemon=True).start()

        # Inform frontend that the visual test sequence has started and logs are available
        return (jsonify({'message': f"Test execution for profile '{profile_name}' on asset '{asset_number}' initiated."}), 200)
    except Exception as e:
        server_logger.error(f'Error during /api/run_tests for asset {asset_number}, profile {profile_name}: {e!s}', exc_info=True)
        asset_test_logs[asset_number].append(f"[ERROR] Failed to run tests due to an internal server error: {str(e)}")
        return (jsonify({'error': 'Failed to run tests due to an internal server error', 'details': str(e)}), 500)

@app.route('/api/test_log/stream/<string:asset_number>')
def test_log_stream_route(asset_number):
     """Server-Sent Events stream of live test logs for the given asset."""
     def _event_stream():
         import json, time
         last_idx = -1
         while True:
             logs = asset_test_logs.get(asset_number, [])
             if last_idx < len(logs) - 1:
                 # Send new log lines
                 for i in range(last_idx + 1, len(logs)):
                     data = json.dumps({"type": "log", "idx": i, "log": logs[i]})
                     yield f"data: {data}\n\n"
                 last_idx = len(logs) - 1
             # Heart-beat to keep connection alive
             yield ": keep-alive\n\n"
             time.sleep(0.4)
             # Add visual progress to unified SSE
             with visual_test_lock:
                 for session_id, session_data in visual_test_sessions.items():
                     if session_data['asset_number'] == asset_number:
                         progress_data = {
                             "type": "progress",
                             "progress": session_data['progress'],
                             "status": session_data['status'],
                             "test_type": session_data['test_type']
                         }
                         yield f"data: {json.dumps(progress_data)}\n\n"
     return Response(_event_stream(), mimetype='text/event-stream', headers={'Cache-Control': 'no-cache', 'X-Accel-Buffering': 'no'})


@app.route('/api/test_log/<string:asset_number>', methods=['GET'])
def get_test_log_route(asset_number):
    if not asset_number or not asset_number.strip():
        return jsonify({'error': 'Asset number is required'}), 400

    logs = asset_test_logs.get(asset_number, [])
    since_index_str = request.args.get('since_index')

    if since_index_str is not None:
        try:
            since_index = int(since_index_str)
            # Return only new logs. Add 1 because client has logs *up to* since_index.
            logs_to_return = logs[since_index + 1:]
        except ValueError:
            return jsonify({'error': 'Invalid since_index, must be an integer.'}), 400
    else:
        logs_to_return = logs

    return jsonify({'logs': logs_to_return, 'last_index': len(logs) -1 if logs else -1})

@app.route('/api/settings')
def settings_route():
     from agent.tests.test_config import DEFAULT_SETTINGS
     # Provide only visual-related defaults to keep payload small
     keys = ['visual_cpu_duration', 'visual_ram_size_mb', 'visual_ram_duration']
     filtered = {k: DEFAULT_SETTINGS.get(k) for k in keys}
     return jsonify(filtered)


@app.route('/api/available_tests', methods=['GET'])
def available_tests_route():
    # This list is based on the test_function_map in TestOrchestrator
    # Ideally, this would be dynamically generated or come from a shared configuration
    # For now, we'll hardcode it based on the known tests in TestOrchestrator.
    # We should only include tests that can be run headlessly or are platform-agnostic GUI tests
    # if the GUI tests are designed to be non-blocking or provide a result without user interaction
    # or if the profile editor simply needs a list of all *possible* tests.
    # The TestOrchestrator itself has logic to skip GUI tests in headless mode.
    # For profile editing purposes, it's probably best to list all known test paths.

    # Extracted from TestOrchestrator.test_function_map (keys)
    # Note: Some tests might be platform-specific (e.g., run_secure_wipe_test for Linux)
    # or might have visual components. The profile editor should list them, and the
    # orchestrator will handle skipping if necessary at runtime.
    known_test_paths = [
        'agent.tests.cpu_test.run_basic_cpu_test',
        'agent.tests.cpu_test.run_cpu_stress_test', # This one is conditionally added in orchestrator
        'agent.tests.ram_test.run_ram_test',
        'agent.tests.ram_test.run_advanced_ram_test',
        'agent.tests.display_test.run_lcd_test_gui',
        'agent.tests.keyboard_test.run_keyboard_test',
        'agent.tests.pointing_device_test.run_pointing_device_test',
        'agent.tests.visual_cpu_test.visual_cpu_test', # This is a visual alternative
        'agent.tests.visual_ram_test.run_visual_ram_test', # Visual alternative
        'agent.tests.drive_wipe_test.run_secure_wipe_test', # Linux specific in orchestrator
        'agent.tests.touch_screen_test.run_touch_screen_test',
        # Deprecated battery tests are handled differently by orchestrator (auto-skipped with note)
        # For selection, we might not want to list them, or list them with a (deprecated) tag.
        # For now, excluding them as orchestrator auto-skips.
        # 'agent.tests.battery_test.run_battery_test',
        # 'agent.tests.battery_test.run_battery_discharge_test',
        # 'agent.tests.battery_test.run_battery_charge_test',
        # 'agent.tests.battery_test.run_battery_full_assessment',
    ]

    # A more robust way would be to inspect TestOrchestrator's map if possible,
    # or have a shared list. For now, this synchronized list is okay.
    # We should also consider if run_cpu_stress_test is always available or depends on import.
    # The orchestrator map handles this: `**({'agent.tests.cpu_test.run_cpu_stress_test': ...} if run_cpu_stress_test else {})`
    # So, we might need to check for `run_cpu_stress_test`'s availability here too.

    # Simplified list for now, can be refined if direct inspection of TestOrchestrator is too complex here.
    # This list should represent the tests a user can choose to put into a profile.
    # The user-friendly names are derived by the frontend (e.g. in populateTestCheckboxes).
    # The profile stores these full paths.

    # Let's refine this by trying to import the test functions to see if they are available
    # This is closer to what TestOrchestrator does.

    potential_tests = {
        'Basic CPU Test': 'agent.tests.cpu_test.run_basic_cpu_test',
        'CPU Stress Test': 'agent.tests.cpu_test.run_cpu_stress_test',
        'Basic RAM Test': 'agent.tests.ram_test.run_ram_test',
        'Advanced RAM Test': 'agent.tests.ram_test.run_advanced_ram_test',
        'Display Test (LCD)': 'agent.tests.display_test.run_lcd_test_gui',
        'Keyboard Test': 'agent.tests.keyboard_test.run_keyboard_test',
        'Pointing Device Test': 'agent.tests.pointing_device_test.run_pointing_device_test',
        'Visual CPU Test': 'agent.tests.visual_cpu_test.visual_cpu_test',
        'Visual RAM Test': 'agent.tests.visual_ram_test.run_visual_ram_test',
        'Secure Wipe Test (Linux)': 'agent.tests.drive_wipe_test.run_secure_wipe_test',
        'Touch Screen Test': 'agent.tests.touch_screen_test.run_touch_screen_test',
    }

    available_display_tests = []
    for display_name, path in potential_tests.items():
        module_path, func_name = path.rsplit('.', 1)
        try:
            module = __import__(module_path, fromlist=[func_name])
            if hasattr(module, func_name):
                # For platform specific tests like wipe, we might add more checks or rely on orchestrator to skip
                # For now, if it can be imported, list it.
                available_display_tests.append({"name": display_name, "path": path})
            else:
                server_logger.warning(f"Could not find function {func_name} in {module_path} for available tests.")
        except ImportError:
            server_logger.warning(f"Could not import module {module_path} for available tests (path: {path}). Test may be platform specific or optional.")
        except Exception as e:
            server_logger.error(f"Unexpected error checking test {path}: {e}", exc_info=True)

    # Sort by display name for user convenience
    available_display_tests.sort(key=lambda x: x["name"])

    return jsonify(available_display_tests)


@app.route('/api/visual_test/start', methods=['POST'])
def start_visual_test_route():
    """Start a visual test session and return session ID."""
    if not request.json:
        return jsonify({'error': 'Invalid input, JSON required'}), 400

    data = request.json
    test_type = data.get('test_type')  # 'ram' or 'cpu'
    asset_number = data.get('asset_number')
    test_params = data.get('test_params', {})

    if not all([test_type, asset_number]):
        return jsonify({'error': 'Missing required fields: test_type, asset_number'}), 400

    if test_type not in ['ram', 'cpu', 'lcd', 'keyboard', 'pointing', 'touch']:
        return jsonify({'error': 'Invalid test_type. Must be "ram", "cpu", "lcd", "keyboard", "pointing", or "touch"'}), 400

    # Generate unique session ID
    session_id = f"{test_type}_{asset_number}_{int(time.time())}"

    # Initialize session data
    with visual_test_lock:
        visual_test_sessions[session_id] = {
            'test_type': test_type,
            'asset_number': asset_number,
            'status': 'initializing',
            'progress': {
                'overall_progress': 0,
                'operation_progress': 0,
                'operation_text': 'Initializing...',
                'cycles': 0,
                'patterns': 0,
                'errors': 0,
                'speed': 0,
                'time_left': 0,
                'system_info': {},
                # LCD-specific fields
                'current_color': '',
                'current_color_hex': '',
                'color_index': 0,
                'total_colors': 0,
                'failed_colors': [],
                'awaiting_user_input': False,
                # Keyboard-specific fields
                'total_keys': 0,
                'pressed_keys_count': 0,
                'pressed_keys': [],
                'unpressed_keys': [],
                'key_layout': {}, # To send to frontend for rendering
                # Pointing device specific fields
                'pointer_moved': False,
                'touchpad_area_entered': False,
                'pointer_stick_target_entered': False,
                'pointer_stick_target_exited': False,
                'buttons_clicked': {
                    'left': False, 'middle': False, 'right': False,
                    'back': False, 'forward': False
                },
                'scrolled_up': False,
                'scrolled_down': False,
                'ui_test_elements': { # For on-screen items that need to be interacted with
                    # e.g., 'ui_left_button_top': 'untested' (untested, pass, fail)
                },
                'current_coordinates': {'x': 0, 'y': 0},
                # Touch screen specific fields
                'targets_total': 5, # Standard number of targets
                'targets_hit_count': 0,
                'targets_status': {}, # e.g. {'target_1': 'unhit', 'target_2': 'hit'}
                'multi_touch_detected': False,
                'pinch_detected': False,
                'zoom_detected': False,
                'active_touch_points': 0 # Number of current touches
            },
            'test_params': test_params,
            'start_time': time.time(),
            'test_complete': False,
            'test_result': None
        }

    # Check if a background thread is already running for this session
    with visual_test_lock:
        if visual_test_sessions[session_id].get('background_thread_started', False):
            server_logger.warning(f"🔥 DUPLICATE THREAD BLOCKED: Background thread already started for session {session_id}")
            return jsonify({'session_id': session_id, 'message': 'Visual test already started'}), 200
        visual_test_sessions[session_id]['background_thread_started'] = True

    # Start the test in a background thread
    server_logger.info(f"Starting background thread for visual test session {session_id} (type: {test_type})")
    test_thread = threading.Thread(
        target=_run_visual_test_background,
        args=(session_id, test_type, test_params),
        daemon=True
    )
    test_thread.start()
    server_logger.info(f"Background thread started for session {session_id}")

    return jsonify({'session_id': session_id, 'message': 'Visual test started'}), 200


@app.route('/api/visual_test/progress/<session_id>')
def visual_test_progress_stream(session_id):
    """Server-Sent Events endpoint for streaming test progress."""
    def generate_progress():
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions.get(session_id)
                if not session_data:
                    yield f"data: {json.dumps({'error': 'Session not found'})}\n\n"
                    break

                # Send current progress data
                progress_data = {
                    'type': 'progress',
                    'status': session_data['status'],
                    'progress': session_data['progress'],
                    'test_complete': session_data['test_complete']
                }

                if session_data['test_result']:
                    progress_data['test_result'] = session_data['test_result']

                yield f"data: {json.dumps(progress_data)}\n\n"

                # If test is complete, send final update and break
                if session_data['test_complete']:
                    break

            time.sleep(0.5)  # Update every 500ms

    return Response(
        generate_progress(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*'
        }
    )


@app.route('/api/visual_test/stop/<session_id>', methods=['POST'])
def stop_visual_test_route(session_id):
    """Stop a running visual test and optionally persist the outcome.

    The frontend may POST a small JSON body, e.g. {"result": "completed" | "cancel"}.
    If the session has a populated `test_result` dictionary it will be persisted to
    the standard `results` directory using the shared ResultManager utility.
    """
    request_result_flag = None
    if request.is_json:
        request_result_flag = request.json.get('result')

    with visual_test_lock:
        session_data = visual_test_sessions.get(session_id)
        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        # Whatever happens, mark the session as stopping so background thread can exit
        session_data['stop_requested'] = True
        if request_result_flag:
            session_data['status'] = request_result_flag  # 'completed' | 'cancel'
        else:
            session_data['status'] = 'stopping'

    # Persist result only if we received an explicit finish signal and a test_result exists
    if request_result_flag and request_result_flag != 'cancel':
        test_result = session_data.get('test_result') or {}
        if not test_result:
            # Fallback minimal result if backend test didn't populate one yet
            test_result = {'status': 'UNKNOWN', 'notes': 'Visual test ended but no detailed result available.'}

        asset_number = session_data.get('asset_number', 'unknown_asset')
        test_name = session_data.get('test_type', 'visual_test')
        operator_id = session_data.get('operator_id', 'web_ui')  # Not currently captured, default placeholder
        try:
            res_mgr = ResultManager(log_callback=server_logger.info)
            res_mgr.add_result(asset_number, operator_id, test_name, test_result)
            # Immediately consolidate so the latest visual result is reflected.
            res_mgr.consolidate_results_for_asset(asset_number, operator_id)
        except Exception as e:
            server_logger.error(f"Failed to persist or consolidate visual test result for session {session_id}: {e}")

    return jsonify({'message': 'Stop request processed', 'session_status': request_result_flag or 'stopping'}), 200


def _run_visual_test_background(session_id, test_type, test_params):
    """Run visual test in background thread."""
    try:
        if test_type == 'ram':
            _run_ram_test_background(session_id, test_params)
        elif test_type == 'cpu':
            _run_cpu_test_background(session_id, test_params)
        elif test_type == 'lcd':
            _run_lcd_test_background(session_id, test_params)
        elif test_type == 'keyboard':
            _run_keyboard_test_background(session_id, test_params)
        elif test_type == 'pointing':
            _run_pointing_device_test_background(session_id, test_params)
        elif test_type == 'touch':
            _run_touch_screen_test_background(session_id, test_params)
    except Exception as e:
        server_logger.error(f"Error in visual test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            if session_id in visual_test_sessions:
                visual_test_sessions[session_id]['status'] = 'error'
                visual_test_sessions[session_id]['test_complete'] = True
                visual_test_sessions[session_id]['test_result'] = {
                    'status': 'ERROR',
                    'notes': f'Test error: {str(e)}'
                }


def _run_ram_test_background(session_id, test_params):
    """Run RAM test logic adapted for web UI."""
    import psutil

    # Get test parameters
    test_size_mb = test_params.get('test_size_mb', 1024)
    duration_seconds = test_params.get('duration_seconds', 30)

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'

    try:
        # Get system memory information
        mem_info = psutil.virtual_memory()
        total_mb = mem_info.total / (1024 * 1024)
        available_mb = mem_info.available / (1024 * 1024)

        # Update system info
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress']['system_info'] = {
                'total_mb': int(total_mb),
                'available_mb': int(available_mb)
            }

        # Determine actual test size
        test_size_mb = min(test_size_mb, int(available_mb * 0.25))
        test_size_bytes = test_size_mb * 1024 * 1024

        # Test patterns
        patterns = [b'\xAA', b'\x55', b'\xFF', b'\x00']
        pattern_names = [f"0x{p[0]:02X}" for p in patterns]

        # Update patterns count
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress']['patterns'] = len(patterns)

        # Track statistics
        cycles_completed = 0
        total_errors = 0
        start_time = time.time()
        end_time = start_time + duration_seconds

        # Main test loop
        while time.time() < end_time:
            # Check if stop was requested
            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                if session_data.get('stop_requested', False):
                    break

            cycles_completed += 1

            # Update progress
            elapsed = time.time() - start_time
            overall_progress = min(100, (elapsed / duration_seconds) * 100)
            time_left = max(0, duration_seconds - elapsed)

            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                session_data['progress'].update({
                    'overall_progress': overall_progress,
                    'cycles': cycles_completed,
                    'time_left': time_left,
                    'operation_text': 'Allocating memory...',
                    'operation_progress': 0
                })

            # Allocate memory
            try:
                buf = bytearray(test_size_bytes)
            except MemoryError:
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['status'] = 'error'
                    session_data['test_complete'] = True
                    session_data['test_result'] = {
                        'status': 'FAIL',
                        'notes': 'Memory allocation failed'
                    }
                return

            # Test each pattern
            for pattern_idx, pattern_byte in enumerate(patterns):
                if time.time() >= end_time:
                    break

                # Check if stop was requested
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    if session_data.get('stop_requested', False):
                        break

                pattern_hex = f"0x{pattern_byte[0]:02X}"

                # Write pattern
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['operation_text'] = f'Writing pattern {pattern_hex}...'

                start_write = time.time()
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break

                    chunk_size = min(4096, test_size_bytes - i)
                    buf[i:i+chunk_size] = pattern_byte * chunk_size

                    # Update operation progress
                    progress = (i / test_size_bytes) * 100
                    with visual_test_lock:
                        session_data = visual_test_sessions[session_id]
                        session_data['progress']['operation_progress'] = progress

                write_time = time.time() - start_write
                write_speed = (test_size_bytes / write_time) / (1024 * 1024) if write_time > 0 else 0

                # Update speed
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['speed'] = write_speed

                # Verify pattern
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['operation_text'] = f'Verifying pattern {pattern_hex}...'

                errors = 0
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break

                    chunk_size = min(4096, test_size_bytes - i)
                    chunk = buf[i:i+chunk_size]
                    expected = pattern_byte * chunk_size

                    if chunk != expected:
                        errors += 1

                    # Update operation progress
                    progress = (i / test_size_bytes) * 100
                    with visual_test_lock:
                        session_data = visual_test_sessions[session_id]
                        session_data['progress']['operation_progress'] = progress

                total_errors += errors

                # Update error count
                with visual_test_lock:
                    session_data = visual_test_sessions[session_id]
                    session_data['progress']['errors'] = total_errors

        # Test complete
        total_time = time.time() - start_time
        status = 'PASS' if total_errors == 0 else 'FAIL'

        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'complete'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': status,
                'notes': f'Completed {cycles_completed} cycles with {total_errors} errors in {total_time:.1f} seconds',
                'cycles_completed': cycles_completed,
                'errors': total_errors,
                'duration_seconds': total_time,
                'test_size_mb': test_size_mb
            }

    except Exception as e:
        server_logger.error(f"Error in RAM test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'error'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': 'ERROR',
                'notes': f'Test error: {str(e)}'
            }


def _run_cpu_test_background(session_id, test_params):
    """Run CPU test logic adapted for web UI."""
    import psutil
    # This would be implemented similar to RAM test
    # For now, just a placeholder
    from agent.tests.test_config import get_setting
    duration_seconds = int(test_params.get('duration_seconds', get_setting('visual_cpu_duration')))

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress']['operation_text'] = 'Running CPU stress test...'

    # Simulate CPU test progress
    psutil.cpu_percent(interval=None)  # Prime reading
    start_time = time.time()
    end_time = start_time + duration_seconds

    while time.time() < end_time:
        # Check if stop was requested
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            if session_data.get('stop_requested', False):
                break

        elapsed = time.time() - start_time
        progress = min(100, (elapsed / duration_seconds) * 100)
        time_left = max(0, duration_seconds - elapsed)
        cpu_usage = psutil.cpu_percent(interval=None)

        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['progress'].update({
                'overall_progress': progress,
                'time_left': time_left,
                'cpu_usage': cpu_usage
            })

        time.sleep(0.1)

    # Complete the test
    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['test_result'] = {
            'status': 'PASS',
            'notes': f'CPU test completed in {duration_seconds} seconds'
        }


def _run_lcd_test_background(session_id, test_params):
    """Run LCD test logic adapted for web UI."""
    # Test colors matching the original tkinter implementation
    test_colors = [
        ("Black", "#000000"),
        ("White", "#FFFFFF"),
        ("Red", "#FF0000"),
        ("Green", "#00FF00"),
        ("Blue", "#0000FF")
    ]

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress'].update({
            'total_colors': len(test_colors),
            'color_index': 0,
            'current_color': test_colors[0][0],
            'current_color_hex': test_colors[0][1],
            'operation_text': f'Displaying {test_colors[0][0]} - Press OK if correct, FAIL if not',
            'awaiting_user_input': True
        })

    try:
        # LCD test is user-driven, so we just wait for user input
        # The test progresses through user interaction via the input endpoint
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions[session_id]
                if session_data.get('stop_requested', False) or session_data['test_complete']:
                    break

            time.sleep(0.1)  # Small sleep to prevent busy waiting

    except Exception as e:
        server_logger.error(f"Error in LCD test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            session_data['status'] = 'error'
            session_data['test_complete'] = True
            session_data['test_result'] = {
                'status': 'ERROR',
                'notes': f'Test error: {str(e)}'
            }


@app.route('/api/keyboard_test_complete', methods=['POST'])
def keyboard_test_complete_route():
    """Handle completion notification from native keyboard test."""
    if not request.json:
        return jsonify({'error': 'Invalid input, JSON required'}), 400
    
    data = request.json
    session_id = data.get('session_id')
    result = data.get('result', {})
    
    if not session_id:
        return jsonify({'error': 'Missing session_id'}), 400
    
    server_logger.info(f"🎯 Keyboard test completion notification received for session {session_id}")
    
    # Update the session to mark it as complete
    with visual_test_lock:
        session_data = visual_test_sessions.get(session_id)
        if session_data:
            session_data['test_complete'] = True
            session_data['status'] = 'completed'
            session_data['test_result'] = {
                'status': 'PASS' if result.get('status') == 'PASS' else 'FAIL',
                'notes': result.get('notes', 'Native keyboard test completed'),
                'pressed_keys_count': result.get('pressed_keys_count', 0),
                'total_keys': result.get('total_keys', 0),
                'percentage': result.get('percentage', 0),
                'untested_keys': result.get('untested_keys', []),
                'force_completed': result.get('force_completed', False)
            }
            server_logger.info(f"✅ Keyboard test session {session_id} marked as complete")
        else:
            server_logger.warning(f"❌ Keyboard test session {session_id} not found")
    
    # Notify any waiting visual test sequence that the keyboard test is done
    # We'll use a simple global flag that the frontend can check
    global keyboard_test_completed_sessions
    if 'keyboard_test_completed_sessions' not in globals():
        keyboard_test_completed_sessions = set()
    keyboard_test_completed_sessions.add(session_id)
    
    return jsonify({'message': 'Keyboard test completion processed'}), 200

@app.route('/api/keyboard_test_status/<session_id>')
def keyboard_test_status_route(session_id):
    """Check if a keyboard test session has completed."""
    global keyboard_test_completed_sessions
    if 'keyboard_test_completed_sessions' not in globals():
        keyboard_test_completed_sessions = set()
    
    completed = session_id in keyboard_test_completed_sessions
    if completed:
        # Remove from set once checked to clean up
        keyboard_test_completed_sessions.discard(session_id)
    
    return jsonify({'completed': completed}), 200

@app.route('/api/visual_test/input/<session_id>', methods=['POST'])
def visual_test_input_route(session_id):
    """Handle user input for visual tests (e.g., LCD test color approval)."""
    if not request.json:
        return jsonify({'error': 'Invalid input, JSON required'}), 400

    data = request.json
    action = data.get('action')  # 'ok', 'fail', 'next', etc.

    with visual_test_lock:
        session_data = visual_test_sessions.get(session_id)
        if not session_data:
            return jsonify({'error': 'Session not found'}), 404

        test_type = session_data['test_type']

        if test_type == 'lcd':
            return _handle_lcd_input(session_id, session_data, action)
        elif test_type == 'keyboard':
            return _handle_keyboard_input(session_id, session_data, action, data)
        elif test_type == 'pointing':
            return _handle_pointing_device_input(session_id, session_data, action, data)
        elif test_type == 'touch':
            return _handle_touch_screen_input(session_id, session_data, action, data)
        else:
            return jsonify({'error': f'Input not supported for test type: {test_type}'}), 400

# <<< POINTING DEVICE TEST BACKGROUND AND HANDLER (EXISTING CODE) >>>
# ... (Keep existing _run_pointing_device_test_background and _handle_pointing_device_input functions here) ...
# For brevity, I am not copying them here, but they should remain.

def _run_touch_screen_test_background(session_id, test_params):
    """Run Touch Screen test logic adapted for web UI."""
    # Linux touchscreen detection; skip if absent to avoid blocking on non-touch devices.
    try:
        import subprocess, re, shutil
        has_touch = False
        if shutil.which('libinput'):
            out = subprocess.check_output(['libinput', 'list-devices'], text=True, stderr=subprocess.DEVNULL)
            if re.search(r'touch\s*screen|touchscreen', out, re.I):
                has_touch = True
        if not has_touch and shutil.which('xinput'):
            out = subprocess.check_output(['xinput', 'list'], text=True, stderr=subprocess.DEVNULL)
            if re.search(r'touch\s*screen|touchscreen', out, re.I):
                has_touch = True
        if not has_touch:
            try:
                with open('/proc/bus/input/devices') as f:
                    if re.search(r'touchscreen', f.read(), re.I):
                        has_touch = True
            except Exception:
                pass
    except Exception as det_err:
        has_touch = False
        server_logger.warning(f"Touchscreen detection error: {det_err}")

    if not has_touch:
        with visual_test_lock:
            session_data = visual_test_sessions[session_id]
            # Mark as complete and set SKIPPED (N/A) so the UI auto-closes and result clearly reflects not applicable.
            session_data['status'] = 'complete'
            session_data['test_complete'] = True
            session_data['progress']['awaiting_user_input'] = False
            session_data['test_result'] = {
                'status': 'skipped',
                'notes': 'N/A - No touchscreen hardware detected. Test skipped automatically.'
            }
        server_logger.info(f"Touch screen test {session_id} skipped (no touchscreen detected).")
        return

    num_targets = 5 # As per original test
    initial_targets_status = {f'target_{i+1}': 'unhit' for i in range(num_targets)}

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress'].update({
            'targets_total': num_targets,
            'targets_hit_count': 0,
            'targets_status': initial_targets_status, # e.g. {'target_1': 'unhit', ...}
            'multi_touch_detected': False,
            'gesture_performed': False, # True if any pinch or zoom gesture is made
            # Specific gesture flags can remain for info bar if desired, but completion relies on gesture_performed
            'pinch_event_detected': False,
            'zoom_event_detected': False,
            'active_touch_points': 0,
            'operation_text': f'Touch the {num_targets} targets. Then use two fingers for multi-touch & a pinch or zoom gesture.',
            'awaiting_user_input': True
        })
        server_logger.info(f"Touch screen test {session_id} started.")

    try:
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions.get(session_id)
                if not session_data or session_data.get('stop_requested', False) or session_data['test_complete']:
                    server_logger.info(f"Touch screen test {session_id} stopping or completed.")
                    break
            time.sleep(0.1)
    except Exception as e:
        server_logger.error(f"Error in Touch Screen test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            if session_id in visual_test_sessions:
                session_data = visual_test_sessions[session_id]
                session_data['status'] = 'error'
                session_data['test_complete'] = True
                session_data['test_result'] = { 'status': 'ERROR', 'notes': f'Test error: {str(e)}' }

def _handle_touch_screen_input(session_id, session_data, action, data):
    """Handle Touch Screen test user input (touch events, gestures, completion)."""
    progress = session_data['progress']
    response_message = "Touch event processed."

    if action == 'touch_event':
        target_id = data.get('target_id') # e.g., 'target_1'
        num_touches = data.get('touch_points_count', 0)
        progress['active_touch_points'] = num_touches

        if target_id and target_id in progress['targets_status'] and progress['targets_status'][target_id] == 'unhit':
            progress['targets_status'][target_id] = 'hit'
            progress['targets_hit_count'] = sum(1 for status in progress['targets_status'].values() if status == 'hit')
            response_message = f"Target {target_id} hit."

        if num_touches >= 2 and not progress['multi_touch_detected']:
            progress['multi_touch_detected'] = True
            response_message += " Multi-touch detected."
            progress['operation_text'] = "Multi-touch active. Try pinch/zoom gestures."


    elif action == 'gesture_event':
        gesture_type = data.get('gesture_type') # 'pinch' or 'zoom'
        if not progress['multi_touch_detected']: # Gesture requires multi-touch first
            return jsonify({'error': 'Multi-touch not detected prior to gesture.'}), 400

        if gesture_type == 'pinch':
            progress['pinch_event_detected'] = True
            progress['gesture_performed'] = True # Key flag for completion
            response_message = "Pinch gesture detected."
        elif gesture_type == 'zoom':
            progress['zoom_event_detected'] = True
            progress['gesture_performed'] = True # Key flag for completion
            response_message = "Zoom gesture detected."

        if progress['gesture_performed']:
             progress['operation_text'] = "Gesture detected! You can complete the test if other steps are done."


    elif action == 'complete_test':
        force_complete = data.get('force_complete', False)

        passed_all_targets = progress['targets_hit_count'] == progress['targets_total']
        passed_multi_touch = progress['multi_touch_detected']
        # Align with Tkinter: pinch_zoom_completed means any significant gesture was made.
        passed_gesture_phase = progress['gesture_performed']

        final_status_str = "PASS"
        notes_parts = []
        notes_parts.append(f"Targets Hit: {progress['targets_hit_count']}/{progress['targets_total']}.")
        notes_parts.append(f"Multi-touch Detected: {'Yes' if passed_multi_touch else 'No'}.")
        notes_parts.append(f"Gesture Performed: {'Yes' if passed_gesture_phase else 'No'} (Pinch: {'Yes' if progress.get('pinch_event_detected') else 'No'}, Zoom: {'Yes' if progress.get('zoom_event_detected') else 'No'}).")


        if force_complete:
            final_status_str = "PASS"
            notes = "Test force-completed. " + " ".join(notes_parts)
        elif not (passed_all_targets and passed_multi_touch and passed_gesture_phase):
            final_status_str = "FAIL"
            notes = "Test failed. " + " ".join(notes_parts)
        else:
            notes = "Test passed. " + " ".join(notes_parts)

        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['progress']['awaiting_user_input'] = False
        session_data['test_result'] = {
            'status': final_status_str,
            'notes': notes,
            'details': {
                'targets_hit': progress['targets_hit_count'],
                'targets_total': progress['targets_total'],
                'multi_touch_detected': passed_multi_touch,
                'gesture_performed': passed_gesture_phase,
                'pinch_event_detected': progress.get('pinch_event_detected', False),
                'zoom_event_detected': progress.get('zoom_event_detected', False),
                'force_completed': force_complete
            }
        }
        server_logger.info(f"Touch screen test {session_id} completed. Status: {final_status_str}. Notes: {notes}")
        return jsonify({'message': 'Touch screen test completed', 'test_result': session_data['test_result']}), 200
    else:
        return jsonify({'error': f'Invalid action for touch screen test: {action}'}), 400

    # Update overall progress
    phases_total = 3 # Targets, Multi-touch, Gesture (any)
    phases_done = 0
    if progress['targets_hit_count'] == progress['targets_total']: phases_done +=1
    if progress['multi_touch_detected']: phases_done +=1
    if progress['gesture_performed']: phases_done +=1 # Check the main gesture flag
    progress['overall_progress'] = (phases_done / phases_total) * 100 if phases_total > 0 else 0

    return jsonify({'message': response_message, 'progress': progress}), 200


def _run_pointing_device_test_background(session_id, test_params):
    """Run Pointing Device test logic adapted for web UI."""
    # Mirrored from StreamlinedPointingTest.test_elements_config and ui_button_states
    # Physical device components
    initial_physical_elements_state = {
        "Pointer Movement": "untested", # Tracks general pointer movement
        "Touchpad Interaction": "untested", # Tracks interaction with the main touchpad zone
        "Pointer Stick Target": "untested", # Tracks entering and exiting the central circle
        "Left Button Physical": "untested",
        "Middle Button Physical": "untested",
        "Right Button Physical": "untested",
        "Scroll Up": "untested",
        "Scroll Down": "untested",
        "Back Button Physical": "untested",
        "Forward Button Physical": "untested",
    }
    # On-screen UI elements that need to be clicked correctly
    initial_ui_test_elements_status = {
        # These IDs should match the `id` field in `testElements.buttons` in pointing_device_test.html
        "button_ui_left_top": "untested",    # Expects physical left click
        "button_ui_middle_top": "untested",  # Expects physical middle click
        "button_ui_right_top": "untested",   # Expects physical right click
        "button_ui_left_bottom": "untested", # Expects physical left click
        "button_ui_middle_bottom": "untested",# Expects physical middle click
        "button_ui_right_bottom": "untested",# Expects physical right click
    }

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress'].update({
            # Physical component statuses
            **initial_physical_elements_state, # Unpack all physical states here
            # UI test elements statuses
            'ui_test_elements_status': initial_ui_test_elements_status,
            # Helper flags for complex states like pointer stick target
            '_pointer_stick_target_entered_flag': False, # Internal flag
            # General info
            'current_coordinates': {'x': 0, 'y': 0},
            'operation_text': 'Move pointer, click physical buttons, use scroll, and click the on-screen UI buttons correctly.',
            'awaiting_user_input': True
        })
        server_logger.info(f"Pointing device test {session_id} started with detailed progress structure.")

    try:
        # Pointing device test is user-driven. Backend waits for events or completion signal.
        while True:
            with visual_test_lock:
                session_data = visual_test_sessions.get(session_id)
                if not session_data or session_data.get('stop_requested', False) or session_data['test_complete']:
                    server_logger.info(f"Pointing device test {session_id} stopping or completed.")
                    break
            time.sleep(0.1)
    except Exception as e:
        server_logger.error(f"Error in Pointing Device test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            if session_id in visual_test_sessions:
                session_data = visual_test_sessions[session_id]
                session_data['status'] = 'error'
                session_data['test_complete'] = True
                session_data['test_result'] = { 'status': 'ERROR', 'notes': f'Test error: {str(e)}' }


def _handle_pointing_device_input(session_id, session_data, action, data):
    """Handle Pointing Device test user input (mouse events or test completion)."""
    progress = session_data['progress']
    response_message = "Event processed."

    if action == 'mouse_move':
        if progress.get("Pointer Movement") == "untested":
            progress["Pointer Movement"] = "pass"
        coordinates = data.get('coordinates')
        if coordinates:
            progress['current_coordinates'] = coordinates

        event_type = data.get('event_type') # Sent by frontend: 'touchpad_enter', 'pointer_stick_target_enter', 'pointer_stick_target_exit'
        if event_type == 'touchpad_enter' and progress.get("Touchpad Interaction") == "untested":
            progress["Touchpad Interaction"] = "pass"
        elif event_type == 'pointer_stick_target_enter':
            progress['_pointer_stick_target_entered_flag'] = True
            # Status becomes 'pass' only upon successful exit
        elif event_type == 'pointer_stick_target_exit':
            if progress.get('_pointer_stick_target_entered_flag') and progress.get("Pointer Stick Target") == "untested":
                progress["Pointer Stick Target"] = "pass"
        response_message = f"Mouse moved to {coordinates}. Event type: {event_type}"


    elif action == 'button_click':
        physical_button_map = {
            'left': "Left Button Physical", 'middle': "Middle Button Physical", 'right': "Right Button Physical",
            'back': "Back Button Physical", 'forward': "Forward Button Physical"
        }
        button_type = data.get('button_type') # This is the physical button: 'left', 'middle', 'right', 'back', 'forward'
        ui_element_id = data.get('ui_element_id') # This is the ID of the on-screen drawn button, if clicked

        # 1. Record physical button press
        if button_type and physical_button_map.get(button_type) in progress:
            if progress[physical_button_map[button_type]] == "untested":
                 progress[physical_button_map[button_type]] = "pass"
            response_message = f"Physical {button_type} button click registered."

        # 2. Handle interaction with on-screen UI test elements
        if ui_element_id and ui_element_id in progress.get('ui_test_elements_status', {}):
            # Determine the expected physical button for this UI element
            # This logic should align with how ui_element_id maps to expected interaction
            # e.g. "button_ui_left_top" expects a "left" physical click.
            expected_physical_key = None
            if "left" in ui_element_id: expected_physical_key = "left"
            elif "middle" in ui_element_id: expected_physical_key = "middle"
            elif "right" in ui_element_id: expected_physical_key = "right"
            # Add more for other types if UI elements test other physical buttons like back/forward

            current_ui_element_status = progress['ui_test_elements_status'][ui_element_id]

            if expected_physical_key:
                if button_type == expected_physical_key:
                    if current_ui_element_status == "untested":
                        progress['ui_test_elements_status'][ui_element_id] = "pass"
                        response_message += f" UI element {ui_element_id} correctly clicked (PASSED)."
                    # If already pass/fail, clicking again doesn't change it unless specific toggle logic is needed
                else: # Wrong physical button for this UI element
                    progress['ui_test_elements_status'][ui_element_id] = "fail"
                    response_message += f" UI element {ui_element_id} clicked with WRONG button ({button_type} instead of {expected_physical_key}) (FAILED)."
            else: # UI element doesn't have a specific physical button expectation (should not happen for these)
                if current_ui_element_status == "untested":
                    progress['ui_test_elements_status'][ui_element_id] = "pass" # Generic pass
                response_message += f" UI element {ui_element_id} interacted."


    elif action == 'scroll':
        direction = data.get('direction') # 'up' or 'down'
        if direction == 'up' and progress.get("Scroll Up") == "untested":
            progress["Scroll Up"] = "pass"
        elif direction == 'down' and progress.get("Scroll Down") == "untested":
            progress["Scroll Down"] = "pass"

    elif action == 'complete_test':
        force_complete = data.get('force_complete', False)
        notes_list = []
        passed_all_physical = True
        passed_all_ui_elements = True
        
        # List of relevant physical components we're testing
        relevant_physical_components = [
            "Pointer Movement",
            "Touchpad Interaction",
            "Pointer Stick Target",
            "Left Button Physical",
            "Middle Button Physical",
            "Right Button Physical"
        ]

        # Check physical components - only include relevant ones
        for key in relevant_physical_components:
            if key in progress:
                status_val = progress[key]
                notes_list.append(f"{key}: {status_val}")
                if status_val == "untested" or status_val == "fail": # Consider 'untested' as not passed for overall
                    if not (key == "Middle Button Physical"): # Optional button
                        passed_all_physical = False

        # Check UI test elements - only include relevant ones
        if 'ui_test_elements_status' in progress:
            for ui_key, ui_status_val in progress['ui_test_elements_status'].items():
                # Only include UI elements for the buttons we're keeping
                if ui_key in ['button_ui_left_top', 'button_ui_middle_top', 'button_ui_right_top',
                              'button_ui_left_bottom', 'button_ui_middle_bottom', 'button_ui_right_bottom']:
                    notes_list.append(f"UI Element {ui_key}: {ui_status_val}")
                    if ui_status_val == "fail" or ui_status_val == "untested":
                        passed_all_ui_elements = False

        final_status_str = "PASS"
        if force_complete:
            final_status_str = "PASS" # Policy: forced completion is a pass
            notes_list.append("Test was force-completed.")
        elif not passed_all_physical or not passed_all_ui_elements:
            final_status_str = "FAIL"

        final_notes = "\n".join(notes_list)
        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['progress']['awaiting_user_input'] = False
        # Filter details to only include relevant components
        relevant_details = {}
        
        # Include only the relevant physical components
        relevant_physical_components = [
            "Pointer Movement",
            "Touchpad Interaction",
            "Pointer Stick Target",
            "Left Button Physical",
            "Middle Button Physical",
            "Right Button Physical"
        ]
        
        for key in relevant_physical_components:
            if key in progress:
                relevant_details[key] = progress[key]
        
        # Include UI test elements for the buttons we're keeping
        if 'ui_test_elements_status' in progress:
            ui_elements = {}
            for ui_key, ui_status_val in progress['ui_test_elements_status'].items():
                if ui_key in ['button_ui_left_top', 'button_ui_middle_top', 'button_ui_right_top',
                              'button_ui_left_bottom', 'button_ui_middle_bottom', 'button_ui_right_bottom']:
                    ui_elements[ui_key] = ui_status_val
            relevant_details['ui_test_elements_status'] = ui_elements
        
        # Include overall progress
        if 'overall_progress' in progress:
            relevant_details['overall_progress'] = progress['overall_progress']
            
        session_data['test_result'] = {
            'status': final_status_str,
            'notes': final_notes,
            'details': relevant_details,
            'force_completed': force_complete
        }
        server_logger.info(f"Pointing device test {session_id} completed. Status: {final_status_str}. Notes:\n{final_notes}")
        return jsonify({'message': 'Pointing device test completed', 'test_result': session_data['test_result']}), 200

    else:
        return jsonify({'error': f'Invalid action for pointing device test: {action}'}), 400

    # Calculate overall progress based only on relevant test elements
    total_testable_elements = 0
    passed_elements = 0
    
    # List of relevant physical components we're testing
    relevant_physical_components = [
        "Pointer Movement",
        "Touchpad Interaction",
        "Pointer Stick Target",
        "Left Button Physical",
        "Middle Button Physical",
        "Right Button Physical"
    ]
    
    # Count physical components
    for key in relevant_physical_components:
        if key in progress:
            total_testable_elements += 1
            if progress[key] == "pass":
                passed_elements += 1
    
    # Count UI elements
    relevant_ui_elements = [
        'button_ui_left_top', 'button_ui_middle_top', 'button_ui_right_top',
        'button_ui_left_bottom', 'button_ui_middle_bottom', 'button_ui_right_bottom'
    ]
    
    if 'ui_test_elements_status' in progress:
        for ui_key in relevant_ui_elements:
            if ui_key in progress['ui_test_elements_status']:
                total_testable_elements += 1
                if progress['ui_test_elements_status'][ui_key] == "pass":
                    passed_elements += 1

    progress['overall_progress'] = (passed_elements / total_testable_elements) * 100 if total_testable_elements > 0 else 0

    return jsonify({'message': response_message, 'progress': progress}), 200


def _run_keyboard_test_background(session_id, test_params):
    """Run native Keyboard test by launching it in a new terminal."""
    import os
    import subprocess
    from agent.tests.keyboard_test import KEYBOARD_LAYOUT
    
    # Add debug logging to track function calls
    server_logger.info(f"🔥 _run_keyboard_test_background called for session {session_id}")
    import threading
    server_logger.info(f"🔥 Current thread: {threading.current_thread().name}")
    server_logger.info(f"🔥 Active keyboard sessions before check: {active_keyboard_sessions}")
    import traceback
    server_logger.info(f"Call stack: {traceback.format_stack()}")

    # Flatten KEYBOARD_LAYOUT to a list of all key names for web UI compatibility
    all_keys_in_layout = []
    for row_name, keys_in_row in KEYBOARD_LAYOUT.items():
        for key_info in keys_in_row:
            all_keys_in_layout.append(key_info['key'])

    unique_layout_keys = sorted(list(set(all_keys_in_layout)))

    with visual_test_lock:
        session_data = visual_test_sessions[session_id]
        session_data['status'] = 'running'
        session_data['progress'].update({
            'key_layout': KEYBOARD_LAYOUT,
            'total_keys': len(unique_layout_keys),
            'unpressed_keys': list(unique_layout_keys),
            'pressed_keys': [],
            'pressed_keys_count': 0,
            'operation_text': 'Launching native keyboard test window...',
            'awaiting_user_input': True,
            'native_test_running': True,
            'debug_log': []  # Add debug log array
        })
        server_logger.info(f"Native Keyboard test {session_id} starting. Total keys: {len(unique_layout_keys)}")
    
    # Helper function to add debug messages that will be sent to browser
    def add_debug_log(message):
        with visual_test_lock:
            session_data = visual_test_sessions.get(session_id)
            if session_data:
                if 'debug_log' not in session_data['progress']:
                    session_data['progress']['debug_log'] = []
                session_data['progress']['debug_log'].append(message)
                server_logger.info(f"Keyboard test debug: {message}")
    
    # Use global lock to prevent multiple keyboard test launches
    with keyboard_test_lock:
        if session_id in active_keyboard_sessions:
            server_logger.warning(f"🔥 DUPLICATE CALL BLOCKED: Keyboard test session {session_id} is already being processed! Skipping duplicate call.")
            return
        active_keyboard_sessions.add(session_id)
        server_logger.info(f"🔥 ADDED TO ACTIVE SESSIONS: {session_id}, active sessions now: {active_keyboard_sessions}")
    
    # Ensure we remove from active sessions when done
    def cleanup_session():
        with keyboard_test_lock:
            active_keyboard_sessions.discard(session_id)
    
    try:
        add_debug_log("Starting keyboard test launch process...")
        # Create a simple test script that runs the keyboard test
        test_script = f'''#!/usr/bin/env python3
import sys
import os
sys.path.append('{os.getcwd()}')

try:
    import tkinter as tk
    from agent.tests.keyboard_test import run_keyboard_test
    import requests
    import json
    
    # Create root window
    root = tk.Tk()
    root.title("Keyboard Test")
    
    # Run the keyboard test
    result = run_keyboard_test(root)
    print(f"Keyboard test completed: {{result}}")
    
    # Notify the web server that the keyboard test is complete
    try:
        response = requests.post('http://127.0.0.1:5000/api/keyboard_test_complete', 
                               json={{'session_id': '{session_id}', 'result': result}},
                               timeout=5)
        if response.ok:
            print("Successfully notified web server of completion")
        else:
            print(f"Failed to notify web server: {{response.status_code}}")
    except Exception as notify_error:
        print(f"Error notifying web server: {{notify_error}}")
    
except Exception as e:
    print(f"Error running keyboard test: {{e}}")
    import traceback
    traceback.print_exc()
'''
        
        # Write the script to a temporary file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(test_script)
            script_path = f.name
        
        # Make the script executable
        os.chmod(script_path, 0o755)
        
        try:
            add_debug_log("Creating keyboard test script...")
            add_debug_log(f"Script path: {script_path}")
            add_debug_log(f"Script executable: {os.access(script_path, os.X_OK)}")
            add_debug_log(f"Python path: {os.getcwd()}")
            
            # Update status to show we're launching
            with visual_test_lock:
                session_data = visual_test_sessions.get(session_id)
                if session_data:
                    session_data['progress']['operation_text'] = 'Native keyboard test window should open now. Check for a new window.'
            
            # Try different ways to launch the test depending on the environment
            launched = False
            add_debug_log("Starting launch attempts...")
            
            # Try direct launch first (better for custom systems)
            add_debug_log("Attempting direct launch with python3...")
            try:
                # Launch directly without terminal (should work on custom Debian)
                process = subprocess.Popen([
                    'python3', script_path
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                launched = True
                add_debug_log("✅ Process launched successfully")
                server_logger.info("Launched keyboard test directly")
                
                # Log any immediate errors
                import time
                time.sleep(1)  # Give it a moment to start
                if process.poll() is not None:
                    # Process already exited
                    stdout, stderr = process.communicate()
                    add_debug_log(f"❌ Process exited immediately with return code: {process.returncode}")
                    add_debug_log(f"STDOUT: {stdout}")
                    add_debug_log(f"STDERR: {stderr}")
                    server_logger.error(f"Keyboard test process exited immediately. Return code: {process.returncode}")
                    server_logger.error(f"STDOUT: {stdout}")
                    server_logger.error(f"STDERR: {stderr}")
                    launched = False
                else:
                    add_debug_log("✅ Process is running")
                    
            except Exception as e:
                add_debug_log(f"❌ Direct launch failed: {str(e)}")
                server_logger.error(f"Failed to launch keyboard test directly: {e}")
                launched = False
            
            # If direct launch failed, try terminal launch as fallback
            if not launched:
                add_debug_log("Direct launch failed, trying terminal emulators...")
                terminal_commands = [
                    ['gnome-terminal', '--', 'python3', script_path],
                    ['konsole', '-e', 'python3', script_path],
                    ['xterm', '-e', 'python3', script_path],
                    ['x-terminal-emulator', '-e', 'python3', script_path]
                ]
                
                for cmd in terminal_commands:
                    try:
                        add_debug_log(f"Trying: {' '.join(cmd)}")
                        subprocess.Popen(cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
                        launched = True
                        add_debug_log(f"✅ Launched with: {' '.join(cmd)}")
                        server_logger.info(f"Launched keyboard test using: {' '.join(cmd)}")
                        break
                    except (FileNotFoundError, subprocess.SubprocessError) as e:
                        add_debug_log(f"❌ {' '.join(cmd)} failed: {str(e)}")
                        server_logger.warning(f"Terminal command failed: {' '.join(cmd)} - {e}")
                        continue
            
            if launched:
                # Update status to indicate test is running
                with visual_test_lock:
                    session_data = visual_test_sessions.get(session_id)
                    if session_data:
                        session_data['progress']['operation_text'] = 'Native keyboard test is running. Look for the keyboard test window and press keys to test them.'
                
                # Wait a bit then mark as completed (since we can't track the external process easily)
                import time
                time.sleep(2)
                
                with visual_test_lock:
                    session_data = visual_test_sessions.get(session_id)
                    if session_data:
                        session_data['test_complete'] = True
                        session_data['status'] = 'completed'
                        session_data['test_result'] = {
                            'status': 'launched',
                            'notes': 'Native keyboard test window launched successfully. The test runs independently - close the keyboard test window when finished.',
                            'pressed_keys_count': 0,
                            'total_keys': len(unique_layout_keys),
                            'percentage': 0,
                            'untested_keys': unique_layout_keys,
                            'force_completed': False
                        }
                        server_logger.info(f"Native Keyboard test {session_id} launched successfully")
            else:
                # Failed to launch
                with visual_test_lock:
                    session_data = visual_test_sessions.get(session_id)
                    if session_data:
                        session_data['test_complete'] = True
                        session_data['status'] = 'error'
                        session_data['test_result'] = {
                            'status': 'error',
                            'notes': 'Failed to launch native keyboard test. No suitable terminal emulator found.'
                        }
                        server_logger.error(f"Failed to launch keyboard test for session {session_id}")
        
        finally:
            # Clean up the script file after a delay (let it run first)
            def cleanup_later():
                import time
                time.sleep(10)  # Wait 10 seconds before cleanup
                try:
                    os.unlink(script_path)
                except:
                    pass
            
            import threading
            threading.Thread(target=cleanup_later, daemon=True).start()
            
    except Exception as e:
        server_logger.error(f"Error in native Keyboard test {session_id}: {e}", exc_info=True)
        with visual_test_lock:
            if session_id in visual_test_sessions:
                visual_test_sessions[session_id]['test_complete'] = True
                visual_test_sessions[session_id]['status'] = 'error'
                visual_test_sessions[session_id]['test_result'] = {
                    'status': 'error',
                    'notes': f'Native test error: {str(e)}'
                }
    finally:
        # Always cleanup the session from active sessions
        cleanup_session()

def _handle_keyboard_input(session_id, session_data, action, data):
    """Handle Keyboard test user input - now mainly for native test communication."""
    
    if action == 'complete_test':
        # For native test, we don't handle completion here - the native test handles it
        # But we can acknowledge the request
        return jsonify({'message': 'Native keyboard test completion handled by native window'}), 200
    
    elif action == 'key_press':
        # For native test, key presses are handled by the native window
        # The web interface might still send these, but we ignore them
        # since the native test updates progress through the log callback
        return jsonify({'message': 'Key press handled by native keyboard test'}), 200
    
    else:
        return jsonify({'error': f'Invalid action for keyboard test: {action}'}), 400


def _handle_lcd_input(session_id, session_data, action):
    """Handle LCD test user input."""
    test_colors = [
        ("Black", "#000000"),
        ("White", "#FFFFFF"),
        ("Red", "#FF0000"),
        ("Green", "#00FF00"),
        ("Blue", "#0000FF")
    ]

    current_index = session_data['progress']['color_index']

    if action == 'fail':
        # Mark current color as failed
        current_color = test_colors[current_index][0]
        session_data['progress']['failed_colors'].append(current_color)
        session_data['progress']['errors'] += 1
        server_logger.info(f"LCD test {session_id}: Color {current_color} marked as FAILED")
    elif action == 'ok':
        # Current color is OK
        current_color = test_colors[current_index][0]
        server_logger.info(f"LCD test {session_id}: Color {current_color} marked as OK")
    else:
        return jsonify({'error': 'Invalid action. Must be "ok" or "fail"'}), 400

    # Move to next color
    next_index = current_index + 1

    if next_index < len(test_colors):
        # Update to next color
        session_data['progress'].update({
            'color_index': next_index,
            'current_color': test_colors[next_index][0],
            'current_color_hex': test_colors[next_index][1],
            'operation_text': f'Displaying {test_colors[next_index][0]} - Press OK if correct, FAIL if not',
            'overall_progress': (next_index / len(test_colors)) * 100,
            'awaiting_user_input': True
        })
    else:
        # Test complete
        failed_colors = session_data['progress']['failed_colors']
        status = 'PASS' if len(failed_colors) == 0 else 'FAIL'

        session_data['status'] = 'complete'
        session_data['test_complete'] = True
        session_data['progress']['awaiting_user_input'] = False
        session_data['progress']['overall_progress'] = 100
        session_data['test_result'] = {
            'status': status,
            'notes': f'LCD test completed. Failed colors: {", ".join(failed_colors)}' if failed_colors else 'All colors displayed correctly',
            'failed_colors': failed_colors,
            'total_colors': len(test_colors)
        }

        server_logger.info(f"LCD test {session_id} completed: {status}")

    return jsonify({'message': 'Input processed', 'test_complete': session_data['test_complete']}), 200


if __name__ == '__main__':
    # Ensure logging is set up for direct execution
    if not server_logger.hasHandlers():
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.DEBUG) # Or INFO for less verbosity
    # app.run(debug=True, host='0.0.0.0', port=5000) # 실행은 run_web_ui.py에서 담당
    server_logger.info("web_server/app.py executed directly. Server will not start from here. Run run_web_ui.py to start.")


@app.route('/api/system_info')
def system_info_route():
    try:
        info = get_system_info()
        return jsonify(info)
    except Exception as e:
        print(f'Error in /api/system_info: {e!s}')
        return (jsonify({'error': 'Failed to retrieve system information', 'details': str(e)}), 500)

@app.route('/api/system/memory_info')
def memory_info_route():
    """Get current system memory information for RAM test configuration."""
    try:
        import psutil
        
        # Get memory information
        mem_info = psutil.virtual_memory()
        total_mb = int(mem_info.total / (1024 * 1024))
        available_mb = int(mem_info.available / (1024 * 1024))
        
        # Calculate common percentage values for UI convenience
        percentage_calculations = {}
        for percentage in [10, 25, 30, 40, 50]:
            percentage_calculations[str(percentage)] = int(available_mb * percentage / 100)
        
        response_data = {
            'total_mb': total_mb,
            'available_mb': available_mb,
            'percentage_calculations': percentage_calculations
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        server_logger.error(f'Error in /api/system/memory_info: {e!s}', exc_info=True)
        return jsonify({
            'error': 'Failed to retrieve memory information', 
            'details': str(e)
        }), 500

def profile_exists(profile_name):
    return os.path.exists(get_profile_path(profile_name))

@app.route('/api/profiles', methods=['GET'])
def list_profiles_route():
    profiles = get_all_profiles()
    return jsonify([p.to_dict() for p in profiles])

@app.route('/api/profiles/validate/<string:profile_name>', methods=['GET'])
def validate_profile_route(profile_name):
    """Validate that all tests referenced in a profile are available/importable."""
    from agent.tests.profiles import load_profile
    import importlib
    profile = load_profile(profile_name)
    if not profile:
        return jsonify({'error': 'Profile not found'}), 404

    results = []
    missing = 0
    valid_visual = {'lcd', 'ram', 'cpu', 'keyboard', 'pointing', 'touch'}

    for test_path in profile.tests:
        available = True
        reason = ''
        if test_path.lower().startswith('visual:'):
            v_type = test_path.split(':', 1)[1].lower()
            if v_type not in valid_visual:
                available = False
                reason = 'Unknown visual test type'
        elif any(keyword in test_path.lower() for keyword in valid_visual):
            # treat as visual by naming convention, assume available
            available = True
        else:
            try:
                mod_path, func_name = test_path.rsplit('.', 1)
                mod = importlib.import_module(mod_path)
                if not hasattr(mod, func_name):
                    available = False
                    reason = 'Function not found'
            except ModuleNotFoundError:
                available = False
                reason = 'Module not found'
            except Exception as ie:
                available = False
                reason = str(ie)
        if not available:
            missing += 1
        results.append({'path': test_path, 'available': available, 'reason': reason})

    status = 'ok' if missing == 0 else 'warning'

    return jsonify({
        'profile': profile_name,
        'status': status,
        'missing_count': missing,
        'tests': results
    }), 200


@app.route('/api/profiles/<string:profile_name>', methods=['GET'])
def get_profile_route(profile_name):
    profile = load_profile(profile_name)
    if profile:
        return jsonify(profile.to_dict())
    else:
        return (jsonify({'error': 'Profile not found'}), 404)

@app.route('/api/profiles', methods=['POST'])
def create_profile_route():
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    data = request.json
    required_fields = ['name', 'tests']
    for field in required_fields:
        if field not in data:
            return (jsonify({'error': f'Missing field: {field}'}), 400)
    profile_name = data.get('name')
    if not profile_name or not profile_name.strip():
        return (jsonify({'error': 'Profile name cannot be empty'}), 400)
    if profile_exists(profile_name):
        return (jsonify({'error': f"Profile '{profile_name}' already exists"}), 409)
    new_profile = Profile(name=profile_name, description=data.get('description', ''), tests=data.get('tests', []), device_type=data.get('device_type', 'Generic'), test_args=data.get('test_args', {}))
    if save_profile(new_profile):
        return (jsonify(new_profile.to_dict()), 201)
    else:
        return (jsonify({'error': 'Failed to save profile'}), 500)

@app.route('/api/profiles/<string:profile_name>', methods=['PUT'])
def update_profile_route(profile_name):
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    if not profile_exists(profile_name):
        return (jsonify({'error': 'Profile not found'}), 404)
    data = request.json
    if 'name' in data and data['name'] != profile_name:
        return (jsonify({'error': f"Cannot change profile name from '{profile_name}' to '{data['name']}'. Delete and recreate if necessary."}), 400)
    updated_profile = Profile(name=profile_name, description=data.get('description', load_profile(profile_name).description if load_profile(profile_name) else ''), tests=data.get('tests', load_profile(profile_name).tests if load_profile(profile_name) else []), device_type=data.get('device_type', load_profile(profile_name).device_type if load_profile(profile_name) else 'Generic'), test_args=data.get('test_args', load_profile(profile_name).test_args if load_profile(profile_name) else {}))
    if save_profile(updated_profile):
        return (jsonify(updated_profile.to_dict()), 200)
    else:
        return (jsonify({'error': 'Failed to update profile'}), 500)

@app.route('/api/profiles/<string:profile_name>', methods=['DELETE'])
def delete_profile_route(profile_name):
    if not profile_exists(profile_name):
        return (jsonify({'error': 'Profile not found'}), 404)
    if delete_profile(profile_name):
        return (jsonify({}), 204)
    else:
        return (jsonify({'error': 'Failed to delete profile'}), 500)

@app.route('/api/results/<string:asset_number>', methods=['GET'])
def list_asset_results_route(asset_number):
    if not os.path.isdir(RESULTS_DIR):
        return (jsonify({'error': 'Results directory not found on server'}), 404)
    consolidated_pattern = os.path.join(RESULTS_DIR, f'consolidated_nexus_results_{asset_number}.json')
    consolidated_files = [os.path.basename(f) for f in glob.glob(consolidated_pattern)]
    individual_pattern = os.path.join(RESULTS_DIR, f'nexus_result_{asset_number}_*.json')
    individual_files = [os.path.basename(f) for f in glob.glob(individual_pattern)]
    if not consolidated_files and (not individual_files):
        return (jsonify({'error': 'No results found for this asset number'}), 404)
    return jsonify({'asset_number': asset_number, 'consolidated_results': consolidated_files, 'individual_results': individual_files})

@app.route('/api/results/<string:asset_number>/<string:result_filename>', methods=['GET'])
def get_specific_result_route(asset_number, result_filename):
    if '..' in result_filename or result_filename.startswith('/'):
        return (jsonify({'error': 'Invalid filename'}), 400)
    file_path = os.path.join(RESULTS_DIR, result_filename)
    expected_consolidated_prefix = f'consolidated_nexus_results_{asset_number}.json'
    expected_individual_prefix = f'nexus_result_{asset_number}_'
    is_valid_consolidated = result_filename == expected_consolidated_prefix
    is_valid_individual = result_filename.startswith(expected_individual_prefix) and result_filename.endswith('.json')
    if not (is_valid_consolidated or is_valid_individual):
        return (jsonify({'error': 'Filename does not match asset number pattern or expected format'}), 400)
    abs_results_dir = os.path.abspath(RESULTS_DIR)
    abs_file_path = os.path.join(abs_results_dir, result_filename)
    if os.path.exists(abs_file_path) and os.path.isfile(abs_file_path):
        return send_from_directory(abs_results_dir, result_filename, as_attachment=False)
    else:
        return (jsonify({'error': 'Result file not found'}), 404)

@app.route('/api/device_conditions/<string:asset_number>', methods=['GET'])
def get_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return (jsonify({'error': 'Asset number is required'}), 400)
    conditions = load_device_conditions(asset_number)
    if conditions:
        return (jsonify(conditions), 200)
    else:
        return (jsonify({'message': 'No conditions found for this asset, or error loading.', 'default_structure_if_needed': DEFAULT_DEVICE_CONDITIONS_STRUCTURE}), 404)

@app.route('/api/device_conditions/<string:asset_number>', methods=['POST'])
def save_device_conditions_route(asset_number):
    if not asset_number or not asset_number.strip():
        return (jsonify({'error': 'Asset number is required'}), 400)
    if not request.json:
        return (jsonify({'error': 'Invalid input, JSON required'}), 400)
    conditions_data = request.json
    if save_device_conditions(conditions_data, asset_number):
        return (jsonify({'message': 'Device conditions saved successfully'}), 200)
    else:
        return (jsonify({'error': 'Failed to save device conditions'}), 500)


# --- On-demand result consolidation ---
@app.route('/api/results/consolidate/<string:asset_number>', methods=['POST'])
def consolidate_results_route(asset_number):
    """Force consolidation and inventory push for the given asset."""
    operator_id = request.json.get('operator_id', 'web_ui') if request.is_json else 'web_ui'
    res_mgr = ResultManager(log_callback=server_logger.info)
    consolidated_path = res_mgr.consolidate_results_for_asset(asset_number, operator_id)
    if consolidated_path:
        return (jsonify({'message': 'Consolidation completed', 'file': os.path.basename(consolidated_path)}), 200)
    else:
        return (jsonify({'error': 'Consolidation failed – check logs for details'}), 500)

# --- Drive Wiping Endpoints ---
# In-memory store for wipe progress and logs. Key: 'current_wipe', Value: dict of progress data
# This is a simplified approach. For production, consider a more robust solution.
wipe_status_data: dict[str, Any] = {
    "status": "idle", # idle, wiping, complete, error
    "progress": 0, # Overall progress 0-100
    "current_drive": "",
    "drive_progress": 0, # Progress for the current drive 0-100
    "message": "",
    "logs": [],
    "results": [] # List of dicts, one per drive
}
wipe_lock = threading.Lock()

@app.route('/api/drives', methods=['GET'])
def get_drives_route():
    """Returns a list of available drives with their information."""
    try:
        # Assuming get_detailed_drive_info is the function to call
        from agent.hardware.drive_info import get_detailed_drive_info
        drives = get_detailed_drive_info()
        return jsonify(drives)
    except ImportError:
        server_logger.error("Failed to import get_detailed_drive_info from agent.hardware.drive_info")
        return jsonify({'error': 'Drive information module not found on server'}), 500
    except Exception as e:
        server_logger.error(f"Error getting drive info: {e}", exc_info=True)
        return jsonify({'error': 'Failed to retrieve drive information', 'details': str(e)}), 500

@app.route('/api/wipe_methods', methods=['GET'])
def get_wipe_methods_route():
    """Returns a list of available wipe methods."""
    # Taken from agent.gui.drive_wipe_gui
    from agent.gui.drive_wipe_gui import WIPE_METHODS, WIPE_METHOD_DESCRIPTIONS
    methods = []
    for name, key in WIPE_METHODS.items():
        methods.append({
            "key": key,
            "name": name,
            "description": WIPE_METHOD_DESCRIPTIONS.get(name, "No description available.")
        })
    return jsonify(methods)

@app.route('/api/wipe_drives', methods=['POST'])
def wipe_drives_route():
    """Initiates the drive wiping process."""
    with wipe_lock:
        if wipe_status_data["status"] == "wiping":
            return jsonify({'error': 'A wipe operation is already in progress.'}), 409 # Conflict

        data = request.json
        if not data:
            return jsonify({'error': 'Invalid input, JSON required'}), 400

        drives_to_wipe = data.get('drives') # List of drive paths, e.g., ["/dev/sda", "/dev/sdb"]
        method_key = data.get('method_key') # e.g., "zero_fill"

        if not drives_to_wipe or not isinstance(drives_to_wipe, list):
            return jsonify({'error': 'Missing or invalid "drives" field (must be a list of drive paths)'}), 400
        if not method_key:
            return jsonify({'error': 'Missing "method_key" field'}), 400

        from agent.gui.drive_wipe_gui import WIPE_METHODS
        if method_key not in WIPE_METHODS.values():
            return jsonify({'error': f'Invalid wipe method_key: {method_key}'}), 400

        # Reset status for new wipe
        wipe_status_data.update({
            "status": "starting_wipe",
            "progress": 0,
            "current_drive": "",
            "drive_progress": 0,
            "message": "Initializing wipe...",
            "logs": ["Wipe process initiated."],
            "results": [],
            "selected_drives": drives_to_wipe,
            "selected_method": method_key
        })

        # Placeholder for starting the actual wipe process in a background thread
        # This will be implemented in the next step.
        # For now, we'll just log it and simulate completion.
        server_logger.info(f"Request to wipe drives: {drives_to_wipe} using method: {method_key}")

        # --- This is where the background thread for wiping will be started ---
        # Simulating immediate start for now, will be replaced by actual threaded call
        # from agent.tests.drive_wipe_test import process_wipe_queue # Placeholder
        # from agent.hardware.drive_info import get_detailed_drive_info # Placeholder

        # def _update_progress_api(current_drive_path, overall_progress_val, drive_progress_val, msg_val):
        #     with wipe_lock:
        #         wipe_status_data["current_drive"] = current_drive_path
        #         wipe_status_data["progress"] = overall_progress_val
        #         wipe_status_data["drive_progress"] = drive_progress_val
        #         wipe_status_data["message"] = msg_val
        #         wipe_status_data["logs"].append(f"PROGRESS: {current_drive_path} - {msg_val} ({drive_progress_val:.0f}%) | Overall: {overall_progress_val:.0f}%")

        # def _log_to_api(msg_val, level="info"):
        #     with wipe_lock:
        #         wipe_status_data["logs"].append(f"[{level.upper()}] {msg_val}")

        # def _finalize_drive_wipe_api(drive_path_val, result_data_val):
        #     with wipe_lock:
        #         wipe_status_data["results"].append(result_data_val)
        #         wipe_status_data["logs"].append(f"RESULT for {drive_path_val}: {result_data_val.get('status', '?')}")

        # def _all_wipes_completed_api():
        #     with wipe_lock:
        #         wipe_status_data["status"] = "complete"
        #         wipe_status_data["progress"] = 100
        #         wipe_status_data["message"] = "All wipes completed."
        #         wipe_status_data["logs"].append("All wipe operations finished.")
        #         server_logger.info("Background wipe process reported completion.")

        # try:
        #     all_drives_on_system = get_detailed_drive_info() # Get current drive info
        #     wipe_thread = threading.Thread(
        #         target=process_wipe_queue, # This needs to be adapted from drive_wipe_test
        #         args=(
        #             drives_to_wipe,
        #             method_key,
        #             all_drives_on_system, # Send full list for context if needed by process_wipe_queue
        #             _update_progress_api,
        #             _log_to_api,
        #             _finalize_drive_wipe_api,
        #             _all_wipes_completed_api,
        #         ),
        #         daemon=True
        #     )
        #     wipe_thread.start()
        #     wipe_status_data["status"] = "wiping" # Update status after thread starts
        #     server_logger.info(f"Background wipe thread started for drives: {drives_to_wipe}")
        # except Exception as e:
        #     server_logger.error(f"Failed to start wipe thread: {e}", exc_info=True)
        #     wipe_status_data["status"] = "error"
        #     wipe_status_data["message"] = f"Error starting wipe: {str(e)}"
        #     return jsonify({'error': 'Failed to start wipe process', 'details': str(e)}), 500
        # --- Start actual wipe process in a background thread ---
        try:
            from agent.tests.drive_wipe_test import WIPE_METHOD_MAP, calculate_boundary_hashes, ZERO_MB_SHA256, _get_smart_status # Import necessary components
            from agent.hardware.drive_info import get_detailed_drive_info # To get full drive info
            import datetime # Ensure datetime is imported

            # API-specific callbacks that update wipe_status_data
            def _update_progress_api(current_drive_path, overall_progress_val, drive_progress_val, msg_val):
                with wipe_lock:
                    if wipe_status_data["status"] == "cancelling": # Check if cancellation is requested
                        server_logger.info(f"Wipe cancellation in progress for {current_drive_path}. Progress update skipped.")
                        return True # Indicate to caller that cancellation is active

                    wipe_status_data["current_drive"] = current_drive_path
                    wipe_status_data["progress"] = overall_progress_val
                    wipe_status_data["drive_progress"] = drive_progress_val
                    wipe_status_data["message"] = msg_val
                    log_entry = f"PROGRESS: {current_drive_path} - {msg_val} ({drive_progress_val:.0f}%) | Overall: {overall_progress_val:.0f}%"
                    wipe_status_data["logs"].append(log_entry)
                    server_logger.debug(log_entry)
                    return False # Indicate to caller to continue

            def _log_to_api(msg_val, level="info"):
                with wipe_lock:
                    log_entry = f"[{level.upper()}] {msg_val}"
                    wipe_status_data["logs"].append(log_entry)
                    if level == "error":
                        server_logger.error(log_entry)
                    elif level == "warning":
                        server_logger.warning(log_entry)
                    else:
                        server_logger.info(log_entry)

            def _finalize_drive_wipe_api(drive_path_val, result_data_val):
                with wipe_lock:
                    wipe_status_data["results"].append(result_data_val)
                    log_entry = f"RESULT for {drive_path_val}: Status {result_data_val.get('status', '?')}, Verification: {result_data_val.get('verification', '-')}"
                    wipe_status_data["logs"].append(log_entry)
                    server_logger.info(log_entry)

            def _all_wipes_completed_api():
                with wipe_lock:
                    if wipe_status_data["status"] == "cancelling":
                        wipe_status_data["status"] = "cancelled"
                        wipe_status_data["message"] = "Wipe operation cancelled."
                        wipe_status_data["logs"].append("Wipe operation was cancelled by user.")
                    elif wipe_status_data["status"] != "error":
                        wipe_status_data["status"] = "complete"
                        wipe_status_data["message"] = "All wipes completed."
                        wipe_status_data["logs"].append("All wipe operations finished.")
                    if wipe_status_data["status"] != "error":
                         wipe_status_data["progress"] = 100
                    server_logger.info(f"Background wipe process finished with status: {wipe_status_data['status']}.")

            # Adapted perform_single_wipe_threaded for API
            def perform_single_wipe_threaded_api(
                drive_path: str,
                method_name_key: str,
                progress_cb, log_cb, result_cb_final):

                with wipe_lock:
                    if wipe_status_data["status"] == "cancelling":
                        log_cb(f"Wipe for {drive_path} skipped due to cancellation request.", "warning")
                        result_cb_final(drive_path, {
                            "device_path": drive_path, "method": method_name_key, "status": "cancelled",
                            "details": "Operation cancelled by user before starting this drive.",
                            "errors": "", "verification": "not_applicable", "hashes": {}, "smart_status_pre_wipe": "N/A",
                            "start_time": datetime.datetime.utcnow().isoformat(), "end_time": datetime.datetime.utcnow().isoformat()
                        })
                        return True

                start_ts = datetime.datetime.utcnow().isoformat()
                log_cb(f"Performing S.M.A.R.T. check for {drive_path}...", "info")
                smart_status = _get_smart_status(drive_path, log_cb)
                log_cb(f"S.M.A.R.T. status for {drive_path}: {smart_status}", "info")

                log_cb(f"Starting wipe for {drive_path} using method: {method_name_key}", "info")
                pre_hash = calculate_boundary_hashes(drive_path, "pre")

                runner = WIPE_METHOD_MAP.get(method_name_key)
                if not runner:
                    log_cb(f"Invalid method key '{method_name_key}' for drive {drive_path}. Skipping.", "error")
                    res = {"status": "fail", "details": f"Invalid method key: {method_name_key}", "errors": "Method not found"}
                else:
                    try:
                        def cancellable_progress_cb(current_drive, overall_prog, drive_prog, msg):
                            if progress_cb(current_drive, overall_prog, drive_prog, msg):
                                raise InterruptedError("Wipe cancelled by user")
                            return False

                        # Make sure the runner function (e.g. _run_zero_fill) is adapted to use this cancellable_progress_cb
                        # and to check its return value or catch InterruptedError.
                        # This change is NOT made here but assumed to be handled if deep cancellation is needed.
                        # For now, _dd_fill and _run_shred in drive_wipe_test.py are not adapted for this InterruptedError.
                        # Cancellation will primarily work BETWEEN drives or if the progress_cb is checked by the runner.
                        res = runner(drive_path, method_name_key, cancellable_progress_cb, log_cb)
                    except InterruptedError:
                        log_cb(f"Wipe for {drive_path} was interrupted by cancellation.", "warning")
                        res = {"status": "cancelled", "details": "Operation cancelled by user during wipe.", "errors": ""}
                    except Exception as e:
                        log_cb(f"Error during wipe execution for {drive_path} with method {method_name_key}: {e}", "error")
                        res = {"status": "fail", "details": f"Runtime error during wipe: {str(e)}", "errors": str(e)}

                post_wipe_sample_sha256 = res.pop("post_fill_sample_sha256", None)
                post_hash = calculate_boundary_hashes(drive_path, "post")
                if post_wipe_sample_sha256:
                    post_hash["post_wipe_sample_sha256"] = post_wipe_sample_sha256

                verification = "not_applicable"
                if res["status"] == "pass":
                    if method_name_key == "zero_fill" or method_name_key == "three_pass_fill":
                        if post_hash.get("post_first_1mb_sha256") == ZERO_MB_SHA256 and \
                           post_hash.get("post_last_1mb_sha256") == ZERO_MB_SHA256:
                            verification = "pass: first and last 1MB are zeroed"
                        else:
                            verification = "fail: first and/or last 1MB are not zeroed"
                    elif method_name_key == "random_fill":
                        if post_wipe_sample_sha256:
                            if post_wipe_sample_sha256 == post_hash.get("post_first_1mb_sha256"):
                                 verification = "pass: command completed, first 1MB hash matches sample"
                            else:
                                 verification = "warn: command completed, but first 1MB hash changed between sample and final check"
                            verification += " (random data not directly verifiable)"
                        else:
                            verification = "warn: command completed, but sample hash of random data was not obtained"
                    elif method_name_key in ["blkdiscard", "ata_secure_erase", "nvme_sanitize", "dodshort", "gutmann"]:
                         verification = f"pass: {method_name_key} command reported success"
                elif res["status"] == "cancelled":
                    verification = "not_applicable (cancelled)"
                else:
                    verification = f"fail: wipe command for {method_name_key} failed"

                log_cb(f"Verification result for {drive_path} ({method_name_key}): {verification}", "info")
                if res.get("errors"):
                    log_cb(f"Errors encountered for {drive_path} ({method_name_key}): {res.get('errors')}", "error")

                final_result_data = {
                    "device_path": drive_path, "method": method_name_key, "status": res["status"],
                    "details": res.get("details"), "errors": res.get("errors"),
                    "verification": verification, "hashes": {**pre_hash, **post_hash},
                    "smart_status_pre_wipe": smart_status,
                    "start_time": start_ts, "end_time": datetime.datetime.utcnow().isoformat()
                }
                result_cb_final(drive_path, final_result_data)
                return False

            # Adapted process_wipe_queue for API
            def process_wipe_queue_api(
                drives_to_wipe_list: List[str],
                method_key_val: str,
                progress_cb_overall, log_cb_overall, result_cb_drive_final, all_done_cb_overall
            ):
                initial_status_set = False
                try:
                    with wipe_lock:
                        if wipe_status_data["status"] == "cancelling":
                             log_cb_overall("Wipe queue processing cancelled before start.", "warning")
                             all_done_cb_overall()
                             return
                        wipe_status_data["status"] = "wiping"
                        wipe_status_data["message"] = "Wipe in progress..."
                        initial_status_set = True

                    total_drives = len(drives_to_wipe_list)
                    for idx, drive_path_item in enumerate(drives_to_wipe_list, 1):
                        def single_drive_progress_adapter(current_drive_path_ignored, overall_ignored, single_drive_pct, msg_val):
                            overall_progress_val = ((idx - 1) / total_drives * 100) + (single_drive_pct / total_drives)
                            return progress_cb_overall(drive_path_item, overall_progress_val, single_drive_pct, msg_val)

                        if perform_single_wipe_threaded_api(
                            drive_path_item, method_key_val,
                            single_drive_progress_adapter, log_cb_overall, result_cb_drive_final
                        ):
                            log_cb_overall(f"Stopping wipe queue due to cancellation during/before drive {drive_path_item}.", "warning")
                            break

                    all_done_cb_overall()
                except Exception as e:
                    server_logger.error(f"Unhandled exception in wipe thread: {e}", exc_info=True)
                    with wipe_lock:
                        wipe_status_data["status"] = "error"
                        wipe_status_data["message"] = f"Critical error during wipe: {str(e)}"
                        wipe_status_data["logs"].append(f"[CRITICAL ERROR] {str(e)}")
                    if initial_status_set:
                         all_done_cb_overall()


            wipe_thread = threading.Thread(
                target=process_wipe_queue_api,
                args=(
                    drives_to_wipe,
                    method_key,
                    _update_progress_api,
                    _log_to_api,
                    _finalize_drive_wipe_api,
                    _all_wipes_completed_api,
                ),
                daemon=True
            )
            wipe_thread.start()
            server_logger.info(f"Background wipe thread initiated for drives: {drives_to_wipe} with method {method_key}")
            return jsonify({'message': 'Drive wipe process started. Poll /api/wipe_progress for status.'}), 202

        except ImportError as e:
            server_logger.error(f"ImportError when trying to start wipe: {e}", exc_info=True)
            with wipe_lock:
                wipe_status_data["status"] = "error"
                wipe_status_data["message"] = f"Server error: Missing components for wiping - {str(e)}"
            return jsonify({'error': 'Server configuration error, cannot start wipe', 'details': str(e)}), 500
        except Exception as e:
            server_logger.error(f"Failed to start wipe thread: {e}", exc_info=True)
            with wipe_lock:
                wipe_status_data["status"] = "error"
                wipe_status_data["message"] = f"Error starting wipe: {str(e)}"
            return jsonify({'error': 'Failed to start wipe process', 'details': str(e)}), 500
        # --- End of actual wipe process ---


@app.route('/api/wipe_progress', methods=['GET'])
def get_wipe_progress_route():
    """Returns the progress of the current wipe operation."""
    with wipe_lock:
        # Make a copy to avoid issues if the data is modified while sending
        data_to_send = dict(wipe_status_data)
    return jsonify(data_to_send)

@app.route('/api/wipe_drives/cancel', methods=['POST'])
def cancel_wipe_drives_route():
    """Attempts to cancel the ongoing wipe operation."""
    # This is a placeholder. Actual cancellation is complex and depends on the wipe method.
    # For methods like dd, it might involve killing the process.
    # For hardware commands (ATA Secure Erase, NVMe Sanitize), cancellation might not be possible
    # or could leave the drive in an indeterminate state.
    with wipe_lock:
        if wipe_status_data["status"] != "wiping":
            return jsonify({'error': 'No wipe operation is currently in progress to cancel.'}), 400

        # Basic cancellation: set a flag. The background thread needs to check this flag.
        wipe_status_data["status"] = "cancelling"
        wipe_status_data["message"] = "Cancellation request received. Attempting to stop..."
        wipe_status_data["logs"].append("WARN: Cancellation requested. Actual stop depends on current operation.")

        # TODO: Implement logic in the background wipe thread to check for this 'cancelling' status
        # and attempt to halt operations safely. This is non-trivial.

        server_logger.warning("Cancellation requested for wipe operation. Actual stop mechanism not yet fully implemented.")
    return jsonify({'message': 'Wipe cancellation requested. Monitor progress for final status.'}), 200

# --- End Drive Wiping Endpoints ---
