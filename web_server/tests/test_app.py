import pytest
import json
import os
import sys

# Add project root to sys.path to allow imports from 'agent' and 'web_server'
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

# Import the Flask app instance
from web_server.app import app as flask_app

@pytest.fixture
def app():
    flask_app.config.update({
        "TESTING": True,
    })
    yield flask_app

@pytest.fixture
def client(app):
    return app.test_client()

@pytest.fixture
def runner(app):
    return app.test_cli_runner()

# --- Tests for /api/system_info ---
def test_get_system_info_success(client, mocker):
    mock_data = {
        "hostname": "test_host",
        "os_version": "TestOS 1.0",
        "cpu": "Test CPU @ 3.0GHz",
        "memory": "16 GB",
        "serial_number": "TESTSN123"
    }
    mocker.patch("web_server.app.get_system_info", return_value=mock_data)

    response = client.get("/api/system_info")

    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["hostname"] == "test_host"
    assert data["serial_number"] == "TESTSN123"
    assert "error" not in data

def test_get_system_info_error(client, mocker):
    mocker.patch("web_server.app.get_system_info", side_effect=Exception("Mocked hardware error"))

    response = client.get("/api/system_info")

    assert response.status_code == 500
    data = json.loads(response.data)
    assert "error" in data
    assert data["error"] == "Failed to retrieve system information"
    assert "Mocked hardware error" in data.get("details", "")

# --- Placeholder for Profile API Tests ---
PROFILE_DIR_MOCK = "web_server/tests/mock_profiles"

def setup_mock_profile_dir():
    if not os.path.exists(PROFILE_DIR_MOCK):
        os.makedirs(PROFILE_DIR_MOCK)
    sample_profile_1 = {
        "name": "TestProfile1", "description": "Desc1", "device_type": "Laptop",
        "tests": ["agent.tests.battery_test.run_battery_test"], "test_args": {}
    }
    with open(os.path.join(PROFILE_DIR_MOCK, "TestProfile1.json"), "w") as f:
        json.dump(sample_profile_1, f)

def teardown_mock_profile_dir():
    if os.path.exists(PROFILE_DIR_MOCK):
        for f_name in os.listdir(PROFILE_DIR_MOCK):
            os.remove(os.path.join(PROFILE_DIR_MOCK, f_name))
        os.rmdir(PROFILE_DIR_MOCK)

# Ensure agent.tests.profiles can be imported and Profile class exists for mocking
# Create dummy if it doesn't exist, for script robustness.
AGENT_TESTS_PROFILES_PATH = os.path.join(project_root, "agent", "tests", "profiles.py")
AGENT_TESTS_DIR = os.path.dirname(AGENT_TESTS_PROFILES_PATH)

if not os.path.exists(AGENT_TESTS_DIR):
    os.makedirs(AGENT_TESTS_DIR)
    with open(os.path.join(AGENT_TESTS_DIR, "__init__.py"), "w") as f: f.write("") # Make it a package

if not os.path.exists(AGENT_TESTS_PROFILES_PATH):
    with open(AGENT_TESTS_PROFILES_PATH, "w") as f_dummy_prof:
        f_dummy_prof.write("""
# Dummy agent.tests.profiles
class Profile:
    def __init__(self, name, description="", tests=None, device_type="Generic", test_args=None):
        self.name = name
        self.description = description
        self.tests = tests if tests is not None else []
        self.device_type = device_type
        self.test_args = test_args if test_args is not None else {}
    def to_dict(self): return self.__dict__

def get_all_profiles(): return []
def load_profile(name): return None
# Add other functions if directly called by app.py and need mocking targets
""")
        print(f"Created dummy {AGENT_TESTS_PROFILES_PATH} for test_app.py.")


def test_get_all_profiles_empty(client, mocker):
    mocker.patch("web_server.app.get_all_profiles", return_value=[])
    response = client.get("/api/profiles")
    assert response.status_code == 200
    assert json.loads(response.data) == []

def test_get_all_profiles_with_data(client, mocker):
    # This import needs to succeed. The dummy file creation above helps.
    from agent.tests.profiles import Profile
    mock_profiles_data = [
        Profile(name="ProfileA", tests=["test.a"]),
        Profile(name="ProfileB", tests=["test.b"])
    ]
    mocker.patch("web_server.app.get_all_profiles", return_value=mock_profiles_data)
    response = client.get("/api/profiles")
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data) == 2
    assert data[0]["name"] == "ProfileA"
    assert data[1]["name"] == "ProfileB"


# --- Tests for GET /api/profiles/<profile_name> ---
def test_get_specific_profile_success(client, mocker):
    from agent.tests.profiles import Profile
    mock_profile = Profile(name="SpecificProfile", description="A specific test profile", tests=["test.x.y"])
    mocker.patch("web_server.app.load_profile", return_value=mock_profile)

    response = client.get("/api/profiles/SpecificProfile")
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["name"] == "SpecificProfile"
    assert data["description"] == "A specific test profile"

def test_get_specific_profile_not_found(client, mocker):
    mocker.patch("web_server.app.load_profile", return_value=None)
    response = client.get("/api/profiles/NonExistentProfile")
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "error" in data
    assert data["error"] == "Profile not found"

# --- Tests for POST /api/profiles ---
def test_create_profile_success(client, mocker):
    # from agent.tests.profiles import Profile # Already imported in previous test
    mocker.patch("web_server.app.profile_exists", return_value=False)
    mocker.patch("web_server.app.save_profile", return_value=True)

    new_profile_data = {
        "name": "NewProfile",
        "description": "A new profile",
        "tests": ["test.new.a", "test.new.b"],
        "device_type": "Desktop",
        "test_args": {}
    }
    response = client.post("/api/profiles", json=new_profile_data)

    assert response.status_code == 201
    data = json.loads(response.data)
    assert data["name"] == "NewProfile"
    assert data["description"] == "A new profile"

def test_create_profile_missing_fields(client):
    response = client.post("/api/profiles", json={"description": "Incomplete profile"})
    assert response.status_code == 400
    data = json.loads(response.data)
    assert "error" in data
    # The exact message depends on validation order in app.py
    assert "Missing field" in data["error"]

def test_create_profile_already_exists(client, mocker):
    mocker.patch("web_server.app.profile_exists", return_value=True)
    new_profile_data = {"name": "ExistingProfile", "tests": ["test.c"]}
    response = client.post("/api/profiles", json=new_profile_data)
    assert response.status_code == 409
    data = json.loads(response.data)
    assert "error" in data
    assert "already exists" in data["error"]

# --- Tests for PUT /api/profiles/<profile_name> ---
def test_update_profile_success(client, mocker):
    # from agent.tests.profiles import Profile # Assumed available
    mocker.patch("web_server.app.profile_exists", return_value=True)
    mocker.patch("web_server.app.save_profile", return_value=True)

    updated_data = {"name": "MyProfile", "description": "Updated description", "tests": ["test.updated"]}
    response = client.put("/api/profiles/MyProfile", json=updated_data)

    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["description"] == "Updated description"

def test_update_profile_not_found(client, mocker):
    mocker.patch("web_server.app.profile_exists", return_value=False)
    updated_data = {"name": "NotFoundProfile", "description": "Data", "tests": []}
    response = client.put("/api/profiles/NotFoundProfile", json=updated_data)
    assert response.status_code == 404

# --- Tests for DELETE /api/profiles/<profile_name> ---
def test_delete_profile_success(client, mocker):
    mocker.patch("web_server.app.profile_exists", return_value=True)
    mocker.patch("web_server.app.delete_profile", return_value=True)

    response = client.delete("/api/profiles/DeletableProfile")
    assert response.status_code == 204

def test_delete_profile_not_found(client, mocker):
    mocker.patch("web_server.app.profile_exists", return_value=False)
    response = client.delete("/api/profiles/NonExistentToDelete")
    assert response.status_code == 404

def test_delete_profile_failure(client, mocker):
    mocker.patch("web_server.app.profile_exists", return_value=True)
    mocker.patch("web_server.app.delete_profile", return_value=False)
    response = client.delete("/api/profiles/FailedDeleteProfile")
    assert response.status_code == 500



# --- Tests for Results API ---
# /api/results/<asset_number>
def test_list_asset_results_success(client, mocker):
    asset_num = "Asset123"
    # Corrected mock paths for glob.glob to match how it's called in app.py (os.path.join)
    # Assume RESULTS_DIR is a known constant in app.py, or mock it too.
    # For now, we mock glob.glob directly.
    # The app uses: os.path.join(RESULTS_DIR, f"nexus_consolidated_{asset_number}_*.json")
    # and os.path.join(RESULTS_DIR, f"nexus_result_{asset_number}_*.json")
    # We'll mock glob.glob to return full paths, then os.path.basename is used in app.py

    # Mock the RESULTS_DIR constant in web_server.app if it's defined there and used by glob
    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir")

    mock_consolidated_full_paths = [os.path.join("mocked_results_dir", f"consolidated_nexus_results_{asset_num}.json")]
    mock_individual_full_paths = [os.path.join("mocked_results_dir", f"nexus_result_{asset_num}_testA_timestamp2.json")]

    mocker.patch("web_server.app.os.path.isdir", return_value=True)
    mocker.patch("web_server.app.glob.glob", side_effect=[mock_consolidated_full_paths, mock_individual_full_paths])

    response = client.get(f"/api/results/{asset_num}")
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["asset_number"] == asset_num
    assert data["consolidated_results"] == [os.path.basename(p) for p in mock_consolidated_full_paths]
    assert data["individual_results"] == [os.path.basename(p) for p in mock_individual_full_paths]

def test_list_asset_results_no_results_found(client, mocker):
    asset_num = "AssetNoResults"
    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir")
    mocker.patch("web_server.app.os.path.isdir", return_value=True)
    mocker.patch("web_server.app.glob.glob", side_effect=[[], []])

    response = client.get(f"/api/results/{asset_num}")
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "error" in data
    assert "No results found" in data["error"]

def test_list_asset_results_dir_not_found(client, mocker):
    asset_num = "AssetDirError"
    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir") # Still need this if os.path.join uses it
    mocker.patch("web_server.app.os.path.isdir", return_value=False)

    response = client.get(f"/api/results/{asset_num}")
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "error" in data
    assert "Results directory not found" in data["error"]

# /api/results/<asset_number>/<result_filename>
def test_get_specific_result_success(client, mocker):
    asset_num = "AssetDownload"
    # Filename as it would appear in the URL (basename)
    filename_base = f"consolidated_nexus_results_{asset_num}.json"

    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir_specific")
    # Mock os.path.abspath to control the path send_from_directory receives for directory
    # This ensures we can assert the call correctly.
    mocker.patch("web_server.app.os.path.abspath", return_value="/abs/path/to/mocked_results_dir_specific")

    mock_send_from_directory = mocker.patch("web_server.app.send_from_directory", return_value="File content here")
    mocker.patch("web_server.app.os.path.exists", return_value=True)
    mocker.patch("web_server.app.os.path.isfile", return_value=True)

    response = client.get(f"/api/results/{asset_num}/{filename_base}")
    assert response.status_code == 200
    assert response.data == b"File content here"
    mock_send_from_directory.assert_called_once_with("/abs/path/to/mocked_results_dir_specific", filename_base, as_attachment=False)


def test_get_specific_result_file_not_found(client, mocker):
    asset_num = "AssetNoFile"
    filename = f"nexus_result_{asset_num}_nonexistent.json"
    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir")
    mocker.patch("web_server.app.os.path.abspath", return_value="/abs/path/to/mocked_results_dir")
    mocker.patch("web_server.app.os.path.exists", return_value=False) # File does not exist

    response = client.get(f"/api/results/{asset_num}/{filename}")
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "Result file not found" in data["error"]

def test_get_specific_result_invalid_filename_pattern(client, mocker):
    asset_num = "AssetPattern"
    filename = "some_other_file.json"
    mocker.patch("web_server.app.RESULTS_DIR", "mocked_results_dir")

    response = client.get(f"/api/results/{asset_num}/{filename}")
    assert response.status_code == 400
    data = json.loads(response.data)
    assert "Filename does not match asset number pattern" in data["error"]

# --- Tests for Device Conditions API ---
def test_get_device_conditions_success(client, mocker):
    asset_num = "CondAsset1"
    mock_conditions = {"grade": "A", "case_condition": "Good"}
    mocker.patch("web_server.app.load_device_conditions", return_value=mock_conditions)

    response = client.get(f"/api/device_conditions/{asset_num}")
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data["grade"] == "A"

def test_get_device_conditions_not_found(client, mocker):
    asset_num = "CondAssetNotFound"
    mocker.patch("web_server.app.load_device_conditions", return_value={})
    mocker.patch("web_server.app.DEFAULT_DEVICE_CONDITIONS_STRUCTURE", {"grade": "N/A"})

    response = client.get(f"/api/device_conditions/{asset_num}")
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "No conditions found" in data["message"]
    assert data["default_structure_if_needed"]["grade"] == "N/A"

def test_save_device_conditions_success(client, mocker):
    asset_num = "CondAssetSave"
    conditions_payload = {"grade": "B", "screen_condition": "Scratched"}
    mocker.patch("web_server.app.save_device_conditions", return_value=True)

    response = client.post(f"/api/device_conditions/{asset_num}", json=conditions_payload)
    assert response.status_code == 200
    data = json.loads(response.data)
    assert "Device conditions saved successfully" in data["message"]

def test_save_device_conditions_failure(client, mocker):
    asset_num = "CondAssetSaveFail"
    conditions_payload = {"grade": "C"}
    mocker.patch("web_server.app.save_device_conditions", return_value=False)

    response = client.post(f"/api/device_conditions/{asset_num}", json=conditions_payload)
    assert response.status_code == 500
    data = json.loads(response.data)
    assert "Failed to save device conditions" in data["error"]

def test_save_device_conditions_no_json(client):
    asset_num = "CondAssetNoJson"
    response = client.post(f"/api/device_conditions/{asset_num}", data="not json", content_type="text/plain") # Specify content type
    assert response.status_code == 415 # Flask typically returns 415 for unsupported media type if request.json is accessed
    # For a 415, the response body might not be JSON or might be a generic Werkzeug/Flask error page.
    # If it is JSON and contains an error message, that's good, but not strictly required for a 415.
    # The main point is that the server rejected the request due to the content type.
    # We can check if the response data is loadable as JSON.
    try:
        data = json.loads(response.data)
        assert "error" in data or "message" in data # Check for some indication of error if JSON is returned
    except json.JSONDecodeError:
        # This is acceptable for a 415 if the response is not JSON (e.g., HTML error page)
        pass

# --- Tests for Run Tests API ---
def test_run_tests_success(client, mocker):
    from agent.tests.profiles import Profile
    mock_profile = Profile(name="HeadlessProfile", tests=["agent.tests.battery_test.run_battery_test"])
    mocker.patch("web_server.app.load_profile", return_value=mock_profile)

    mock_orchestrator_instance = mocker.Mock()
    mock_orchestrator_instance.execute_tests = mocker.Mock()

    mock_TestOrchestrator_class = mocker.patch("web_server.app.TestOrchestrator", return_value=mock_orchestrator_instance)
    mocker.patch("web_server.app.ResultManager") # Mock ResultManager instantiation

    payload = {"asset_number": "AssetRun1", "operator_id": "Op1", "profile_name": "HeadlessProfile"}
    response = client.post("/api/run_tests", json=payload)

    assert response.status_code == 200
    data = json.loads(response.data)
    assert "Test execution for profile 'HeadlessProfile' on asset 'AssetRun1' initiated." in data["message"]

    mock_TestOrchestrator_class.assert_called_once()
    mock_orchestrator_instance.execute_tests.assert_called_once_with(headless_mode=True)

def test_run_tests_profile_not_found(client, mocker):
    mocker.patch("web_server.app.load_profile", return_value=None)
    payload = {"asset_number": "AssetRun2", "operator_id": "Op2", "profile_name": "MissingProfile"}
    response = client.post("/api/run_tests", json=payload)
    assert response.status_code == 404
    data = json.loads(response.data)
    assert "Profile 'MissingProfile' not found" in data["error"]

def test_run_tests_missing_payload_fields(client):
    response = client.post("/api/run_tests", json={"asset_number": "AssetRun3"})
    assert response.status_code == 400
    data = json.loads(response.data)
    assert "Missing required fields" in data["error"]


# --- Drive Wipe API Tests ---

# Mock app.wipe_status_data for consistent testing environment
# We need to patch it where it's defined (web_server.app)
@pytest.fixture(autouse=True)
def mock_wipe_status_global(mocker):
    # Initial clean state for wipe_status_data for each test
    initial_wipe_status = {
        "status": "idle", "progress": 0, "current_drive": "", "drive_progress": 0,
        "message": "", "logs": [], "results": [],
        "selected_drives": [], "selected_method": ""
    }
    # Patch the actual wipe_status_data dictionary in the app module
    mocker.patch.dict('web_server.app.wipe_status_data', initial_wipe_status, clear=True)


def test_get_drives_success(client, mocker):
    mock_drives_data = [
        {"path": "/dev/sda", "model": "TestModel1", "size_gb": 120, "type": "SSD", "serial": "SN123", "mountpoints": []},
        {"path": "/dev/sdb", "model": "TestModel2", "size_gb": 500, "type": "HDD", "serial": "SN456", "mountpoints": ["/mnt/data"]},
    ]
    mocker.patch("agent.hardware.drive_info.get_detailed_drive_info", return_value=mock_drives_data)
    response = client.get('/api/drives')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert len(data) == 2
    assert data[0]['path'] == '/dev/sda'

def test_get_drives_backend_error(client, mocker):
    mocker.patch("agent.hardware.drive_info.get_detailed_drive_info", side_effect=Exception("Drive info backend failure"))
    response = client.get('/api/drives')
    assert response.status_code == 500
    data = json.loads(response.data)
    assert data['error'] == 'Failed to retrieve drive information'
    assert "Drive info backend failure" in data['details']

def test_get_drives_module_import_error(client, mocker):
    # Simulate ImportError for the drive_info module itself within the route
    mocker.patch("agent.hardware.drive_info.get_detailed_drive_info", side_effect=ImportError("No module named agent.hardware.drive_info"))
    response = client.get('/api/drives')
    assert response.status_code == 500
    data = json.loads(response.data)
    assert data['error'] == 'Drive information module not found on server'


def test_get_wipe_methods(client):
    response = client.get('/api/wipe_methods')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert isinstance(data, list)
    assert len(data) > 0 # Expecting some methods
    # Check structure of the first method
    first_method = data[0]
    assert "key" in first_method
    assert "name" in first_method
    assert "description" in first_method
    # Check for a known method key (e.g., zero_fill from WIPE_METHODS)
    assert any(m['key'] == 'zero_fill' for m in data)


def test_wipe_drives_start_success(client, mocker):
    # Mock dependencies for starting a wipe
    mocker.patch("agent.hardware.drive_info.get_detailed_drive_info", return_value=[{"path": "/dev/sda"}]) # Minimal drive info
    mock_thread_start = mocker.patch("web_server.app.threading.Thread.start") # Mock the start method

    payload = {"drives": ["/dev/sda"], "method_key": "zero_fill"}
    response = client.post('/api/wipe_drives', json=payload)

    assert response.status_code == 202
    data = json.loads(response.data)
    assert data['message'] == 'Drive wipe process started. Poll /api/wipe_progress for status.'
    mock_thread_start.assert_called_once() # Check that thread.start() was called

    # Check that wipe_status_data was updated (via the mock_wipe_status_global fixture's patched dict)
    from web_server.app import wipe_status_data
    assert wipe_status_data['selected_drives'] == ["/dev/sda"]
    assert wipe_status_data['selected_method'] == "zero_fill"
    # The status is set to "starting_wipe" initially by the route,
    # then the thread (if it runs) sets it to "wiping". Here we check the initial set.
    assert wipe_status_data['status'] == "starting_wipe"


def test_wipe_drives_invalid_payloads(client):
    # Missing 'drives'
    response = client.post('/api/wipe_drives', json={"method_key": "zero_fill"})
    assert response.status_code == 400
    assert "Missing or invalid \"drives\" field" in response.get_json()['error']

    # 'drives' not a list
    response = client.post('/api/wipe_drives', json={"drives": "/dev/sda", "method_key": "zero_fill"})
    assert response.status_code == 400
    assert "Missing or invalid \"drives\" field" in response.get_json()['error']

    # Missing 'method_key'
    response = client.post('/api/wipe_drives', json={"drives": ["/dev/sda"]})
    assert response.status_code == 400
    assert "Missing \"method_key\" field" in response.get_json()['error']

    # Invalid 'method_key'
    response = client.post('/api/wipe_drives', json={"drives": ["/dev/sda"], "method_key": "non_existent_method"})
    assert response.status_code == 400
    assert "Invalid wipe method_key" in response.get_json()['error']

def test_wipe_drives_already_in_progress(client, mocker):
    # Use the patched wipe_status_data via mock_wipe_status_global
    from web_server.app import wipe_status_data
    wipe_status_data['status'] = 'wiping' # Simulate ongoing wipe

    payload = {"drives": ["/dev/sdb"], "method_key": "random_fill"}
    response = client.post('/api/wipe_drives', json=payload)
    assert response.status_code == 409 # Conflict
    data = json.loads(response.data)
    assert data['error'] == 'A wipe operation is already in progress.'
    # mock_wipe_status_global will reset wipe_status_data for the next test

def test_get_wipe_progress(client, mocker):
    from web_server.app import wipe_status_data
    # Modify the patched wipe_status_data for this test
    wipe_status_data['status'] = 'wiping'
    wipe_status_data['progress'] = 60
    wipe_status_data['current_drive'] = '/dev/sdb'
    wipe_status_data['logs'] = ["Log 1", "Log 2 progress..."]

    response = client.get('/api/wipe_progress')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['status'] == 'wiping'
    assert data['progress'] == 60
    assert data['current_drive'] == '/dev/sdb'
    assert data['logs'] == ["Log 1", "Log 2 progress..."]

def test_cancel_wipe_drives_when_wiping(client, mocker):
    from web_server.app import wipe_status_data
    wipe_status_data['status'] = 'wiping' # Simulate ongoing wipe

    response = client.post('/api/wipe_drives/cancel')
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['message'] == 'Wipe cancellation requested. Monitor progress for final status.'
    assert wipe_status_data['status'] == 'cancelling' # Check status update

def test_cancel_wipe_drives_when_not_wiping(client, mocker):
    from web_server.app import wipe_status_data
    wipe_status_data['status'] = 'idle' # Ensure not wiping

    response = client.post('/api/wipe_drives/cancel')
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['error'] == 'No wipe operation is currently in progress to cancel.'
    assert wipe_status_data['status'] == 'idle' # Status should remain unchanged


# TODO: More detailed tests for the background wipe process itself.
# This would involve mocking the actual external commands (dd, hdparm, etc.)
# and testing the internal logic of the adapted `perform_single_wipe_threaded_api`
# and `process_wipe_queue_api` more directly, especially their callback handling
# and state updates to `wipe_status_data`. This is complex because these functions
# are defined within the `wipe_drives_route` or need to be refactored out
# for easier unit testing.

# --- End Drive Wipe API Tests ---
