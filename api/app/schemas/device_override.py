from __future__ import annotations
from typing import Optional
from pydantic import BaseModel, Field

class DeviceOverrideIn(BaseModel):
    mac: str = Field(description="MAC address in any common format; will be normalized")
    profile_id: Optional[int] = None
    kernel_url: Optional[str] = None
    initrd_url: Optional[str] = None
    kernel_args: Optional[str] = None

class DeviceOverrideOut(BaseModel):
    id: int
    mac: str  # formatted with colons for output
    profile_id: Optional[int]
    kernel_url: Optional[str]
    initrd_url: Optional[str]
    kernel_args: Optional[str]

    class Config:
        orm_mode = True
