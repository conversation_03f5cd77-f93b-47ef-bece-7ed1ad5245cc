from __future__ import annotations
from typing import Any, Dict, List
import json
from pydantic import BaseModel, Field, field_validator


class CrucibleProfilePayload(BaseModel):
    """Crucible-compatible profile payload structure.

    Expected keys:
    - name: str
    - description: str
    - device_type: str
    - tests: List[str]
    - test_args: Dict[str, Dict[str, Any]]
    """

    name: str = Field(default="Unnamed Profile")
    description: str = Field(default="")
    device_type: str = Field(default="Generic")
    tests: List[str] = Field(default_factory=list)
    test_args: Dict[str, Dict[str, Any]] = Field(default_factory=dict)


def _normalize_payload_to_json_string(value: Any) -> str:
    """Accepts a dict or JSON string, validates against CrucibleProfilePayload, returns JSON string.

    - If value is a string: parse as JSON. If parsing fails, raise ValueError.
    - If value is a dict: use as-is.
    - Otherwise: raise ValueError.
    """
    if isinstance(value, str):
        try:
            data = json.loads(value or "{}")
        except json.JSONDecodeError as e:
            raise ValueError(f"payload must be valid JSON: {e}") from e
    elif isinstance(value, dict):
        data = value
    elif value is None:
        data = {}
    else:
        raise ValueError("payload must be a JSON string or object")

    # Validate and coerce to the Crucible schema
    model = CrucibleProfilePayload(**data)
    # Store compact JSON string for DB
    return json.dumps(model.model_dump())


class ProfileBase(BaseModel):
    name: str = Field(..., max_length=128)
    description: str | None = None
    payload: str = Field(default="{}", description="JSON string defining tests/wipes")
    version: str | None = None

    # Validate and normalize payload on input
    @field_validator("payload", mode="before")
    @classmethod
    def validate_payload(cls, v: Any) -> str:
        return _normalize_payload_to_json_string(v)


class ProfileCreate(ProfileBase):
    pass


class ProfileUpdate(BaseModel):
    name: str | None = None
    description: str | None = None
    payload: str | None = None
    version: str | None = None

    @field_validator("payload", mode="before")
    @classmethod
    def validate_payload(cls, v: Any) -> str | None:
        if v is None:
            return None
        return _normalize_payload_to_json_string(v)


class ProfileOut(ProfileBase):
    id: int
