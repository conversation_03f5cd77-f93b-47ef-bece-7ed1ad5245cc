from __future__ import annotations
from datetime import datetime
from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class ResultIn(BaseModel):
    asset_number: Optional[str] = None
    mac: Optional[str] = None
    profile_name: Optional[str] = None
    test_name: str
    status: str = Field(pattern=r"^(passed|failed|skipped|running)$")
    metrics: Optional[Dict[str, Any]] = None
    started_at: Optional[datetime] = None
    ended_at: Optional[datetime] = None


class ResultOut(BaseModel):
    id: int
    asset_number: Optional[str]
    mac: Optional[str]
    profile_name: Optional[str]
    test_name: str
    status: str
    metrics: Optional[Dict[str, Any]]
    started_at: Optional[datetime]
    ended_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True
