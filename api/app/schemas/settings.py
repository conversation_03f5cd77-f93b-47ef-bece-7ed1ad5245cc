from __future__ import annotations
from pydantic import BaseModel, HttpUrl


class PXESettingsIn(BaseModel):
    kernel_url: str | None = None
    initrd_url: str | None = None
    kernel_args: str | None = None


class PXESettingsOut(BaseModel):
    kernel_url: str | None = None
    initrd_url: str | None = None
    kernel_args: str | None = None


class WebsiteSettingsIn(BaseModel):
    """Settings for Nexus Website integration."""
    base_url: str | None = None
    api_key: str | None = None


class WebsiteSettingsOut(BaseModel):
    base_url: str | None = None
    # Note: never echo the raw API key; only indicate whether it's set
    api_key_set: bool = False
