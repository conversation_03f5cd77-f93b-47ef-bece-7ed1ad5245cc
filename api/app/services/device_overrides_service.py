from __future__ import annotations
from typing import Optional
from sqlalchemy.orm import Session
from ..models.device_override import DeviceOverride


def _normalize_mac(mac: str) -> str:
    s = ''.join(ch for ch in (mac or '').lower() if ch in '0123456789abcdef')
    if len(s) != 12:
        raise ValueError('invalid mac')
    return s


def _format_mac(mac: str) -> str:
    s = mac or ''
    if len(s) == 12:
        return ':'.join([s[i:i+2] for i in range(0, 12, 2)])
    return mac


def get_override(db: Session, mac: str) -> Optional[DeviceOverride]:
    mac_norm = _normalize_mac(mac)
    return db.query(DeviceOverride).filter(DeviceOverride.mac == mac_norm).one_or_none()


def upsert_override(
    db: Session,
    mac: str,
    profile_id: Optional[int] = None,
    kernel_url: Optional[str] = None,
    initrd_url: Optional[str] = None,
    kernel_args: Optional[str] = None,
) -> DeviceOverride:
    mac_norm = _normalize_mac(mac)
    row = db.query(DeviceOverride).filter(DeviceOverride.mac == mac_norm).one_or_none()
    if row is None:
        row = DeviceOverride(mac=mac_norm)
        db.add(row)
    # update fields
    row.profile_id = profile_id
    row.kernel_url = kernel_url
    row.initrd_url = initrd_url
    row.kernel_args = kernel_args
    db.commit()
    db.refresh(row)
    return row


def delete_override(db: Session, mac: str) -> bool:
    mac_norm = _normalize_mac(mac)
    row = db.query(DeviceOverride).filter(DeviceOverride.mac == mac_norm).one_or_none()
    if not row:
        return False
    db.delete(row)
    db.commit()
    return True
