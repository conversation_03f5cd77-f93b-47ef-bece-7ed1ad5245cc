"""
Credit service for communicating with Nexus website API.
Handles credit consumption for hardware tests and drive wipes.
"""
from __future__ import annotations
import logging
from typing import Dict, Any, Optional
from enum import Enum
import requests
from sqlalchemy.orm import Session

from ..services.settings_service import get_website_settings, get

logger = logging.getLogger(__name__)


class OperationType(Enum):
    """Operation types that consume credits."""
    STANDARD_WIPE = "STANDARD_WIPE"
    WIPE_WITH_DIAGNOSTICS = "WIPE_WITH_DIAGNOSTICS"
    DIAGNOSTICS_ONLY = "DIAGNOSTICS_ONLY"


class OperationStatus(Enum):
    """Operation status for credit operations."""
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELED = "CANCELED"


class CreditService:
    """Service for managing credit operations with Nexus website."""
    
    def __init__(self, db: Session):
        self.db = db
        self._load_config()
    
    def _load_config(self):
        """Load website configuration from database."""
        settings = get_website_settings(self.db)
        self.base_url = settings.get("base_url")
        
        # Get the actual API key (not just the flag)
        from ..services.settings_service import WEBSITE_API_KEY_KEY
        self.api_key = get(self.db, WEBSITE_API_KEY_KEY)
        
        if not self.base_url or not self.api_key:
            logger.warning("Website API not configured - credit operations will be skipped")
    
    def _make_request(self, method: str, endpoint: str, json_data: Optional[Dict] = None) -> Optional[Dict]:
        """Make an authenticated request to the website API."""
        if not self.base_url or not self.api_key:
            logger.debug("Website API not configured, skipping request")
            return None
        
        url = f"{self.base_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Making {method} request to {url}")
        if json_data:
            logger.debug(f"Request payload: {json_data}")
        
        try:
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                json=json_data,
                timeout=10
            )
            
            logger.info(f"Response status: {response.status_code}")
            
            # Try to get response body for logging
            try:
                response_data = response.json()
                logger.debug(f"Response body: {response_data}")
            except:
                response_data = None
                logger.debug(f"Response text: {response.text[:500]}")
            
            if response.status_code == 401:
                logger.error("API key is invalid or expired")
                return {"error": "unauthorized", "status_code": 401}
            elif response.status_code == 402:
                logger.warning("Insufficient credits for operation")
                return {"error": "insufficient_credits", "status_code": 402, "data": response_data}
            elif response.status_code == 503:
                logger.error("Website API unavailable")
                return {"error": "service_unavailable", "status_code": 503}
            
            response.raise_for_status()
            return response_data if response_data else response.json()
            
        except requests.exceptions.Timeout:
            logger.error(f"Request to {url} timed out")
            return {"error": "timeout", "status_code": 0}
        except requests.exceptions.ConnectionError as e:
            logger.error(f"Connection error to {url}: {e}")
            return {"error": "connection_error", "status_code": 0}
        except requests.exceptions.RequestException as e:
            logger.error(f"Error communicating with website API: {e}")
            if hasattr(e, 'response') and e.response:
                logger.error(f"Error response status: {e.response.status_code}")
                logger.error(f"Error response text: {e.response.text[:500]}")
            return {"error": "request_failed", "status_code": getattr(e.response, 'status_code', 0) if hasattr(e, 'response') else 0}
    
    def get_balance(self) -> Optional[Dict[str, Any]]:
        """Get current credit balance and subscription info."""
        result = self._make_request("GET", "/api/credits/balance")
        
        # Handle error responses
        if result and "error" in result:
            logger.warning(f"Balance check failed: {result.get('error')}")
            return None
        
        return result
    
    def start_operation(
        self,
        operation_type: OperationType,
        device_serial: str,
        device_model: str,
        metadata: Optional[Dict] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Start an operation and consume credits.
        
        Returns:
            Dict with operationId if successful, None if failed
        """
        data = {
            "operationType": operation_type.value,
            "deviceSerial": device_serial,
            "deviceModel": device_model,
            "metadata": metadata or {}
        }
        
        result = self._make_request("POST", "/api/credits/consume", data)
        
        # Handle error responses with specific status codes
        if result and "error" in result:
            logger.error(f"Failed to start operation: {result.get('error')} (status: {result.get('status_code')})")
            # Return the error info so the router can handle it properly
            return result
        
        if result and result.get("success"):
            logger.info(
                f"Started {operation_type.value} operation {result.get('operationId')} "
                f"for device {device_serial}, consumed {result.get('creditsConsumed')} credits"
            )
        
        return result
    
    def complete_operation(
        self,
        operation_id: str,
        status: OperationStatus,
        metadata: Optional[Dict] = None,
        error_message: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Complete, fail, or cancel an operation.
        Failed/canceled operations will trigger credit refund.
        
        Returns:
            Dict with operation details if successful, None if failed
        """
        data = {
            "operationId": operation_id,
            "status": status.value,
            "metadata": metadata or {}
        }
        
        if error_message and status in [OperationStatus.FAILED, OperationStatus.CANCELED]:
            data["errorMessage"] = error_message
        
        result = self._make_request("PATCH", "/api/credits/consume", data)
        
        if result and result.get("success"):
            operation = result.get("operation", {})
            if operation.get("refunded"):
                logger.info(f"Operation {operation_id} {status.value}, credits refunded")
            else:
                logger.info(f"Operation {operation_id} completed successfully")
        
        return result
    
    def consume_for_diagnostics(
        self,
        device_serial: str,
        device_model: str,
        test_results: Dict[str, Any]
    ) -> Optional[str]:
        """
        Consume credits for hardware diagnostics.
        
        Returns:
            Operation ID if successful, None if failed
        """
        # Start the operation
        result = self.start_operation(
            operation_type=OperationType.DIAGNOSTICS_ONLY,
            device_serial=device_serial,
            device_model=device_model,
            metadata={
                "test_count": len(test_results.get("tests", [])),
                "profile": test_results.get("profile_name", "unknown")
            }
        )
        
        if not result or not result.get("operationId"):
            return None
        
        return result["operationId"]
    
    def consume_for_wipe(
        self,
        device_serial: str,
        device_model: str,
        drive_info: Dict[str, Any],
        with_diagnostics: bool = False
    ) -> Optional[str]:
        """
        Consume credits for drive wipe operation.
        
        Returns:
            Operation ID if successful, None if failed
        """
        operation_type = (
            OperationType.WIPE_WITH_DIAGNOSTICS 
            if with_diagnostics 
            else OperationType.STANDARD_WIPE
        )
        
        # Start the operation
        result = self.start_operation(
            operation_type=operation_type,
            device_serial=device_serial,
            device_model=device_model,
            metadata={
                "drive_model": drive_info.get("model", "unknown"),
                "drive_size_gb": drive_info.get("size_gb", 0),
                "wipe_method": drive_info.get("method", "secure_erase")
            }
        )
        
        if not result or not result.get("operationId"):
            return None
        
        return result["operationId"]


def get_credit_service(db: Session) -> CreditService:
    """Factory function to get a CreditService instance."""
    return CreditService(db)
