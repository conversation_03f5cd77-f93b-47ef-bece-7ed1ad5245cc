from __future__ import annotations
import asyncio
from typing import AsyncItera<PERSON>, Dict, Any

class EventsBus:
    """Simple in-memory pub/sub for broadcasting events to SSE clients.
    Not suitable for multi-process; good enough for dev/MVP.
    """

    def __init__(self) -> None:
        self._queue: "asyncio.Queue[Dict[str, Any]]" = asyncio.Queue()

    async def publish(self, event: Dict[str, Any]) -> None:
        await self._queue.put(event)

    async def subscribe(self) -> AsyncIterator[Dict[str, Any]]:
        while True:
            item = await self._queue.get()
            yield item


events_bus = EventsBus()
