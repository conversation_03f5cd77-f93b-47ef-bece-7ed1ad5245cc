from __future__ import annotations
from sqlalchemy.orm import Session
from ..models.setting import Setting
from ..config import settings as app_settings


PXE_KERNEL_KEY = "pxe.kernel_url"
PXE_INITRD_KEY = "pxe.initrd_url"
PXE_ARGS_KEY = "pxe.kernel_args"

# Website integration keys
WEBSITE_BASE_URL_KEY = "website.base_url"
WEBSITE_API_KEY_KEY = "website.api_key"


def get(db: Session, key: str) -> str | None:
    row = db.query(Setting).filter(Setting.key == key).one_or_none()
    return row.value if row else None


def set_(db: Session, key: str, value: str | None) -> None:
    if value is None:
        return
    row = db.query(Setting).filter(Setting.key == key).one_or_none()
    if row is None:
        row = Setting(key=key, value=value)
        db.add(row)
    else:
        row.value = value
    db.flush()


def get_pxe_settings(db: Session) -> dict:
    return {
        "kernel_url": get(db, PXE_KERNEL_KEY),
        "initrd_url": get(db, PXE_INITRD_KEY),
        "kernel_args": get(db, PXE_ARGS_KEY),
    }


def set_pxe_settings(db: Session, kernel_url: str | None, initrd_url: str | None, kernel_args: str | None) -> dict:
    set_(db, PXE_KERNEL_KEY, kernel_url)
    set_(db, PXE_INITRD_KEY, initrd_url)
    set_(db, PXE_ARGS_KEY, kernel_args)
    db.commit()
    return get_pxe_settings(db)


def get_website_settings(db: Session) -> dict:
    base_url = get(db, WEBSITE_BASE_URL_KEY)
    api_key = get(db, WEBSITE_API_KEY_KEY)
    return {
        "base_url": base_url,
        "api_key_set": bool(api_key),
    }


def set_website_settings(db: Session, base_url: str | None, api_key: str | None) -> dict:
    # Normalize base_url
    if base_url:
        base_url = base_url.strip()
        if base_url.endswith('/'):
            base_url = base_url[:-1]
    set_(db, WEBSITE_BASE_URL_KEY, base_url)
    # Store API key as-is (consider encryption at rest in future)
    if api_key:
        set_(db, WEBSITE_API_KEY_KEY, api_key.strip())
    db.commit()
    return get_website_settings(db)
