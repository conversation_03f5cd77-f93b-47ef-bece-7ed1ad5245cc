from __future__ import annotations
from typing import List, Optional
from sqlalchemy.orm import Session
from ..models.profile import Profile
from ..models.setting import Setting
import json

DEFAULT_PROFILE_KEY = "profiles.default_id"


def list_profiles(db: Session) -> List[Profile]:
    return db.query(Profile).order_by(Profile.name.asc()).all()


def create_profile(db: Session, name: str, description: Optional[str], payload: str, version: Optional[str]) -> Profile:
    row = Profile(name=name, description=description, payload=payload, version=version)
    db.add(row)
    db.commit()
    db.refresh(row)
    return row


def get_profile(db: Session, pid: int) -> Optional[Profile]:
    return db.query(Profile).filter(Profile.id == pid).one_or_none()


def update_profile(db: Session, pid: int, **updates) -> Optional[Profile]:
    row = get_profile(db, pid)
    if not row:
        return None
    for k, v in updates.items():
        if v is not None and hasattr(row, k):
            setattr(row, k, v)
    db.commit()
    db.refresh(row)
    return row


def delete_profile(db: Session, pid: int) -> bool:
    row = get_profile(db, pid)
    if not row:
        return False
    db.delete(row)
    db.commit()
    return True


def get_default_profile_id(db: Session) -> Optional[int]:
    s = db.query(Setting).filter(Setting.key == DEFAULT_PROFILE_KEY).one_or_none()
    if not s:
        return None
    try:
        return int(s.value)
    except Exception:
        return None


def set_default_profile_id(db: Session, pid: Optional[int]) -> Optional[int]:
    # Validate exists when setting non-null
    if pid is not None and not get_profile(db, pid):
        return None
    s = db.query(Setting).filter(Setting.key == DEFAULT_PROFILE_KEY).one_or_none()
    if s is None:
        if pid is None:
            return None
        s = Setting(key=DEFAULT_PROFILE_KEY, value=str(pid))
        db.add(s)
    else:
        if pid is None:
            db.delete(s)
            db.commit()
            return None
        s.value = str(pid)
    db.commit()
    return pid


def seed_default_profiles(db: Session) -> None:
    """Seed the database with Crucible default profiles if none exist.

    Payloads are Crucible-compatible and stored as JSON strings in `Profile.payload`.
    """
    # If any profiles exist, do nothing
    existing_count = db.query(Profile).count()
    if existing_count > 0:
        return

    # Define default profiles (aligned with Crucible agent defaults)
    defaults = [
        {
            "name": "Desktop",
            "description": "Standard tests for desktop computers",
            "device_type": "Desktop",
            "tests": [
                "agent.tests.cpu_test.run_basic_cpu_test",
                "agent.tests.cpu_test.run_cpu_stress_test",
                "agent.tests.ram_test.run_ram_test",
                "agent.tests.ram_test.run_advanced_ram_test",
                "agent.tests.display_test.run_lcd_test_gui",
                "agent.tests.keyboard_test.run_keyboard_test",
            ],
            "test_args": {
                "agent.tests.ram_test.run_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 30,
                    "duration_seconds": 45,
                },
                "agent.tests.ram_test.run_advanced_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 35,
                    "duration_seconds": 60,
                },
                "agent.tests.visual_ram_test.run_visual_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 30,
                    "duration_seconds": 45,
                },
                "agent.tests.web_visual_ram_test.run_web_visual_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 30,
                    "duration_seconds": 45,
                },
            },
        },
        {
            "name": "Laptop",
            "description": "Standard tests for laptop computers",
            "device_type": "Laptop",
            "tests": [
                "agent.tests.cpu_test.run_basic_cpu_test",
                "agent.tests.ram_test.run_ram_test",
                "agent.tests.display_test.run_lcd_test_gui",
                "agent.tests.keyboard_test.run_keyboard_test",
                "agent.tests.pointing_device_test.run_pointing_device_test",
                "agent.tests.battery_test.run_battery_test",
            ],
            "test_args": {
                "agent.tests.ram_test.run_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 20,
                    "duration_seconds": 30,
                },
                "agent.tests.visual_ram_test.run_visual_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 20,
                    "duration_seconds": 30,
                },
                "agent.tests.web_visual_ram_test.run_web_visual_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 20,
                    "duration_seconds": 30,
                },
            },
        },
        {
            "name": "Battery",
            "description": "Battery health and performance tests for laptops and portable devices",
            "device_type": "Laptop",
            "tests": [
                "agent.tests.battery_test.run_battery_test",
                "agent.tests.battery_test.run_battery_discharge_test",
                "agent.tests.battery_test.run_battery_charge_test",
                "agent.tests.battery_test.run_battery_full_assessment",
            ],
            "test_args": {
                "agent.tests.battery_test.run_battery_discharge_test": {"duration_seconds": 120},
                "agent.tests.battery_test.run_battery_charge_test": {"duration_seconds": 180},
            },
        },
        {
            "name": "Visual Tests",
            "description": "Visual tests for interactive diagnostics",
            "device_type": "Any",
            "tests": [
                "agent.tests.visual_cpu_test.run_visual_cpu_test",
                "agent.tests.visual_ram_test.run_visual_ram_test",
            ],
            "test_args": {
                "agent.tests.visual_ram_test.run_visual_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 25,
                    "duration_seconds": 60,
                }
            },
        },
        {
            "name": "Quick Check",
            "description": "Quick basic tests for rapid diagnostics",
            "device_type": "Any",
            "tests": [
                "agent.tests.cpu_test.run_basic_cpu_test",
                "agent.tests.ram_test.run_ram_test",
            ],
            "test_args": {
                "agent.tests.ram_test.run_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 15,
                    "duration_seconds": 20,
                }
            },
        },
        {
            "name": "Stress Test",
            "description": "Extended stress tests for stability verification",
            "device_type": "Any",
            "tests": [
                "agent.tests.cpu_test.run_cpu_stress_test",
                "agent.tests.ram_test.run_advanced_ram_test",
            ],
            "test_args": {
                "agent.tests.cpu_test.run_cpu_stress_test": {"duration_seconds": 60},
                "agent.tests.ram_test.run_advanced_ram_test": {
                    "test_size_mode": "percentage",
                    "test_size_value": 40,
                    "duration_seconds": 120,
                },
            },
        },
        {
            "name": "Secure Wipe",
            "description": "Secure data wiping for device decommissioning",
            "device_type": "Any",
            "tests": [
                "agent.tests.drive_wipe_test.run_secure_wipe_test",
                "agent.tests.drive_wipe_test.run_wipe_verification_test",
            ],
            "test_args": {},
        },
    ]

    created_rows: list[Profile] = []
    for p in defaults:
        row = Profile(
            name=p["name"],
            description=p.get("description"),
            payload=json.dumps({
                "name": p["name"],
                "description": p.get("description", ""),
                "device_type": p.get("device_type", "Generic"),
                "tests": p.get("tests", []),
                "test_args": p.get("test_args", {}),
            }),
            version=None,
        )
        db.add(row)
        created_rows.append(row)

    db.commit()

    # Set a sensible default profile: Quick Check (if present)
    quick = next((r for r in db.query(Profile).all() if r.name == "Quick Check"), None)
    if quick is not None:
        set_default_profile_id(db, quick.id)
