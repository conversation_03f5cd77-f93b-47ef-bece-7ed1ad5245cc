from __future__ import annotations
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from ..db import get_db
from ..schemas.settings import PXESettingsIn, PXESettingsOut, WebsiteSettingsIn, WebsiteSettingsOut
from ..services.settings_service import (
    get_pxe_settings,
    set_pxe_settings,
    get_website_settings,
    set_website_settings,
)

router = APIRouter()


@router.get("/settings/pxe", response_model=PXESettingsOut)
def get_pxe(db: Session = Depends(get_db)):
    data = get_pxe_settings(db)
    return PXESettingsOut(**data)


@router.post("/settings/pxe", response_model=PXESettingsOut)
def update_pxe(payload: PXESettingsIn, db: Session = Depends(get_db)):
    data = set_pxe_settings(db, payload.kernel_url, payload.initrd_url, payload.kernel_args)
    return PXESettingsOut(**data)


@router.get("/settings/website", response_model=WebsiteSettingsOut)
def get_website(db: Session = Depends(get_db)):
    data = get_website_settings(db)
    return WebsiteSettingsOut(**data)


@router.post("/settings/website", response_model=WebsiteSettingsOut)
def update_website(payload: WebsiteSettingsIn, db: Session = Depends(get_db)):
    data = set_website_settings(db, base_url=payload.base_url, api_key=payload.api_key)
    return WebsiteSettingsOut(**data)
