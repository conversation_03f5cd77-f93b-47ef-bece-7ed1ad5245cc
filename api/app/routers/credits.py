"""Credit management router for Nexus API.
Provides endpoints to check balance and manage credit operations.
"""
from __future__ import annotations
import os
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from pydantic import BaseModel
import logging

from ..db import get_db
from ..services.credit_service import get_credit_service, OperationStatus, OperationType

logger = logging.getLogger(__name__)


router = APIRouter()


class CreditBalanceResponse(BaseModel):
    balance: Optional[int] = None
    subscription: Optional[dict] = None
    configured: bool = False
    error: Optional[str] = None


class CompleteOperationRequest(BaseModel):
    operation_id: str
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[dict] = None


class StartOperationRequest(BaseModel):
    operationType: str
    deviceSerial: str
    deviceModel: str
    metadata: Optional[dict] = None


class PatchConsumeRequest(BaseModel):
    operationId: str
    status: str  # COMPLETED | FAILED | CANCELED | IN_PROGRESS
    metadata: Optional[dict] = None
    errorMessage: Optional[str] = None


@router.get("/credits/balance", response_model=CreditBalanceResponse)
async def get_credit_balance(db: Session = Depends(get_db)):
    """Get current credit balance from website API."""
    service = get_credit_service(db)
    
    if not service.base_url or not service.api_key:
        return CreditBalanceResponse(
            configured=False,
            error="Website API not configured"
        )
    
    result = service.get_balance()
    if not result:
        return CreditBalanceResponse(
            configured=True,
            error="Failed to fetch balance"
        )
    
    return CreditBalanceResponse(
        balance=result.get("balance"),
        subscription=result.get("subscription"),
        configured=True
    )


@router.post("/credits/complete-operation")
async def complete_operation(
    request: CompleteOperationRequest,
    db: Session = Depends(get_db)
):
    """Complete or fail a credit operation."""
    service = get_credit_service(db)
    
    if not service.base_url or not service.api_key:
        raise HTTPException(status_code=503, detail="Website API not configured")
    
    status = OperationStatus.COMPLETED if request.success else OperationStatus.FAILED
    
    result = service.complete_operation(
        operation_id=request.operation_id,
        status=status,
        metadata=request.metadata,
        error_message=request.error_message
    )
    
    if not result:
        raise HTTPException(status_code=500, detail="Failed to complete operation")
    
    return result


# Relay endpoints to match agent client expectations
@router.post("/credits/consume")
async def consume_start(
    request: StartOperationRequest,
    db: Session = Depends(get_db)
):
    """Start an operation and consume credits (relay to website API)."""
    service = get_credit_service(db)

    if not service.base_url or not service.api_key:
        raise HTTPException(status_code=503, detail="Website API not configured")

    # Development bypass for testing
    dev_bypass = os.getenv("NEXUS_DEV_BYPASS_CREDITS", "").lower() == "true"
    if dev_bypass:
        logger.warning("DEV MODE: Bypassing credit check")
        return {
            "success": True,
            "operationId": f"dev-{request.operationType}-{request.deviceSerial}",
            "creditsConsumed": 0,
            "message": "Development bypass - no credits consumed"
        }
    
    # Pre-check balance to provide clear error early
    bal = service.get_balance()
    if bal is None:
        # Website API likely unreachable or misconfigured
        raise HTTPException(status_code=503, detail="Website API unreachable")
    if bal.get("configured") and (bal.get("balance") == 0):
        raise HTTPException(status_code=402, detail="Insufficient credits")

    try:
        op_type = OperationType[request.operationType]
    except KeyError:
        raise HTTPException(status_code=400, detail="Invalid operationType")

    result = service.start_operation(
        operation_type=op_type,
        device_serial=request.deviceSerial,
        device_model=request.deviceModel,
        metadata=request.metadata or {},
    )

    # Handle error responses with proper status codes
    if result and "error" in result:
        error_type = result.get("error")
        status_code = result.get("status_code", 500)
        
        if error_type == "insufficient_credits" or status_code == 402:
            raise HTTPException(status_code=402, detail="Insufficient credits")
        elif error_type == "unauthorized" or status_code == 401:
            raise HTTPException(status_code=401, detail="API key is invalid or expired")
        elif error_type == "service_unavailable" or status_code == 503:
            raise HTTPException(status_code=503, detail="Website API unavailable")
        elif error_type in ["timeout", "connection_error"]:
            raise HTTPException(status_code=503, detail="Website API unreachable")
        else:
            raise HTTPException(status_code=500, detail=f"Failed to start operation: {error_type}")
    
    if not result:
        raise HTTPException(status_code=500, detail="Failed to start operation")

    return result


@router.patch("/credits/consume")
async def consume_patch(
    request: PatchConsumeRequest,
    db: Session = Depends(get_db)
):
    """Complete/fail an operation (relay to website API)."""
    service = get_credit_service(db)

    if not service.base_url or not service.api_key:
        raise HTTPException(status_code=503, detail="Website API not configured")

    try:
        status = OperationStatus[request.status]
    except KeyError:
        raise HTTPException(status_code=400, detail="Invalid status")

    # Development bypass for testing
    dev_bypass = os.getenv("NEXUS_DEV_BYPASS_CREDITS", "").lower() == "true"
    if dev_bypass:
        logger.warning(f"DEV MODE: Completing operation {request.operationId} with status {status.value}")
        return {
            "success": True,
            "operation": {
                "operationId": request.operationId,
                "status": status.value,
                "refunded": status in [OperationStatus.FAILED, OperationStatus.CANCELED]
            },
            "message": "Development bypass - operation completed"
        }
    
    result = service.complete_operation(
        operation_id=request.operationId,
        status=status,
        metadata=request.metadata or {},
        error_message=request.errorMessage,
    )

    # Handle error responses with proper status codes
    if result and "error" in result:
        error_type = result.get("error")
        status_code = result.get("status_code", 500)
        
        if error_type == "unauthorized" or status_code == 401:
            raise HTTPException(status_code=401, detail="API key is invalid or expired")
        elif error_type == "service_unavailable" or status_code == 503:
            raise HTTPException(status_code=503, detail="Website API unavailable")
        elif error_type in ["timeout", "connection_error"]:
            raise HTTPException(status_code=503, detail="Website API unreachable")
        else:
            raise HTTPException(status_code=500, detail=f"Failed to complete operation: {error_type}")
    
    if not result:
        raise HTTPException(status_code=500, detail="Failed to complete operation")

    return result
