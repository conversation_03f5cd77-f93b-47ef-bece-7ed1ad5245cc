from __future__ import annotations
from typing import List, Optional
import json
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..db import get_db
from ..schemas.profile import ProfileCreate, ProfileUpdate, ProfileOut
from ..services import profiles_service as svc

router = APIRouter()


@router.get("/profiles", response_model=List[ProfileOut])
def list_profiles(db: Session = Depends(get_db)):
    rows = svc.list_profiles(db)
    return [
        ProfileOut(id=r.id, name=r.name, description=r.description, payload=r.payload, version=r.version)
        for r in rows
    ]


@router.post("/profiles", response_model=ProfileOut)
def create_profile(payload: ProfileCreate, db: Session = Depends(get_db)):
    row = svc.create_profile(db, payload.name, payload.description, payload.payload, payload.version)
    return ProfileOut(id=row.id, name=row.name, description=row.description, payload=row.payload, version=row.version)


@router.get("/profiles/default")
def get_default(db: Session = Depends(get_db)):
    pid = svc.get_default_profile_id(db)
    return {"default_id": pid}


@router.post("/profiles/default")
def set_default(payload: dict, db: Session = Depends(get_db)):
    pid = payload.get("default_id")
    if pid is not None and not isinstance(pid, int):
        raise HTTPException(status_code=400, detail="default_id must be int or null")
    res = svc.set_default_profile_id(db, pid)
    if pid is not None and res is None:
        raise HTTPException(status_code=404, detail="Profile not found")
    return {"default_id": res}


@router.get("/profiles/crucible")
def list_profiles_crucible(db: Session = Depends(get_db)):
    """Return an array of Crucible-compatible profile payload objects.

    Each item is the parsed JSON from the stored profile.payload.
    Invalid or empty payloads are returned as empty objects.
    """
    rows = svc.list_profiles(db)
    out: List[dict] = []
    for r in rows:
        try:
            out.append(json.loads(r.payload or "{}"))
        except json.JSONDecodeError:
            out.append({})
    return out


@router.get("/profiles/{pid}", response_model=ProfileOut)
def get_profile(pid: int, db: Session = Depends(get_db)):
    row = svc.get_profile(db, pid)
    if not row:
        raise HTTPException(status_code=404, detail="Profile not found")
    return ProfileOut(id=row.id, name=row.name, description=row.description, payload=row.payload, version=row.version)


@router.patch("/profiles/{pid}", response_model=ProfileOut)
def update_profile(pid: int, payload: ProfileUpdate, db: Session = Depends(get_db)):
    row = svc.update_profile(db, pid, name=payload.name, description=payload.description, payload=payload.payload, version=payload.version)
    if not row:
        raise HTTPException(status_code=404, detail="Profile not found")
    return ProfileOut(id=row.id, name=row.name, description=row.description, payload=row.payload, version=row.version)


@router.delete("/profiles/{pid}")
def delete_profile(pid: int, db: Session = Depends(get_db)):
    ok = svc.delete_profile(db, pid)
    if not ok:
        raise HTTPException(status_code=404, detail="Profile not found")
    return {"ok": True}
