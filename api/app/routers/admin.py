from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import docker

router = APIRouter()

# Map friendly names to container names from docker-compose
CONTAINERS = {
    "api": "nexus-api",
    "ui": "nexus-ui",
    "nginx": "nexus-nginx",
}

class RestartRequest(BaseModel):
    service: str = "all"  # api | ui | nginx | all

class StartRequest(BaseModel):
    service: str = "all"  # api | ui | nginx | all

class StopRequest(BaseModel):
    service: str = "all"  # api | ui | nginx | all


def _client():
    # Force local socket to avoid problematic DOCKER_HOST values
    try:
        return docker.DockerClient(base_url='unix://var/run/docker.sock')
    except Exception:
        # Fallback to env if explicit socket fails
        return docker.from_env()


def _resolve_services(service: str):
    if service == "all":
        return list(CONTAINERS.values())
    key = service.lower()
    if key not in CONTAINERS:
        raise HTTPException(status_code=400, detail="Invalid service. Use api|ui|nginx|all")
    return [CONTAINERS[key]]


@router.post("/admin/docker/restart")
def docker_restart(req: RestartRequest):
    try:
        d = _client()
        d.ping()
        names = _resolve_services(req.service)
        results = {}
        for name in names:
            try:
                c = d.containers.get(name)
                c.restart()
                results[name] = "restarted"
            except docker.errors.NotFound:
                results[name] = "not_found"
            except Exception as e:
                results[name] = f"error: {e}"
        return {"ok": True, "results": results}
    except Exception as e:
        return {"ok": False, "error": str(e)}


@router.post("/admin/docker/start")
def docker_start(req: StartRequest):
    try:
        d = _client()
        d.ping()
        names = _resolve_services(req.service)
        results = {}
        for name in names:
            try:
                c = d.containers.get(name)
                c.reload()
                if c.status != "running":
                    c.start()
                results[name] = "running"
            except docker.errors.NotFound:
                results[name] = "not_found"
            except Exception as e:
                results[name] = f"error: {e}"
        return {"ok": True, "results": results}
    except Exception as e:
        return {"ok": False, "error": str(e)}


@router.post("/admin/docker/stop")
def docker_stop(req: StopRequest):
    try:
        d = _client()
        d.ping()
        names = _resolve_services(req.service)
        results = {}
        for name in names:
            try:
                c = d.containers.get(name)
                c.reload()
                if c.status == "running":
                    c.stop()
                results[name] = "stopped"
            except docker.errors.NotFound:
                results[name] = "not_found"
            except Exception as e:
                results[name] = f"error: {e}"
        return {"ok": True, "results": results}
    except Exception as e:
        return {"ok": False, "error": str(e)}


@router.get("/admin/docker/info")
def docker_info():
    try:
        d = _client()
        version = d.version()
        return {"ok": True, "ServerVersion": version.get("Version"), "ApiVersion": version.get("ApiVersion"), "MinAPIVersion": version.get("MinAPIVersion")}
    except Exception as e:
        return {"ok": False, "error": str(e)}
