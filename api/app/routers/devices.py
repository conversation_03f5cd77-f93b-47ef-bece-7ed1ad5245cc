from __future__ import annotations
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.device import Device
from ..models.session import Session as DeviceSession
from ..schemas.device import DeviceRegister, DeviceOut

router = APIRouter()


def _normalize_mac(mac: str) -> str:
    s = ''.join(ch for ch in mac.lower() if ch in '0123456789abcdef')
    if len(s) != 12:
        raise ValueError("invalid mac")
    return s


def _format_mac(mac: str) -> str:
    s = mac
    if len(s) == 12:
        return ':'.join([s[i:i+2] for i in range(0, 12, 2)])
    return mac


@router.post("/devices/register", response_model=DeviceOut)
def register_device(payload: DeviceRegister, db: Session = Depends(get_db)):
    try:
        mac_norm = _normalize_mac(payload.mac)
    except Exception:
        raise HTTPException(status_code=400, detail="Invalid MAC address")

    device = db.query(Device).filter(Device.mac == mac_norm).one_or_none()
    if device is None:
        device = Device(mac=mac_norm, uuid=payload.uuid, serial=payload.serial)
        db.add(device)
        db.flush()
    else:
        if payload.uuid:
            device.uuid = payload.uuid
        if payload.serial:
            device.serial = payload.serial
        device.touch()

    # start a new session entry
    session = DeviceSession(device_id=device.id, state="registered")
    db.add(session)

    db.commit()
    db.refresh(device)

    return DeviceOut(
        id=device.id,
        mac=_format_mac(device.mac),
        uuid=device.uuid,
        serial=device.serial,
        last_seen=device.last_seen,
    )


@router.get("/devices/recent", response_model=List[DeviceOut])
def recent_devices(limit: int = 25, db: Session = Depends(get_db)):
    rows = (
        db.query(Device)
        .order_by(Device.last_seen.desc())
        .limit(min(limit, 200))
        .all()
    )
    return [
        DeviceOut(
            id=r.id,
            mac=_format_mac(r.mac),
            uuid=r.uuid,
            serial=r.serial,
            last_seen=r.last_seen,
        )
        for r in rows
    ]
