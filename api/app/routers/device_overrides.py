from __future__ import annotations
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from ..db import get_db
from ..schemas.device_override import DeviceOverrideIn, DeviceOverrideOut
from ..services import device_overrides_service as svc

router = APIRouter()


def _to_out(row) -> DeviceOverrideOut:
    data = {
        "id": row.id,
        "mac": svc._format_mac(row.mac),
        "profile_id": row.profile_id,
        "kernel_url": row.kernel_url,
        "initrd_url": row.initrd_url,
        "kernel_args": row.kernel_args,
    }
    return DeviceOverrideOut(**data)


@router.get("/device_overrides/{mac}", response_model=DeviceOverrideOut)
async def get_device_override(mac: str, db: Session = Depends(get_db)):
    row = svc.get_override(db, mac)
    if not row:
        raise HTTPException(status_code=404, detail="Not found")
    return _to_out(row)


@router.put("/device_overrides/{mac}", response_model=DeviceOverrideOut)
async def put_device_override(mac: str, payload: DeviceOverrideIn, db: Session = Depends(get_db)):
    try:
        row = svc.upsert_override(
            db,
            mac=payload.mac or mac,
            profile_id=payload.profile_id,
            kernel_url=payload.kernel_url,
            initrd_url=payload.initrd_url,
            kernel_args=payload.kernel_args,
        )
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid MAC address")
    return _to_out(row)


@router.delete("/device_overrides/{mac}")
async def delete_device_override(mac: str, db: Session = Depends(get_db)):
    try:
        ok = svc.delete_override(db, mac)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid MAC address")
    if not ok:
        raise HTTPException(status_code=404, detail="Not found")
    return {"ok": True}
