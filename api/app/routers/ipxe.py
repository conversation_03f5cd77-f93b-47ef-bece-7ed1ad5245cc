from __future__ import annotations
from fastapi import APIRouter, Depends, Response, Query
from sqlalchemy.orm import Session
from ..db import get_db
from ..services.settings_service import get_pxe_settings
from ..config import settings as app_settings
from ..services import device_overrides_service as overrides_svc

router = APIRouter()


def _abs(url: str | None) -> str | None:
    if not url:
        return None
    if url.startswith("http://") or url.startswith("https://"):
        return url
    # treat as relative to artifacts
    if url.startswith("/"):
        return f"http://{app_settings.HOSTNAME}{url}"
    return f"http://{app_settings.HOSTNAME}/artifacts/{url}"


@router.get("/ipxe", response_class=Response)
async def ipxe_script(
    mac: str | None = Query(default=None),
    uuid: str | None = Query(default=None),
    db: Session = Depends(get_db),
):
    # Start with global PXE settings
    cfg = get_pxe_settings(db)
    kernel = _abs(cfg.get("kernel_url"))
    initrd = _abs(cfg.get("initrd_url"))
    args = cfg.get("kernel_args") or ""

    # Apply per-MAC overrides if present
    if mac:
        try:
            ov = overrides_svc.get_override(db, mac)
        except Exception:
            ov = None
        if ov:
            if ov.kernel_url:
                kernel = _abs(ov.kernel_url)
            if ov.initrd_url:
                initrd = _abs(ov.initrd_url)
            if ov.kernel_args is not None:
                args = ov.kernel_args

    lines = ["#!ipxe"]
    lines.append("set 210:string http://" + app_settings.HOSTNAME)
    if mac:
        lines.append(f"set boot_mac {mac}")
    if uuid:
        lines.append(f"set boot_uuid {uuid}")

    if not kernel or not initrd:
        lines += [
            "echo Nexus PXE not configured. Set kernel/initrd in /api/settings/pxe",
            "sleep 3",
            "shell",
        ]
        script = "\n".join(lines) + "\n"
        return Response(content=script, media_type="text/plain")

    # kernel args typically include boot=live fetch=... or url=...
    lines.append(
        f"kernel {kernel} {args}".strip()
    )
    lines.append(f"initrd {initrd}")
    lines.append("boot")

    script = "\n".join(lines) + "\n"
    return Response(content=script, media_type="text/plain")
