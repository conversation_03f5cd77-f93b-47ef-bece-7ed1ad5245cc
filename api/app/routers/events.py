from __future__ import annotations
import async<PERSON>
import json
from typing import AsyncIterator

from fastapi import APIRouter
from starlette.responses import StreamingResponse

from ..services.events_bus import events_bus

router = APIRouter()


async def _event_stream() -> AsyncIterator[bytes]:
    # Send an initial comment to establish the stream
    yield b":ok\n\n"
    async for event in events_bus.subscribe():
        # SSE format: data: <json>\n\n
        data = json.dumps(event).encode("utf-8")
        yield b"data: " + data + b"\n\n"
        # cooperative scheduling
        await asyncio.sleep(0)


@router.get("/events")
async def events_sse() -> StreamingResponse:
    return StreamingResponse(_event_stream(), media_type="text/event-stream")
