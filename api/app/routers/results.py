from __future__ import annotations
from datetime import datetime
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session

from ..db import get_db
from ..models.result import Result
from ..schemas.result import ResultIn, ResultOut
from ..services.events_bus import events_bus

router = APIRouter()


@router.post("/results", response_model=ResultOut)
async def create_result(payload: ResultIn, db: Session = Depends(get_db)):
    if not payload.test_name or not payload.status:
        raise HTTPException(status_code=422, detail="test_name and status are required")

    row = Result(
        asset_number=payload.asset_number,
        mac=(payload.mac or None),
        profile_name=payload.profile_name,
        test_name=payload.test_name,
        status=payload.status,
        metrics=payload.metrics,
        started_at=payload.started_at,
        ended_at=payload.ended_at,
    )
    db.add(row)
    db.commit()
    db.refresh(row)

    # Publish event for SSE/WebSocket consumers
    await events_bus.publish({
        "type": "result_created",
        "result": {
            "id": row.id,
            "asset_number": row.asset_number,
            "mac": row.mac,
            "profile_name": row.profile_name,
            "test_name": row.test_name,
            "status": row.status,
            "metrics": row.metrics,
            "started_at": row.started_at.isoformat() if row.started_at else None,
            "ended_at": row.ended_at.isoformat() if row.ended_at else None,
            "created_at": row.created_at.isoformat(),
        }
    })

    return row


@router.get("/results", response_model=List[ResultOut])
async def list_results(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=200),
    db: Session = Depends(get_db),
):
    q = db.query(Result)
    if asset:
        q = q.filter(Result.asset_number == asset)
    if mac:
        q = q.filter(Result.mac == mac)
    rows = q.order_by(Result.created_at.desc()).limit(limit).all()
    return rows


@router.get("/results/latest", response_model=List[ResultOut])
async def latest_results(
    asset: Optional[str] = Query(None),
    mac: Optional[str] = Query(None),
    db: Session = Depends(get_db),
):
    q = db.query(Result)
    if asset:
        q = q.filter(Result.asset_number == asset)
    if mac:
        q = q.filter(Result.mac == mac)
    # pick latest per test_name
    rows = (
        q.order_by(Result.test_name.asc(), Result.created_at.desc()).all()
    )
    seen = set()
    unique: List[Result] = []
    for r in rows:
        key = (r.test_name, r.asset_number, r.mac)
        if key in seen:
            continue
        seen.add(key)
        unique.append(r)
    return unique
