from __future__ import annotations
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from ..db import Base


class DeviceOverride(Base):
    __tablename__ = "device_overrides"
    __table_args__ = (UniqueConstraint("mac", name="uq_device_overrides_mac"),)

    id = Column(Integer, primary_key=True, index=True)
    # MAC stored normalized: 12 lowercase hex chars, no separators
    mac = Column(String(12), nullable=False, index=True)

    # Optional profile association
    profile_id = Column(Integer, ForeignKey("profiles.id"), nullable=True)
    profile = relationship("Profile", backref="device_overrides", lazy="joined")

    # Optional PXE field overrides
    kernel_url = Column(Text, nullable=True)
    initrd_url = Column(Text, nullable=True)
    kernel_args = Column(Text, nullable=True)

    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False)
