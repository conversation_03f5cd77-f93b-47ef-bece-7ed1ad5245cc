from __future__ import annotations
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, Integer, String, DateTime, JSON

from ..db import Base


class Result(Base):
    __tablename__ = "results"

    id = Column(Integer, primary_key=True, index=True)
    asset_number = Column(String(128), index=True, nullable=True)
    mac = Column(String(32), index=True, nullable=True)
    profile_name = Column(String(128), nullable=True)
    test_name = Column(String(128), nullable=False)
    status = Column(String(32), nullable=False)
    metrics = Column(JSON, nullable=True)
    started_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
