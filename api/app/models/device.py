from __future__ import annotations
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime
from ..db import Base


class Device(Base):
    __tablename__ = "devices"

    id = Column(Integer, primary_key=True, index=True)
    mac = Column(String(32), unique=True, nullable=False, index=True)
    uuid = Column(String(64), nullable=True, index=True)
    serial = Column(String(128), nullable=True)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    last_seen = Column(DateTime, default=datetime.utcnow, nullable=False)

    def touch(self):
        now = datetime.utcnow()
        self.updated_at = now
        self.last_seen = now
