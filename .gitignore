# Root-level .gitignore for Nexus
# Covers: Python/FastAPI (api/), Next.js (ui/), Docker/infra, and common OS/editor files

# -----------------------------
# OS / Editor / IDE
# -----------------------------
.DS_Store
Thumbs.db
.idea/
.vscode/
*.swp
*.swo

# -----------------------------
# Environment / Secrets
# -----------------------------
.env
.env.*
*.env
*.secret
secrets.*

# -----------------------------
# Python / FastAPI (api/)
# -----------------------------
__pycache__/
*.py[cod]
*$py.class
.python-version
.venv/
venv/
api/.venv/
api/venv/

# Packaging / build
build/
dist/
.eggs/
*.egg-info/

# Testing / coverage / tooling
.pytest_cache/
.coverage
.coverage.*
htmlcov/
.mypy_cache/
.pyre/
.tox/

# Logs
*.log
logs/

# -----------------------------
# Node / Next.js (ui/)
# Note: ui has its own .gitignore; these are safety nets at repo root
# -----------------------------
node_modules/
ui/node_modules/
.next/
ui/.next/
.next-cache/
ui/.next-cache/
.vercel/
ui/.vercel/
.turbo/
ui/.turbo/

yarn.lock
pnpm-lock.yaml

# -----------------------------
# Docker / Infra artifacts
# -----------------------------
# Local volumes, generated data, and DB files
infra/data/*
!infra/data/.gitkeep
artifacts/*
!artifacts/.gitkeep

# Docker build output
**/docker/*.pid
**/docker/*.cid

# -----------------------------
# Nginx & runtime cache
# -----------------------------
**/nginx/cache/
**/nginx/temp/

# -----------------------------
# Misc
# -----------------------------
.sourcemaps/
*.tgz
