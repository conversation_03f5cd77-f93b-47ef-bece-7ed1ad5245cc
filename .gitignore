# Python bytecode and cache
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments and environment files
.env
.env.*
.venv
venv/
ENV/
.envrc
pip-log.txt
pip-delete-this-directory.txt
pip-wheel-metadata/
.wheelhouse/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
pytestdebug.log
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pyre/
.mypy_cache/

# Jupyter
.ipynb_checkpoints/

# Editors / IDEs
.vscode/
.idea/
*.sublime-* 
*.code-workspace

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Local data / outputs
results/

# Databases
*.sqlite3
*.db
*.db-journal

# Secrets / keys
*.key
*.pem
*.crt
*.p12

# Node / frontend (safe defaults)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnp.*
.pnpm-debug.log*

# Source maps
*.map

# Temporary / swap files
*~
*.swp
*.swo

# Backup files
*.bak
*.tmp
*.temp
