const Stripe = require('stripe');
require('dotenv').config();

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function checkWebhookConfig() {
  try {
    console.log('=== CHECKING WEBHOOK CONFIGURATION ===\n');
    
    // 1. List all webhook endpoints
    console.log('1. Current Webhook Endpoints:');
    const webhooks = await stripe.webhookEndpoints.list();
    
    if (webhooks.data.length === 0) {
      console.log('   ❌ No webhook endpoints found in Stripe Dashboard');
      console.log('   📝 You need to create a webhook endpoint');
      return;
    }
    
    webhooks.data.forEach((webhook, index) => {
      console.log(`\n   Webhook ${index + 1}:`);
      console.log(`   URL: ${webhook.url}`);
      console.log(`   Status: ${webhook.status}`);
      console.log(`   Created: ${new Date(webhook.created * 1000).toLocaleString()}`);
      console.log(`   Events: ${webhook.enabled_events.join(', ')}`);
      
      // Check if URL looks correct
      if (webhook.url.includes('localhost')) {
        console.log('   ⚠️  WARNING: Webhook points to localhost - this won\'t work in production');
      } else if (webhook.url.includes('vercel.app') || webhook.url.includes('.com')) {
        console.log('   ✅ Webhook URL looks correct for production');
      }
      
      // Check if required events are enabled
      const requiredEvents = ['payment_intent.succeeded', 'checkout.session.completed'];
      const missingEvents = requiredEvents.filter(event => !webhook.enabled_events.includes(event));
      
      if (missingEvents.length > 0) {
        console.log(`   ⚠️  Missing required events: ${missingEvents.join(', ')}`);
      } else {
        console.log('   ✅ Required events are configured');
      }
    });
    
    // 2. Check recent webhook deliveries
    console.log('\n2. Recent Webhook Deliveries:');
    
    for (const webhook of webhooks.data) {
      try {
        console.log(`\n   Checking deliveries for: ${webhook.url}`);
        
        // Note: This endpoint might not be available in all Stripe API versions
        // We'll try to get recent events instead
        const events = await stripe.events.list({
          limit: 10,
          types: ['payment_intent.succeeded', 'checkout.session.completed']
        });
        
        console.log(`   Found ${events.data.length} recent payment events:`);
        
        events.data.forEach((event, index) => {
          console.log(`   ${index + 1}. ${event.type} - ${new Date(event.created * 1000).toLocaleString()}`);
          if (event.type === 'payment_intent.succeeded') {
            const pi = event.data.object;
            console.log(`      Payment Intent: ${pi.id} - $${(pi.amount / 100).toFixed(2)}`);
            if (pi.metadata && pi.metadata.type === 'credits') {
              console.log(`      Credit Purchase: ${pi.metadata.creditAmount} credits for user ${pi.metadata.userId}`);
            }
          }
        });
        
      } catch (error) {
        console.log(`   ❌ Could not fetch deliveries: ${error.message}`);
      }
    }
    
    // 3. Test webhook endpoint accessibility
    console.log('\n3. Testing Webhook Endpoint Accessibility:');
    
    for (const webhook of webhooks.data) {
      console.log(`\n   Testing: ${webhook.url}`);
      
      try {
        const response = await fetch(webhook.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'stripe-signature': 'test-signature'
          },
          body: JSON.stringify({ test: true })
        });
        
        console.log(`   Response Status: ${response.status}`);
        
        if (response.status === 400) {
          console.log('   ✅ Endpoint is accessible (signature verification failed as expected)');
        } else if (response.status === 200) {
          console.log('   ⚠️  Endpoint returned 200 - signature verification might be disabled');
        } else {
          console.log(`   ❌ Unexpected response: ${response.status}`);
        }
        
      } catch (error) {
        console.log(`   ❌ Endpoint not accessible: ${error.message}`);
      }
    }
    
    console.log('\n=== TROUBLESHOOTING STEPS ===');
    console.log('1. Check Stripe Dashboard → Webhooks → Your endpoint → Recent deliveries');
    console.log('2. Check Vercel Dashboard → Your project → Functions → Logs');
    console.log('3. Verify STRIPE_WEBHOOK_SECRET matches the signing secret from Stripe Dashboard');
    console.log('4. Make sure your Vercel deployment is live and the webhook URL is correct');
    console.log('5. Try making another test purchase and check the logs immediately');
    
  } catch (error) {
    console.error('Error checking webhook configuration:', error);
  }
}

checkWebhookConfig();
