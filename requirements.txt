fastapi
uvicorn[standard]
pydantic
httpx
rich
psutil
numpy
py-cpuinfo
requests

# --- GUI ---
# --- Hardware/Diagnostics ---
# psutil (already included)
# py-cpuinfo (already included)

# System package requirements (for hardware detection)
# On Fedora/RHEL/CentOS: sudo dnf install lshw pciutils smartmontools
# On Debian/Ubuntu: sudo apt-get install lshw pciutils smartmontools

# --- Testing ---
pytest

# --- Optional: For Windows support ---
# wmi;pywin32

# --- CLI tools required on Linux (install via apt/yum, not pip):
# lsblk, smartctl, nwipe, hdparm, nvme-cli, blkdiscard, blockdev, dd, sha256sum
# Example install on Ubuntu/Debian:
# sudo apt-get install lsblk smartmontools nwipe hdparm nvme-cli util-linux coreutils
