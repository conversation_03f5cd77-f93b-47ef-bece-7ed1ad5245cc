import { Suspense } from 'react';
import LoginContent from './LoginContent';

// Ensure this page is rendered dynamically to avoid prerender errors with useSearchParams
export const dynamic = 'force-dynamic';

export default function LoginPage() {
  return (
    <Suspense fallback={<div className="container mx-auto max-w-md px-6 py-32"><p>Loading…</p></div>}>
      <LoginContent />
    </Suspense>
  );
}
