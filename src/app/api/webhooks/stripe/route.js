import { NextResponse } from 'next/server';
import Strip<PERSON> from 'stripe';
import { prisma } from '@/lib/db';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Ensure this route runs on Node.js (not edge) and is not statically optimized
export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request) {
  const body = await request.text();
  const signature = request.headers.get('stripe-signature');

  let event;

  try {
    event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSuccess(event.data.object);
        break;
      
      case 'invoice.payment_succeeded':
        await handleSubscriptionPayment(event.data.object);
        break;
      
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdate(event.data.object);
        break;
      
      case 'customer.subscription.deleted':
        await handleSubscriptionCancellation(event.data.object);
        break;
      
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
      
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 });
  }
}

function fromEpochSeconds(sec) {
  if (typeof sec !== 'number' || !Number.isFinite(sec)) return null;
  const d = new Date(sec * 1000);
  return isNaN(d.getTime()) ? null : d;
}

async function handlePaymentSuccess(paymentIntent) {
  const { id, amount, currency, metadata } = paymentIntent;
  
  if (metadata.type === 'credits') {
    // Handle credit purchase
    const userId = metadata.userId;
    if (!userId || userId === 'guest') {
      console.log('Skipping credit DB write for guest/unknown user');
      return;
    }
    const creditAmount = parseInt(metadata.creditAmount);
    const price = amount / 100; // Convert from cents

    await prisma.$transaction(async (tx) => {
      // Create transaction record
      const transaction = await tx.transaction.create({
        data: {
          userId,
          type: 'CREDIT_PURCHASE',
          amount: price,
          currency,
          status: 'COMPLETED',
          stripePaymentIntentId: id,
          metadata: {
            creditAmount,
            productId: metadata.productId
          }
        }
      });

      // Add credits to user account
      await tx.credit.create({
        data: {
          userId,
          amount: creditAmount,
          remainingAmount: creditAmount,
          purchasePrice: price,
          transactionId: transaction.id,
          expiresAt: null // Credits never expire
        }
      });
    });

    console.log(`Added ${creditAmount} credits for user ${userId}`);
  }
  
  if (metadata.type === 'license') {
    // Handle perpetual license purchase
    const userId = metadata.userId;
    if (!userId || userId === 'guest') {
      console.log('Skipping license DB write for guest/unknown user');
      return;
    }
    const licenseType = metadata.licenseType;
    const price = amount / 100;

    await prisma.$transaction(async (tx) => {
      // Create transaction record
      const transaction = await tx.transaction.create({
        data: {
          userId,
          type: 'LICENSE_PURCHASE',
          amount: price,
          currency,
          status: 'COMPLETED',
          stripePaymentIntentId: id,
          metadata: {
            licenseType,
            productId: metadata.productId
          }
        }
      });

      // Generate license key
      const licenseKey = generateLicenseKey();
      
      // Create license record
      await tx.license.create({
        data: {
          userId,
          licenseKey,
          licenseType: licenseType === 'industrial' ? 'INDUSTRIAL_SERVER' : 'INDUSTRIAL_MAINTENANCE',
          serverCount: 1,
          transactionId: transaction.id,
          maintenanceExpiry: licenseType === 'maintenance' ? 
            new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) : null // 1 year from now
        }
      });
    });

    console.log(`Created ${licenseType} license for user ${userId}`);
  }
}

async function handleSubscriptionPayment(invoice) {
  const subscriptionId = invoice.subscription;
  const customerId = invoice.customer;
  const amount = invoice.amount_paid / 100;

  // Find user by Stripe customer ID
  const subscription = await prisma.subscription.findFirst({
    where: { stripeSubscriptionId: subscriptionId },
    include: { user: true }
  });

  if (subscription) {
    // Create transaction record for subscription payment
    await prisma.transaction.create({
      data: {
        userId: subscription.userId,
        type: 'SUBSCRIPTION_PAYMENT',
        amount,
        currency: invoice.currency,
        status: 'COMPLETED',
        stripeInvoiceId: invoice.id,
        metadata: {
          subscriptionId: subscriptionId,
          planType: subscription.planType
        }
      }
    });

    console.log(`Recorded subscription payment for user ${subscription.userId}`);
  }
}

async function handleSubscriptionUpdate(stripeSubscription) {
  const { id, customer, status, current_period_start, current_period_end, cancel_at_period_end, metadata } = stripeSubscription;

  const startDate = fromEpochSeconds(current_period_start);
  const endDate = fromEpochSeconds(current_period_end);

  // Find existing subscription or create new one
  const existingSubscription = await prisma.subscription.findUnique({
    where: { stripeSubscriptionId: id }
  });

  if (existingSubscription) {
    // Update existing subscription
    await prisma.subscription.update({
      where: { stripeSubscriptionId: id },
      data: {
        status: mapStripeStatus(status),
        ...(startDate ? { currentPeriodStart: startDate } : {}),
        ...(endDate ? { currentPeriodEnd: endDate } : {}),
        cancelAtPeriodEnd: cancel_at_period_end
      }
    });
  } else if (metadata.userId && metadata.userId !== 'guest') {
    // Create new subscription
    if (!startDate || !endDate) {
      console.log('Skipping subscription create due to invalid period dates');
      return;
    }
    await prisma.subscription.create({
      data: {
        userId: metadata.userId,
        stripeSubscriptionId: id,
        planType: metadata.planType === 'pro' ? 'PRO' : 'BUSINESS',
        workstations: metadata.planType === 'pro' ? 5 : 15,
        status: mapStripeStatus(status),
        currentPeriodStart: startDate,
        currentPeriodEnd: endDate,
        cancelAtPeriodEnd: cancel_at_period_end
      }
    });
  }

  console.log(`Updated subscription ${id} with status ${status}`);
}

async function handleSubscriptionCancellation(stripeSubscription) {
  const { id } = stripeSubscription;

  try {
    await prisma.subscription.update({
      where: { stripeSubscriptionId: id },
      data: {
        status: 'CANCELED',
        cancelAtPeriodEnd: true
      }
    });
  } catch (e) {
    // If not found, ignore in test mode/guest flows
    console.log('Subscription not found for cancellation, skipping');
  }

  console.log(`Canceled subscription ${id}`);
}

async function handleCheckoutCompleted(session) {
  const { id, customer, metadata, mode } = session;
  
  if (mode === 'subscription' && metadata.userId) {
    // Subscription checkout completed - the subscription webhook will handle the details
    console.log(`Subscription checkout completed for user ${metadata.userId}`);
  }
  
  if (mode === 'payment' && metadata.userId) {
    // One-time payment completed - handled by payment_intent.succeeded
    console.log(`Payment checkout completed for user ${metadata.userId}`);
  }
}

function mapStripeStatus(stripeStatus) {
  const statusMap = {
    'active': 'ACTIVE',
    'past_due': 'PAST_DUE',
    'canceled': 'CANCELED',
    'incomplete': 'INCOMPLETE',
    'trialing': 'TRIALING'
  };
  
  return statusMap[stripeStatus] || 'INCOMPLETE';
}

function generateLicenseKey() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  const segments = [];
  
  for (let i = 0; i < 4; i++) {
    let segment = '';
    for (let j = 0; j < 4; j++) {
      segment += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    segments.push(segment);
  }
  
  return segments.join('-');
}
