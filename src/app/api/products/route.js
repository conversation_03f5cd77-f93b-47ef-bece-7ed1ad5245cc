import { NextResponse } from 'next/server';
import { CREDIT_PACKS, SUBSCRIPTION_PLANS, PERPETUAL_LICENSES } from '@/lib/products';

export async function GET() {
  try {
    const products = {
      creditPacks: CREDIT_PACKS,
      subscriptionPlans: SUBSCRIPTION_PLANS,
      perpetualLicenses: PERPETUAL_LICENSES
    };

    return NextResponse.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
