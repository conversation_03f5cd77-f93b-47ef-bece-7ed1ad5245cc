import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateApiRequest, logApiUsage, getClientIp } from '@/lib/api-auth';

export const runtime = 'nodejs';

export async function GET(request) {
  try {
    // Authenticate API key
    const apiKeyRecord = await authenticateApiRequest(request);
    if (!apiKeyRecord) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch all licenses for the user
    const licenses = await prisma.license.findMany({
      where: { userId: apiKeyRecord.userId },
      select: {
        id: true,
        licenseType: true,
        serverCount: true,
        maintenanceExpiry: true,
        activatedAt: true,
        createdAt: true,
      }
    });

    // Build summary
    const now = new Date();
    const maintenanceLicenses = licenses.filter(l => l.licenseType === 'INDUSTRIAL_MAINTENANCE' && l.maintenanceExpiry && l.maintenanceExpiry > now);
    const maintenanceActive = maintenanceLicenses.length > 0;
    const maintenanceExpiry = maintenanceActive
      ? new Date(Math.max(...maintenanceLicenses.map(l => l.maintenanceExpiry.getTime())))
      : null;

    const serverLicenses = licenses.filter(l => l.licenseType === 'INDUSTRIAL_SERVER');
    const totalServerCount = serverLicenses.reduce((sum, l) => sum + (l.serverCount || 0), 0);
    const hasServerLicense = serverLicenses.length > 0;

    // Log usage
    await logApiUsage({
      apiKeyId: apiKeyRecord.id,
      action: 'license_status',
      ipAddress: getClientIp(request),
      userAgent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      licenseSummary: {
        hasServerLicense,
        totalServerCount,
        maintenanceActive,
        maintenanceExpiry,
      },
      licenses,
      user: {
        id: apiKeyRecord.user.id,
        email: apiKeyRecord.user.email,
        companyName: apiKeyRecord.user.companyName,
      }
    });
  } catch (err) {
    console.error('License status error:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
