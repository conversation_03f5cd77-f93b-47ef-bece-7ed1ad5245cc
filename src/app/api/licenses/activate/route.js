import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateApiRequest, logApiUsage, getClientIp } from '@/lib/api-auth';

export const runtime = 'nodejs';

export async function POST(request) {
  try {
    const apiKeyRecord = await authenticateApiRequest(request);
    if (!apiKeyRecord) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let body;
    try {
      body = await request.json();
    } catch (_) {
      body = {};
    }

    const { licenseKey } = body;
    if (!licenseKey || typeof licenseKey !== 'string') {
      return NextResponse.json({ error: 'Missing or invalid licenseKey' }, { status: 400 });
    }

    // Find license by key, belonging to the authenticated user
    const license = await prisma.license.findFirst({
      where: { userId: apiKeyRecord.userId, licenseKey },
    });

    if (!license) {
      return NextResponse.json({ error: 'License not found' }, { status: 404 });
    }

    let updated = license;
    if (!license.activatedAt) {
      updated = await prisma.license.update({
        where: { id: license.id },
        data: { activatedAt: new Date() },
      });
    }

    await logApiUsage({
      apiKeyId: apiKeyRecord.id,
      action: 'license_activate',
      metadata: { licenseId: updated.id, licenseType: updated.licenseType },
      ipAddress: getClientIp(request),
      userAgent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      license: {
        id: updated.id,
        licenseKey: updated.licenseKey,
        licenseType: updated.licenseType,
        serverCount: updated.serverCount,
        maintenanceExpiry: updated.maintenanceExpiry,
        activatedAt: updated.activatedAt,
        createdAt: updated.createdAt,
      }
    });
  } catch (err) {
    console.error('License activation error:', err);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
