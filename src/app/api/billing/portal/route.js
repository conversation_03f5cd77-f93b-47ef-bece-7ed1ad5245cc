import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { requireAuth } from '@/lib/auth';

export const runtime = 'nodejs';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request) {
  try {
    const user = await requireAuth(request);

    let body = {};
    try {
      body = await request.json();
    } catch (_) {
      body = {};
    }

    // Find existing Stripe customer by email; create if not found
    let customerId;
    try {
      const customers = await stripe.customers.list({ email: user.email, limit: 1 });
      if (customers.data.length > 0) {
        customerId = customers.data[0].id;
      }
    } catch (_) {
      // ignore lookup errors; we'll attempt to create instead
    }

    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        name: [user.firstName, user.lastName].filter(Boolean).join(' '),
        metadata: { userId: user.id },
      });
      customerId = customer.id;
    }

    const returnUrl = body.returnUrl || `${process.env.NEXT_PUBLIC_APP_URL}/account`;

    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    return NextResponse.json({ url: session.url });
  } catch (error) {
    if (error?.message === 'Authentication required' || error?.message === 'Invalid token') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.error('Billing portal error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
