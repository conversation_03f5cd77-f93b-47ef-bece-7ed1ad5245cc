import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateApiRequest, logApiUsage, getClientIp } from '@/lib/api-auth';

export const runtime = 'nodejs';

export async function GET(request) {
  try {
    // Authenticate the API request
    const apiKeyRecord = await authenticateApiRequest(request);
    if (!apiKeyRecord) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get credit balance for the user
    const creditAgg = await prisma.credit.aggregate({
      where: { 
        userId: apiKeyRecord.userId,
        remainingAmount: { gt: 0 }
      },
      _sum: { remainingAmount: true }
    });

    const balance = creditAgg._sum.remainingAmount || 0;

    // Get active subscription if any
    const subscription = await prisma.subscription.findFirst({
      where: { 
        userId: apiKeyRecord.userId,
        status: 'ACTIVE'
      },
      select: {
        planType: true,
        workstations: true,
        currentPeriodEnd: true
      }
    });

    // Get license summary and list
    const licenses = await prisma.license.findMany({
      where: { userId: apiKeyRecord.userId },
      select: {
        id: true,
        licenseType: true,
        serverCount: true,
        maintenanceExpiry: true,
        activatedAt: true
      }
    });

    const now = new Date();
    const maintenanceLicenses = licenses.filter(l => l.licenseType === 'INDUSTRIAL_MAINTENANCE' && l.maintenanceExpiry && l.maintenanceExpiry > now);
    const maintenanceActive = maintenanceLicenses.length > 0;
    const maintenanceExpiry = maintenanceActive
      ? new Date(Math.max(...maintenanceLicenses.map(l => l.maintenanceExpiry.getTime())))
      : null;
    const serverLicenses = licenses.filter(l => l.licenseType === 'INDUSTRIAL_SERVER');
    const totalServerCount = serverLicenses.reduce((sum, l) => sum + (l.serverCount || 0), 0);
    const hasServerLicense = serverLicenses.length > 0;

    // Log the API usage
    await logApiUsage({
      apiKeyId: apiKeyRecord.id,
      action: 'check_balance',
      ipAddress: getClientIp(request),
      userAgent: request.headers.get('user-agent')
    });

    return NextResponse.json({
      balance,
      subscription: subscription || null,
      licenseSummary: {
        hasServerLicense,
        totalServerCount,
        maintenanceActive,
        maintenanceExpiry
      },
      licenses: licenses.map(l => ({
        id: l.id,
        licenseType: l.licenseType,
        serverCount: l.serverCount,
        maintenanceExpiry: l.maintenanceExpiry,
        activatedAt: l.activatedAt
      })),
      user: {
        id: apiKeyRecord.user.id,
        email: apiKeyRecord.user.email,
        companyName: apiKeyRecord.user.companyName
      }
    });
  } catch (error) {
    console.error('Balance check error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
