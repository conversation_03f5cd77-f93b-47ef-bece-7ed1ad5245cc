import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { authenticateApiRequest, logApiUsage, getClientIp } from '@/lib/api-auth';

export const runtime = 'nodejs';

// Credit costs per operation type
const OPERATION_COSTS = {
  'STANDARD_WIPE': 1,
  'WIPE_WITH_DIAGNOSTICS': 2,
  'DIAGNOSTICS_ONLY': 1
};

export async function POST(request) {
  try {
    // Authenticate the API request
    const apiKeyRecord = await authenticateApiRequest(request);
    if (!apiKeyRecord) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { 
      operationType, 
      deviceSerial, 
      deviceModel,
      metadata = {}
    } = body;

    // Validate operation type
    if (!operationType || !OPERATION_COSTS[operationType]) {
      return NextResponse.json({ 
        error: 'Invalid operation type. Must be one of: STANDARD_WIPE, WIPE_WITH_DIAGNOSTICS, DIAGNOSTICS_ONLY' 
      }, { status: 400 });
    }

    const creditsNeeded = OPERATION_COSTS[operationType];

    // Start a transaction to ensure atomicity
    const result = await prisma.$transaction(async (tx) => {
      // Check available credits
      const availableCredits = await tx.credit.findMany({
        where: {
          userId: apiKeyRecord.userId,
          remainingAmount: { gt: 0 }
        },
        orderBy: [
          { expiresAt: 'asc' }, // Use expiring credits first
          { createdAt: 'asc' }  // Then oldest credits
        ]
      });

      const totalAvailable = availableCredits.reduce((sum, c) => sum + c.remainingAmount, 0);
      
      if (totalAvailable < creditsNeeded) {
        throw new Error(`Insufficient credits. Required: ${creditsNeeded}, Available: ${totalAvailable}`);
      }

      // Create operation record
      const operation = await tx.operation.create({
        data: {
          userId: apiKeyRecord.userId,
          type: operationType,
          status: 'IN_PROGRESS',
          creditsUsed: creditsNeeded,
          deviceSerial,
          deviceModel,
          startedAt: new Date(),
          metadata
        }
      });

      // Consume credits
      let creditsToConsume = creditsNeeded;
      for (const credit of availableCredits) {
        if (creditsToConsume <= 0) break;
        
        const consumed = Math.min(credit.remainingAmount, creditsToConsume);
        await tx.credit.update({
          where: { id: credit.id },
          data: { 
            remainingAmount: credit.remainingAmount - consumed 
          }
        });
        
        creditsToConsume -= consumed;
      }

      // Log the usage
      await tx.usageLog.create({
        data: {
          apiKeyId: apiKeyRecord.id,
          operationId: operation.id,
          action: 'consume_credit',
          creditsUsed: creditsNeeded,
          deviceSerial,
          deviceModel,
          metadata,
          ipAddress: getClientIp(request),
          userAgent: request.headers.get('user-agent')
        }
      });

      return operation;
    });

    // Calculate remaining balance
    const creditAgg = await prisma.credit.aggregate({
      where: { 
        userId: apiKeyRecord.userId,
        remainingAmount: { gt: 0 }
      },
      _sum: { remainingAmount: true }
    });

    return NextResponse.json({
      success: true,
      operationId: result.id,
      creditsConsumed: result.creditsUsed,
      remainingBalance: creditAgg._sum.remainingAmount || 0,
      operation: {
        id: result.id,
        type: result.type,
        status: result.status,
        deviceSerial: result.deviceSerial,
        deviceModel: result.deviceModel,
        startedAt: result.startedAt
      }
    });

  } catch (error) {
    console.error('Credit consumption error:', error);
    
    // Check if it's an insufficient credits error
    if (error.message?.includes('Insufficient credits')) {
      return NextResponse.json({ 
        error: error.message 
      }, { status: 402 }); // Payment Required
    }

    return NextResponse.json({ 
      error: 'Failed to consume credits' 
    }, { status: 500 });
  }
}

// Complete or fail an operation
export async function PATCH(request) {
  try {
    const apiKeyRecord = await authenticateApiRequest(request);
    if (!apiKeyRecord) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { operationId, status, errorMessage = null, metadata = {} } = body;

    if (!operationId || !['COMPLETED', 'FAILED', 'CANCELED'].includes(status)) {
      return NextResponse.json({ 
        error: 'Invalid request. Provide operationId and status (COMPLETED, FAILED, or CANCELED)' 
      }, { status: 400 });
    }

    // Verify operation exists and belongs to the authenticated user
    const existing = await prisma.operation.findUnique({
      where: { id: operationId },
      select: { id: true, userId: true, creditsUsed: true }
    });

    if (!existing || existing.userId !== apiKeyRecord.userId) {
      return NextResponse.json({ error: 'Operation not found' }, { status: 404 });
    }

    // Update operation status
    const operation = await prisma.operation.update({
      where: { id: operationId },
      data: {
        status,
        completedAt: new Date(),
        errorMessage,
        metadata: {
          ...metadata,
          ...(errorMessage ? { error: errorMessage } : {})
        }
      }
    });

    // If operation failed or was canceled, refund the credits
    if (status === 'FAILED' || status === 'CANCELED') {
      await prisma.$transaction(async (tx) => {
        // Create a refund credit entry
        await tx.credit.create({
          data: {
            userId: apiKeyRecord.userId,
            amount: operation.creditsUsed,
            remainingAmount: operation.creditsUsed,
            purchasePrice: 0, // Refund
            transactionId: null,
            expiresAt: null
          }
        });

        // Log the refund
        await tx.usageLog.create({
          data: {
            apiKeyId: apiKeyRecord.id,
            operationId: operation.id,
            action: 'refund_credit',
            creditsUsed: -operation.creditsUsed, // Negative for refund
            metadata: { reason: status === 'FAILED' ? 'operation_failed' : 'operation_canceled' },
            ipAddress: getClientIp(request),
            userAgent: request.headers.get('user-agent')
          }
        });
      });
    }

    return NextResponse.json({
      success: true,
      operation: {
        id: operation.id,
        status: operation.status,
        creditsUsed: operation.creditsUsed,
        refunded: status === 'FAILED' || status === 'CANCELED'
      }
    });

  } catch (error) {
    console.error('Operation update error:', error);
    return NextResponse.json({ 
      error: 'Failed to update operation' 
    }, { status: 500 });
  }
}
