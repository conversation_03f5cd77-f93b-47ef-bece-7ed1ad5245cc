import { NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { requireAuth } from '@/lib/auth';
import { generateApiKey } from '@/lib/api-auth';

export const runtime = 'nodejs';

// List all API keys for the authenticated user
export async function GET(request) {
  try {
    const user = await requireAuth(request);
    
    const apiKeys = await prisma.apiKey.findMany({
      where: { userId: user.id },
      select: {
        id: true,
        name: true,
        lastFourChars: true,
        isActive: true,
        lastUsedAt: true,
        createdAt: true,
        expiresAt: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return NextResponse.json({ apiKeys });
  } catch (error) {
    if (error?.message === 'Authentication required' || error?.message === 'Invalid token') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.error('API key list error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Create a new API key
export async function POST(request) {
  try {
    const user = await requireAuth(request);
    
    const body = await request.json();
    const { name, expiresInDays = null } = body;

    if (!name || name.trim().length === 0) {
      return NextResponse.json({ error: 'API key name is required' }, { status: 400 });
    }

    // Generate the API key
    const { rawKey, keyHash, lastFourChars } = generateApiKey();

    // Calculate expiration if provided
    const expiresAt = expiresInDays 
      ? new Date(Date.now() + expiresInDays * 24 * 60 * 60 * 1000)
      : null;

    // Store the hashed key
    const apiKey = await prisma.apiKey.create({
      data: {
        userId: user.id,
        name: name.trim(),
        keyHash,
        lastFourChars,
        expiresAt
      },
      select: {
        id: true,
        name: true,
        lastFourChars: true,
        createdAt: true,
        expiresAt: true
      }
    });

    return NextResponse.json({
      apiKey: {
        ...apiKey,
        key: rawKey // Only returned once!
      }
    });
  } catch (error) {
    if (error?.message === 'Authentication required' || error?.message === 'Invalid token') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.error('API key creation error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Delete/revoke an API key
export async function DELETE(request) {
  try {
    const user = await requireAuth(request);
    
    const { searchParams } = new URL(request.url);
    const keyId = searchParams.get('id');

    if (!keyId) {
      return NextResponse.json({ error: 'API key ID is required' }, { status: 400 });
    }

    // Delete the key (only if it belongs to the user)
    await prisma.apiKey.deleteMany({
      where: {
        id: keyId,
        userId: user.id
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    if (error?.message === 'Authentication required' || error?.message === 'Invalid token') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    console.error('API key deletion error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
