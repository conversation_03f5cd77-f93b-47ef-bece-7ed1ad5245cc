'use client';

import { useState } from 'react';
import Button from '@/components/Button';
import Card from '@/components/Card';
import { useRouter } from 'next/navigation';

export default function Pricing() {
  const [activeTab, setActiveTab] = useState('credits');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const creditPacks = [
    {
      name: "100 Credits",
      price: "$450",
      pricePerCredit: "$4.50",
      credits: 100,
      description: "Perfect for small operations or trying out the service",
      features: [
        "100 operations (wipes/diagnostics)",
        "Credits never expire",
        "Certificate generation",
        "NIST 800-88 compliance",
        "Email support",
        "Instant activation"
      ],
      cta: "Buy Credits",
      popular: false,
      savings: null
    },
    {
      name: "500 Credits",
      price: "$2,000",
      pricePerCredit: "$4.00",
      credits: 500,
      description: "Best value for growing ITAD operations",
      features: [
        "500 operations (wipes/diagnostics)",
        "Credits never expire",
        "Priority certificate generation",
        "Advanced reporting",
        "Priority email support",
        "Bulk operation discounts"
      ],
      cta: "Buy Credits",
      popular: true,
      savings: "$250 savings"
    },
    {
      name: "1000 Credits",
      price: "$3,500",
      pricePerCredit: "$3.50",
      credits: 1000,
      description: "Maximum savings for high-volume operations",
      features: [
        "1000 operations (wipes/diagnostics)",
        "Credits never expire",
        "Premium support",
        "Custom reporting",
        "API access",
        "Dedicated account manager"
      ],
      cta: "Buy Credits",
      popular: false,
      savings: "$1,000 savings"
    }
  ];

  const subscriptionPlans = [
    {
      name: "Pro",
      price: "$249",
      period: "per month",
      workstations: "5 workstations",
      description: "Perfect for small to medium ITAD operations",
      features: [
        "Up to 5 workstations",
        "Unlimited wipes & diagnostics",
        "Cloud reporting dashboard",
        "Email support",
        "NIST 800-88 compliance",
        "Certificate generation"
      ],
      cta: "Start Subscription",
      popular: false
    },
    {
      name: "Business",
      price: "$499",
      period: "per month",
      workstations: "15 workstations",
      description: "Ideal for growing teams with advanced workflow needs",
      features: [
        "Up to 15 workstations",
        "Unlimited wipes & diagnostics",
        "Advanced cloud reporting",
        "Priority support",
        "API access",
        "ERP integration",
        "Custom workflows",
        "Advanced analytics"
      ],
      cta: "Start Subscription",
      popular: true
    },
    {
      name: "Enterprise",
      price: "Custom",
      period: "enterprise solution",
      workstations: "Unlimited",
      description: "Complete solution for large-scale operations",
      features: [
        "Unlimited workstations",
        "White-label options",
        "On-premise deployment",
        "24/7 dedicated support",
        "Custom development",
        "SLA guarantee",
        "Dedicated account manager",
        "Custom compliance certifications"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const perpetualLicenses = [
    {
      name: "Industrial Server",
      price: "$4,999",
      period: "one-time purchase",
      description: "Unlimited server operations with best TCO for high-volume",
      features: [
        "Unlimited server operations",
        "Bulk hard drive processing",
        "Network deployment",
        "Certificate generation",
        "NIST 800-88 compliance",
        "Basic support included"
      ],
      cta: "Buy License",
      popular: true
    },
    {
      name: "Support & Maintenance",
      price: "$499",
      period: "per year (optional)",
      description: "Ongoing support and updates for perpetual licenses",
      features: [
        "Software updates",
        "New compliance standards",
        "Priority technical support",
        "Feature requests",
        "Bug fixes",
        "Phone support"
      ],
      cta: "Add Support",
      popular: false
    }
  ];

  const getCurrentTiers = () => {
    switch (activeTab) {
      case 'credits':
        return creditPacks;
      case 'subscription':
        return subscriptionPlans;
      case 'perpetual':
        return perpetualLicenses;
      default:
        return creditPacks;
    }
  };

  const startCheckout = async (params) => {
    try {
      setLoading(true);
      const res = await fetch('/api/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: params.type,
          productId: params.productId,
          // Let API use defaults if not provided
        })
      });
      if (res.status === 401) {
        router.push(`/login?next=${encodeURIComponent('/pricing')}`);
        return;
      }
      if (!res.ok) {
        const err = await res.json().catch(() => ({ error: 'Checkout failed' }));
        throw new Error(err.error || 'Checkout failed');
      }
      const data = await res.json();
      if (data.url) {
        window.location.href = data.url;
      } else if (data.sessionId) {
        // Fallback: redirect to Stripe-hosted page if URL is not provided
        window.location.href = `https://checkout.stripe.com/pay/${data.sessionId}`;
      } else {
        throw new Error('Invalid checkout response');
      }
    } catch (e) {
      alert(e.message || 'Unable to start checkout');
    } finally {
      setLoading(false);
    }
  };

  const productIdForTier = (tierName) => {
    if (activeTab === 'credits') {
      switch (tierName) {
        case '100 Credits':
          return 'credits_100';
        case '500 Credits':
          return 'credits_500';
        case '1000 Credits':
          return 'credits_1000';
        default:
          return null;
      }
    }
    if (activeTab === 'subscription') {
      switch (tierName) {
        case 'Pro':
          return 'pro';
        case 'Business':
          return 'business';
        default:
          return null;
      }
    }
    if (activeTab === 'perpetual') {
      switch (tierName) {
        case 'Industrial Server':
          return 'industrial_server';
        case 'Support & Maintenance':
          return 'maintenance';
        default:
          return null;
      }
    }
    return null;
  };

  const handlePurchase = async (tier) => {
    if (tier.cta === 'Contact Sales') {
      router.push('/contact');
      return;
    }

    const productId = productIdForTier(tier.name);
    if (!productId) {
      alert('Unknown product selection');
      return;
    }

    const type =
      activeTab === 'credits'
        ? 'credits'
        : activeTab === 'subscription'
          ? 'subscription'
          : 'license';

    await startCheckout({ type, productId });
  };

  const getTabTitle = () => {
    switch (activeTab) {
      case 'credits':
        return 'Pay-Per-Use Credits';
      case 'subscription':
        return 'Monthly Subscriptions';
      case 'perpetual':
        return 'Perpetual Licenses';
      default:
        return 'Pay-Per-Use Credits';
    }
  };

  return (
    <div className="min-h-screen bg-black text-white py-16">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold mb-4 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">
            ITAD Software Pricing
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Transparent pricing designed for ITAD operators, refurbishers, and MSPs. 
            Choose the model that fits your operation.
          </p>
        </div>

        {/* Pricing Model Tabs */}
        <div className="flex justify-center mb-12">
          <div className="bg-gray-800 p-1 rounded-lg inline-flex">
            <button
              onClick={() => setActiveTab('credits')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === 'credits'
                  ? 'bg-cyan-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Pay-Per-Use Credits
            </button>
            <button
              onClick={() => setActiveTab('subscription')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === 'subscription'
                  ? 'bg-cyan-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Subscriptions
            </button>
            <button
              onClick={() => setActiveTab('perpetual')}
              className={`px-6 py-3 rounded-md text-sm font-medium transition-all ${
                activeTab === 'perpetual'
                  ? 'bg-cyan-600 text-white shadow-lg'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              Perpetual Licenses
            </button>
          </div>
        </div>

        {/* Model Description */}
        <div className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-cyan-300 mb-2">{getTabTitle()}</h2>
          {activeTab === 'credits' && (
            <p className="text-gray-400">
              Credits never expire. Perfect for fluctuating volumes or trying our service.
            </p>
          )}
          {activeTab === 'subscription' && (
            <p className="text-gray-400">
              Unlimited operations based on workstations, not per-asset fees.
            </p>
          )}
          {activeTab === 'perpetual' && (
            <p className="text-gray-400">
              One-time purchase with the best TCO for high-volume operations.
            </p>
          )}
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {getCurrentTiers().map((tier, index) => (
            <Card key={index} className="relative transition-all hover:scale-105">
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-cyan-500 to-sky-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}
              
              <div className="p-6">
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                  <div className="flex items-baseline mb-2">
                    <span className="text-4xl font-bold text-white">{tier.price}</span>
                    {tier.period && (
                      <span className="text-gray-300 ml-2">{tier.period}</span>
                    )}
                  </div>
                  {tier.pricePerCredit && (
                    <p className="text-cyan-300 text-sm">{tier.pricePerCredit} per credit</p>
                  )}
                  {tier.workstations && (
                    <p className="text-cyan-300 text-sm">{tier.workstations}</p>
                  )}
                  {tier.savings && (
                    <p className="text-green-400 text-sm font-semibold">{tier.savings}</p>
                  )}
                  <p className="text-gray-300 mt-2">{tier.description}</p>
                </div>

                <ul className="space-y-3 mb-8">
                  {tier.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start text-gray-300">
                      <span className="w-2 h-2 bg-cyan-400 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                      {feature}
                    </li>
                  ))}
                </ul>

                <Button 
                  variant={tier.popular ? 'primary' : 'secondary'} 
                  className="w-full"
                  disabled={loading}
                  onClick={() => handlePurchase(tier)}
                >
                  {loading ? 'Processing…' : tier.cta}
                </Button>
              </div>
            </Card>
          ))}
        </div>

        {/* Free Trial CTA */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-cyan-900/50 to-sky-900/50 rounded-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Start with 20 Free Credits
            </h3>
            <p className="text-gray-300 mb-6">
              Try our software risk-free. No credit card required. 
              Experience the quality and efficiency for yourself.
            </p>
            <Button variant="primary" size="lg">
              Get Free Trial
            </Button>
          </div>
        </div>

        {/* Competitive Advantages */}
        <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">💎</span>
            </div>
            <h4 className="text-xl font-semibold text-white mb-2">Credits Never Expire</h4>
            <p className="text-gray-400">Unlike competitors, your credits are yours forever. No use-it-or-lose-it pressure.</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📊</span>
            </div>
            <h4 className="text-xl font-semibold text-white mb-2">Transparent Pricing</h4>
            <p className="text-gray-400">No hidden fees or &quot;contact us&quot; pricing. See exactly what you pay upfront.</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-cyan-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎯</span>
            </div>
            <h4 className="text-xl font-semibold text-white mb-2">Asset Value Maximization</h4>
            <p className="text-gray-400">Our diagnostics help you accurately grade hardware to maximize resale value.</p>
          </div>
        </div>
      </div>
    </div>
  );
}