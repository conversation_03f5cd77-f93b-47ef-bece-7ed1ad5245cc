import { Suspense } from 'react';
import SuccessContent from './SuccessContent';

// Ensure dynamic rendering to support useSearchParams without prerender issues
export const dynamic = 'force-dynamic';

export default function Success() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-black text-white flex items-center justify-center"><p>Loading…</p></div>}>
      <SuccessContent />
    </Suspense>
  );
}