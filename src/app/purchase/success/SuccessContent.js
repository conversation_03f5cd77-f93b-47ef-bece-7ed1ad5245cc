'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Button from '@/components/Button';
import Card from '@/components/Card';

export default function SuccessContent() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // In a real application, you would verify the session with <PERSON><PERSON>
    // For now, we'll just simulate the verification
    if (sessionId) {
      setLoading(false);
    } else {
      setError('Invalid session');
      setLoading(false);
    }
  }, [sessionId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto mb-4"></div>
          <p className="text-gray-300">Verifying your purchase...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <Card className="max-w-md w-full text-center">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-white mb-4">Payment Error</h1>
          <p className="text-gray-300 mb-6">{error}</p>
          <Link href="/pricing">
            <Button variant="primary">Back to Pricing</Button>
          </Link>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-cyan-300 text-6xl mb-6">✓</div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">
            Welcome to Nexus Suite!
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8">
            Your payment was successful. Thank you for choosing Nexus Suite!
          </p>
        </div>
      </section>

      {/* Success Details */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-6">What&apos;s Next?</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="p-4 bg-gray-800 rounded-lg">
                <div className="text-cyan-300 text-2xl mb-2">📧</div>
                <h3 className="text-lg font-semibold text-white mb-2">Check Your Email</h3>
                <p className="text-gray-300 text-sm">We&apos;ve sent your login credentials and setup instructions</p>
              </div>

              <div className="p-4 bg-gray-800 rounded-lg">
                <div className="text-cyan-300 text-2xl mb-2">🚀</div>
                <h3 className="text-lg font-semibold text-white mb-2">Get Started</h3>
                <p className="text-gray-300 text-sm">Access your dashboard and start exploring Nexus Suite</p>
              </div>

              <div className="p-4 bg-gray-800 rounded-lg">
                <div className="text-cyan-300 text-2xl mb-2">💬</div>
                <h3 className="text-lg font-semibold text-white mb-2">Need Help?</h3>
                <p className="text-gray-300 text-sm">Our support team is here to help you get started</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/dashboard">
                <Button variant="primary" size="lg">
                  Access Dashboard
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="secondary" size="lg">
                  Contact Support
                </Button>
              </Link>
            </div>
          </Card>
        </div>
      </section>

      {/* Features Preview */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-900">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-12">
            Start Exploring Your New Tools
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Link href="/features#nexus" className="block">
              <Card className="p-6 hover:scale-105 transition-transform duration-300 cursor-pointer">
                <div className="text-3xl mb-4">🚀</div>
                <h3 className="text-lg font-semibold text-white mb-2">Nexus OS</h3>
                <p className="text-gray-300 text-sm">Your AI-powered operating system</p>
              </Card>
            </Link>

            <Link href="/features#foundry" className="block">
              <Card className="p-6 hover:scale-105 transition-transform duration-300 cursor-pointer">
                <div className="text-3xl mb-4">⚒️</div>
                <h3 className="text-lg font-semibold text-white mb-2">Foundry</h3>
                <p className="text-gray-300 text-sm">Development environment with AI assistance</p>
              </Card>
            </Link>

            <Link href="/features#crucible" className="block">
              <Card className="p-6 hover:scale-105 transition-transform duration-300 cursor-pointer">
                <div className="text-3xl mb-4">🔥</div>
                <h3 className="text-lg font-semibold text-white mb-2">Crucible</h3>
                <p className="text-gray-300 text-sm">Data processing and analysis platform</p>
              </Card>
            </Link>

            <Link href="/features#purge" className="block">
              <Card className="p-6 hover:scale-105 transition-transform duration-300 cursor-pointer">
                <div className="text-3xl mb-4">🧹</div>
                <h3 className="text-lg font-semibold text-white mb-2">Purge</h3>
                <p className="text-gray-300 text-sm">Data management and cleanup tool</p>
              </Card>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
