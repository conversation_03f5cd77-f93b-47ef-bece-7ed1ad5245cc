import Link from 'next/link';
import Button from '@/components/Button';
import Card from '@/components/Card';

export default function Cancel() {
  return (
    <div className="min-h-screen bg-black text-white">
      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-cyan-300 text-6xl mb-6">✕</div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">
            Payment Cancelled
          </h1>
          <p className="text-xl md:text-2xl text-gray-300 mb-8">
            No worries! Your payment was cancelled and you haven&apos;t been charged.
          </p>
        </div>
      </section>

      {/* Cancel Details */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8 text-center">
            <h2 className="text-2xl font-bold text-white mb-6">What happens next?</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="p-4 bg-gray-800 rounded-lg">
                <div className="text-cyan-300 text-2xl mb-2">💳</div>
                <h3 className="text-lg font-semibold text-white mb-2">No Charges</h3>
                <p className="text-gray-300 text-sm">You won&apos;t be charged for cancelling</p>
              </div>

              <div className="p-4 bg-gray-800 rounded-lg">
                <div className="text-cyan-300 text-2xl mb-2">🔄</div>
                <h3 className="text-lg font-semibold text-white mb-2">Try Again</h3>
                <p className="text-gray-300 text-sm">You can restart the process anytime</p>
              </div>
            </div>

            <div className="space-y-4">
              <p className="text-gray-300">
                Still interested in Nexus Suite? Here are some options:
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/pricing">
                  <Button variant="primary" size="lg">
                    Back to Pricing
                  </Button>
                </Link>
                <Link href="/contact">
                  <Button variant="secondary" size="lg">
                    Contact Sales
                  </Button>
                </Link>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-slate-900">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center text-white mb-12">
            Have Questions?
          </h2>

          <div className="space-y-6">
            <div className="bg-black rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-3">Can I restart the payment process?</h3>
              <p className="text-gray-300">Yes, you can return to the pricing page and start the checkout process again at any time.</p>
            </div>

            <div className="bg-black rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-3">Are there any hidden fees?</h3>
              <p className="text-gray-300">No, all pricing is transparent. You&apos;ll only see the exact amount you&apos;re paying for during checkout.</p>
            </div>

            <div className="bg-black rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-3">Can I get a demo first?</h3>
              <p className="text-gray-300">Absolutely! Contact our sales team to schedule a personalized demo of Nexus Suite.</p>
            </div>

            <div className="bg-black rounded-lg p-6">
              <h3 className="text-xl font-semibold text-white mb-3">Do you offer refunds?</h3>
              <p className="text-gray-300">Yes, we offer a 30-day money-back guarantee on all plans. Contact support for refund requests.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Try Nexus Suite?
          </h2>
          <p className="text-xl text-gray-300 mb-8">
            Join thousands of teams who trust Nexus Suite to power their productivity
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/features">
              <Button variant="secondary" size="lg">
                Explore Features
              </Button>
            </Link>
            <Link href="/pricing">
              <Button variant="primary" size="lg">
                View Pricing
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}