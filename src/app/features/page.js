import Button from '@/components/Button';
import Card from '@/components/Card';

export default function Features() {
  const products = [
    {
      name: "Nexus",
      description: "The ultimate AI-powered operating system that seamlessly integrates all your digital tools and workflows into a single, intelligent interface.",
      features: [
        "AI-powered task automation",
        "Unified workspace management",
        "Cross-platform synchronization",
        "Smart notifications and alerts",
        "Customizable dashboard",
        "Advanced security protocols"
      ],
      icon: "🚀",
      color: "bg-cyan-600"
    },
    {
      name: "Foundry",
      description: "A comprehensive development environment with built-in AI assistance for rapid prototyping and production-ready code generation.",
      features: [
        "AI-assisted code generation",
        "Real-time collaboration tools",
        "Integrated testing framework",
        "Version control integration",
        "Deployment automation",
        "Performance analytics"
      ],
      icon: "⚒️",
      color: "bg-sky-600"
    },
    {
      name: "Crucible",
      description: "Advanced data processing and analysis platform that transforms raw information into actionable insights with machine learning capabilities.",
      features: [
        "Machine learning integration",
        "Real-time data processing",
        "Advanced visualization tools",
        "Predictive analytics",
        "Data transformation pipelines",
        "API integration framework"
      ],
      icon: "🔥",
      color: "bg-blue-600"
    },
    {
      name: "Purge",
      description: "Intelligent data management and cleanup tool that automatically organizes, archives, and optimizes your digital storage.",
      features: [
        "Automated file organization",
        "Duplicate detection and removal",
        "Storage optimization",
        "Backup and recovery",
        "Privacy protection",
        "Compliance monitoring"
      ],
      icon: "🧹",
      color: "bg-cyan-700"
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white flex items-center">
      <section className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
          {/* Compact Header */}
          <div className="md:col-span-1">
            <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">
              Features
            </h1>
            <p className="text-sm text-gray-400">
              Everything you need, at a glance. No scrolling required on desktop.
            </p>
          </div>

          {/* Compact Products Grid */}
          <div className="md:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {products.map((product, index) => (
                <Card key={index} className="transition-shadow">
                  <div className="flex items-start mb-4">
                    <div className={`${product.color} w-10 h-10 rounded-lg flex items-center justify-center text-xl mr-3`}>
                      {product.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-white mb-1">{product.name}</h3>
                      {/* Keep description minimal to avoid overflow */}
                      <p className="text-sm text-gray-300 line-clamp-2">
                        {product.description}
                      </p>
                    </div>
                  </div>

                  <ul className="space-y-2">
                    {product.features.slice(0, 3).map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center text-gray-300 text-sm">
                        <span className="w-1.5 h-1.5 bg-cyan-400 rounded-full mr-2"></span>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* Optional subtle action without taking space */}
                  <div className="mt-4">
                    <Button variant="ghost" size="sm" className="px-0 py-0 text-sm">
                      Learn more →
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}