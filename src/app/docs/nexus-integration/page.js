import Card from '@/components/Card';

export const runtime = 'nodejs';

export default function NexusIntegrationDocsPage() {
  return (
    <div className="container mx-auto max-w-4xl px-6 py-24 space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Nexus Credit System Integration Guide</h1>
        <p className="text-slate-300">Integrate the Nexus desktop software with the website backend using API keys, credit endpoints, and in-app purchases via Stripe Checkout.</p>
      </div>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Authentication</h2>
        <p className="text-slate-300 mb-3">Use an API key via Bearer authentication for all desktop app API calls.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`Authorization: Bearer nxs_YOUR_API_KEY
Content-Type: application/json`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Endpoints Overview</h2>
        <ul className="list-disc pl-6 text-slate-200 space-y-1">
          <li><code>/api/credits/balance</code> (GET): Check balance + subscription</li>
          <li><code>/api/credits/consume</code> (POST): Start operation and consume credits</li>
          <li><code>/api/credits/consume</code> (PATCH): Complete/fail/cancel operation</li>
          <li><code>/api/checkout</code> (POST): Create Stripe Checkout sessions (credits/subscription/license)</li>
          <li><code>/api/licenses/status</code> (GET): License status and summary</li>
          <li><code>/api/licenses/activate</code> (POST): Activate a license key</li>
        </ul>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Operation Costs</h2>
        <ul className="list-disc pl-6 text-slate-200 space-y-1">
          <li>STANDARD_WIPE: 1 credit</li>
          <li>WIPE_WITH_DIAGNOSTICS: 2 credits</li>
          <li>DIAGNOSTICS_ONLY: 1 credit</li>
        </ul>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">GET /api/credits/balance</h2>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -H "Authorization: Bearer nxs_YOUR_KEY" \
  https://your-site.com/api/credits/balance`}</code></pre>
        <p className="text-slate-300 mt-3">Response:</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`{
  "balance": 123,
  "subscription": {"planType":"pro","workstations":5,"currentPeriodEnd":"2025-09-24T00:00:00.000Z"},
  "licenseSummary": {
    "hasServerLicense": true,
    "totalServerCount": 2,
    "maintenanceActive": true,
    "maintenanceExpiry": "2026-01-01T00:00:00.000Z"
  },
  "licenses": [
    {"id":"lic_1","licenseType":"INDUSTRIAL_SERVER","serverCount":2,"maintenanceExpiry":null,"activatedAt":"2025-08-01T12:00:00.000Z"},
    {"id":"lic_2","licenseType":"INDUSTRIAL_MAINTENANCE","serverCount":1,"maintenanceExpiry":"2026-01-01T00:00:00.000Z","activatedAt":null}
  ],
  "user": {"id":"uuid","email":"<EMAIL>","companyName":"Acme ITAD"}
}`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">POST /api/credits/consume</h2>
        <p className="text-slate-300 mb-3">Starts an operation and consumes credits atomically.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -X POST -H "Authorization: Bearer nxs_YOUR_KEY" -H "Content-Type: application/json" \
  -d '{"operationType":"STANDARD_WIPE","deviceSerial":"ABC123","deviceModel":"Dell 7090"}' \
  https://your-site.com/api/credits/consume`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">PATCH /api/credits/consume</h2>
        <p className="text-slate-300 mb-3">Complete, fail, or cancel an operation. Fail/cancel refunds credits.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -X PATCH -H "Authorization: Bearer nxs_YOUR_KEY" -H "Content-Type: application/json" \
  -d '{"operationId":"op_uuid","status":"COMPLETED"}' \
  https://your-site.com/api/credits/consume`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">POST /api/checkout (In-App Purchase)</h2>
        <p className="text-slate-300 mb-3">Create a Stripe Checkout session to buy credit packs (or subscriptions/licenses) from the desktop app.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -X POST -H "Authorization: Bearer nxs_YOUR_KEY" -H "Content-Type: application/json" \
  -d '{"type":"credits","productId":"credits_100","successUrl":"https://your-site.com/purchase/success","cancelUrl":"https://your-site.com/purchase/cancel"}' \
  https://your-site.com/api/checkout`}</code></pre>
        <p className="text-slate-300 mt-3">Response:</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`{"sessionId":"cs_test_123","url":"https://checkout.stripe.com/c/session_..."}`}</code></pre>
        <p className="text-slate-300 mt-3">Use the returned URL to open the browser. After payment, poll balance until credits are updated. The success URL supports {`{CHECKOUT_SESSION_ID}` } replacement for web flows.</p>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">GET /api/licenses/status</h2>
        <p className="text-slate-300 mb-3">Fetch license summary and list for the authenticated user.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -H "Authorization: Bearer nxs_YOUR_KEY" \
  https://your-site.com/api/licenses/status`}</code></pre>
        <p className="text-slate-300 mt-3">Response:</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`{
  "licenseSummary": {"hasServerLicense": true, "totalServerCount": 2, "maintenanceActive": true, "maintenanceExpiry": "2026-01-01T00:00:00.000Z"},
  "licenses": [
    {"id":"lic_1","licenseType":"INDUSTRIAL_SERVER","serverCount":2,"maintenanceExpiry":null,"activatedAt":"2025-08-01T12:00:00.000Z","createdAt":"2025-08-01T10:00:00.000Z"}
  ],
  "user": {"id":"uuid","email":"<EMAIL>","companyName":"Acme ITAD"}
}`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">POST /api/licenses/activate</h2>
        <p className="text-slate-300 mb-3">Activate a license key for the authenticated account.</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`curl -X POST -H "Authorization: Bearer nxs_YOUR_KEY" -H "Content-Type: application/json" \
  -d '{"licenseKey":"ABCD-EF12-GH34-IJ56"}' \
  https://your-site.com/api/licenses/activate`}</code></pre>
        <p className="text-slate-300 mt-3">Response:</p>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`{"license": {"id":"lic_1","licenseKey":"ABCD-EF12-GH34-IJ56","licenseType":"INDUSTRIAL_SERVER","serverCount":1,"maintenanceExpiry":null,"activatedAt":"2025-08-01T12:00:00.000Z","createdAt":"2025-08-01T10:00:00.000Z"}}`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Python SDK Snippet</h2>
        <pre className="bg-slate-900 p-3 rounded-md overflow-x-auto text-sm"><code>{`from nexus_sdk import NexusAPI, OperationType, OperationStatus
api = NexusAPI(api_key='nxs_YOUR_KEY', base_url='https://your-site.com')

# Check balance
balance = api.get_balance()

# Start operation
result = api.consume_credits(
    operation_type=OperationType.STANDARD_WIPE,
    device_serial='ABC123',
    device_model='Dell Optiplex 7090'
)
op_id = result['operationId']

# Complete operation
api.complete_operation(operation_id=op_id, status=OperationStatus.COMPLETED)

# License status and activation
licenses = api.get_license_status()
print('License summary:', licenses.get('licenseSummary'))
# Optionally activate a license key received via email/portal
# api.activate_license('ABCD-EF12-GH34-IJ56')

# Initiate in-app credits purchase
checkout_url = api.initiate_credit_purchase('credits_100')
# Open checkout_url, then poll api.get_balance()`}</code></pre>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Product IDs</h2>
        <ul className="list-disc pl-6 text-slate-200 space-y-1">
          <li>Credit packs: <code>credits_100</code>, <code>credits_500</code>, <code>credits_1000</code></li>
          <li>Subscriptions: <code>pro</code>, <code>business</code> (enterprise: contact sales)</li>
          <li>Licenses: <code>industrial_server</code>, <code>maintenance</code></li>
        </ul>
      </Card>

      <Card>
        <h2 className="text-xl font-semibold mb-2">Security Tips</h2>
        <ul className="list-disc pl-6 text-slate-200 space-y-1">
          <li>Store API keys securely and rotate on compromise.</li>
          <li>Mask keys in logs (show last 4 chars).</li>
          <li>Consider rate limiting and optional IP allowlisting.</li>
        </ul>
      </Card>

      <div className="text-slate-400 text-sm">
        <p>Files: <code>src/app/api/credits/balance/route.js</code>, <code>src/app/api/credits/consume/route.js</code>, <code>src/app/api/checkout/route.js</code>, <code>src/app/api/licenses/status/route.js</code>, <code>src/app/api/licenses/activate/route.js</code>, <code>lib/products.js</code>, <code>docs/nexus-sdk.py</code></p>
        <p>Prefer the full Markdown guide in <code>docs/Nexus-Integration.md</code> within the repository for copy/paste.</p>
      </div>
    </div>
  );
}
