'use client';

import { useState } from 'react';
import Button from '@/components/Button';
import Card from '@/components/Card';

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    subject: '',
    message: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    alert('Thank you for your message! We\'ll get back to you soon.');
    setFormData({
      name: '',
      email: '',
      company: '',
      subject: '',
      message: ''
    });
  };

  const contactInfo = [
    {
      icon: '📧',
      title: 'Email',
      details: '<EMAIL>',
      description: 'Send us an email anytime'
    },
    {
      icon: '💬',
      title: 'Live Chat',
      details: 'Available 24/7',
      description: 'Get instant help from our team'
    },
    {
      icon: '📞',
      title: 'Phone',
      details: '+1 (555) 123-NEXUS',
      description: 'Mon-Fri, 9AM-6PM EST'
    },
    {
      icon: '📍',
      title: 'Office',
      details: '123 Innovation Drive',
      description: 'Tech Valley, CA 94301'
    }
  ];

  return (
    <div className="min-h-screen bg-black text-white flex items-center">
      <section className="w-full px-4 sm:px-6 lg:px-8 py-8">
        <div className="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
          {/* Compact Header */}
          <div>
            <h1 className="text-4xl font-bold mb-2 bg-gradient-to-r from-cyan-300 to-sky-400 bg-clip-text text-transparent">Contact</h1>
            <p className="text-sm text-gray-400">Quick note or question? Reach us in one view.</p>
            <div className="mt-4 grid grid-cols-2 gap-3">
              {contactInfo.map((info, index) => (
                <Card key={index} className="p-4">
                  <div className="flex items-start">
                    <div className="w-9 h-9 bg-cyan-600 rounded-md flex items-center justify-center text-lg mr-3">
                      {info.icon}
                    </div>
                    <div>
                      <h3 className="text-sm font-semibold text-white">{info.title}</h3>
                      <p className="text-cyan-300 text-sm">{info.details}</p>
                      <p className="text-gray-400 text-xs">{info.description}</p>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* Streamlined Form */}
          <div className="md:col-span-2">
            <Card className="p-6">
              <h2 className="text-xl font-bold text-white mb-4">Send us a message</h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label htmlFor="name" className="block text-xs font-medium text-gray-300 mb-1">Full Name *</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-xs font-medium text-gray-300 mb-1">Email Address *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <label htmlFor="company" className="block text-xs font-medium text-gray-300 mb-1">Company</label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                      placeholder="Your company name"
                    />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-xs font-medium text-gray-300 mb-1">Subject *</label>
                    <select
                      id="subject"
                      name="subject"
                      required
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white focus:ring-2 focus:ring-cyan-400 focus:border-transparent"
                    >
                      <option value="">Select a subject</option>
                      <option value="general">General Inquiry</option>
                      <option value="sales">Sales Questions</option>
                      <option value="support">Technical Support</option>
                      <option value="billing">Billing Questions</option>
                      <option value="partnership">Partnership Opportunities</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="message" className="block text-xs font-medium text-gray-300 mb-1">Message *</label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows="4"
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-md text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400 focus:border-transparent resize-none"
                    placeholder="Tell us how we can help you..."
                  />
                </div>

                <Button type="submit" variant="primary" className="w-full">Send Message</Button>
              </form>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}