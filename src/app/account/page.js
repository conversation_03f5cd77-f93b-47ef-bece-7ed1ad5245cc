import Card from '@/components/Card';
import Button from '@/components/Button';
import { cookies } from 'next/headers';
import { getUserFromToken } from '@/lib/auth';
import Link from 'next/link';
import LogoutButton from '@/components/LogoutButton';
import { prisma } from '@/lib/db';
import ManageBillingButton from '@/components/ManageBillingButton';
import ApiKeyManager from '@/components/ApiKeyManager';

// Ensure Node.js runtime for Prisma
export const runtime = 'nodejs';

export default async function AccountPage() {
  const token = cookies().get('auth-token')?.value;
  const user = token ? await getUserFromToken(token) : null;

  if (!user) {
    // Render a minimal prompt; middleware should already redirect, but JIC
    return (
      <div className="container mx-auto max-w-2xl px-6 py-32">
        <Card>
          <h1 className="text-2xl font-bold mb-4">Please sign in</h1>
          <p className="mb-4 text-slate-300">You must be signed in to view your account.</p>
          <Link href="/login"><Button>Go to sign in</Button></Link>
        </Card>
      </div>
    );
  }

  // Fetch account data
  const [creditAgg, latestSubscription, licenses, recentTx] = await Promise.all([
    prisma.credit.aggregate({
      where: { userId: user.id },
      _sum: { remainingAmount: true },
      _count: true,
    }),
    prisma.subscription.findFirst({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.license.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
    }),
    prisma.transaction.findMany({
      where: { userId: user.id },
      orderBy: { createdAt: 'desc' },
      take: 5,
    }),
  ]);

  const creditsRemaining = creditAgg._sum.remainingAmount || 0;

  function formatDate(d) {
    try { return new Date(d).toLocaleDateString(); } catch { return '-'; }
  }

  function formatMoney(dec) {
    try { return `$${dec?.toString()}`; } catch { return '$0.00'; }
  }

  return (
    <div className="container mx-auto max-w-3xl px-6 py-32 space-y-6">
      {/* Profile */}
      <Card>
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-2xl font-bold mb-2">Account</h1>
            <div className="space-y-1 text-slate-200">
              <p><span className="text-slate-400">Name:</span> {user.firstName} {user.lastName}</p>
              <p><span className="text-slate-400">Email:</span> {user.email}</p>
              {user.companyName && (
                <p><span className="text-slate-400">Company:</span> {user.companyName}</p>
              )}
              <p><span className="text-slate-400">Member since:</span> {formatDate(user.createdAt)}</p>
            </div>
          </div>
          <div className="flex gap-2">
            <LogoutButton />
          </div>
        </div>
      </Card>

      {/* Subscription & Billing */}
      <Card>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Subscription & Billing</h2>
          <ManageBillingButton />
        </div>
        {latestSubscription ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-slate-200">
            <p><span className="text-slate-400">Plan:</span> {latestSubscription.planType}</p>
            <p><span className="text-slate-400">Workstations:</span> {latestSubscription.workstations}</p>
            <p><span className="text-slate-400">Status:</span> {latestSubscription.status}</p>
            <p><span className="text-slate-400">Renews:</span> {formatDate(latestSubscription.currentPeriodEnd)}</p>
            {latestSubscription.cancelAtPeriodEnd && (
              <p className="text-yellow-400">Cancellation scheduled at period end</p>
            )}
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <p className="text-slate-300">No active subscription.</p>
            <Link href="/pricing"><Button>View plans</Button></Link>
          </div>
        )}
      </Card>

      {/* Credits */}
      <Card>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold mb-1">Credits</h2>
            <p className="text-slate-300">Remaining credits: <span className="text-white font-semibold">{creditsRemaining}</span></p>
          </div>
          <Link href="/pricing"><Button>Buy credits</Button></Link>
        </div>
      </Card>

      {/* Licenses */}
      <Card>
        <h2 className="text-xl font-semibold mb-3">Perpetual Licenses</h2>
        {licenses.length === 0 ? (
          <p className="text-slate-300">No licenses yet.</p>
        ) : (
          <div className="space-y-3">
            {licenses.map((lic) => (
              <div key={lic.id} className="border border-slate-800 rounded-md p-3">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-slate-200">
                  <p><span className="text-slate-400">Type:</span> {lic.licenseType}</p>
                  <p><span className="text-slate-400">Servers:</span> {lic.serverCount}</p>
                  <p className="truncate"><span className="text-slate-400">Key:</span> {lic.licenseKey}</p>
                  <p><span className="text-slate-400">Maintenance Expiry:</span> {lic.maintenanceExpiry ? formatDate(lic.maintenanceExpiry) : '—'}</p>
                  <p><span className="text-slate-400">Activated At:</span> {lic.activatedAt ? formatDate(lic.activatedAt) : '—'}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* Recent Activity */}
      <Card>
        <h2 className="text-xl font-semibold mb-3">Recent Transactions</h2>
        {recentTx.length === 0 ? (
          <p className="text-slate-300">No recent transactions.</p>
        ) : (
          <div className="space-y-2">
            {recentTx.map((tx) => (
              <div key={tx.id} className="flex items-center justify-between border border-slate-800 rounded-md p-3 text-slate-200">
                <div className="flex-1">
                  <p className="font-medium">{tx.type} <span className="text-slate-400">• {tx.status}</span></p>
                  <p className="text-sm text-slate-400">{formatDate(tx.createdAt)}</p>
                </div>
                <div className="ml-4 whitespace-nowrap">{formatMoney(tx.amount)}</div>
              </div>
            ))}
          </div>
        )}
      </Card>

      {/* API Keys for Nexus Integration */}
      <ApiKeyManager />
      
      {/* Developer Docs */}
      <Card>
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold mb-1">Developer Docs</h2>
            <p className="text-slate-300">Integrate Nexus with your software via API and SDK.</p>
          </div>
          <Link href="/docs/nexus-integration"><Button>View Integration Guide</Button></Link>
        </div>
      </Card>
    </div>
  );
}
