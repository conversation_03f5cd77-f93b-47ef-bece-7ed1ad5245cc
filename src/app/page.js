"use client";
import Link from 'next/link';
import Button from '@/components/Button';
import Card from '@/components/Card';
import { useState } from 'react';

export default function Home() {
  const backgrounds = [
    '/nexus_wallpaper_landscape.png',
    '/nexus_nebula.png',
    '/nexussquareblackhole.png',
    '/nexus.png'
  ];
  const [bgIndex, setBgIndex] = useState(0);
  const colorBgMap = {
    green: 'bg-cyan-600',
    crimson: 'bg-sky-600',
    blue: 'bg-blue-600',
    purple: 'bg-cyan-700',
  };
  return (
    <div className="min-h-screen text-white">
      {/* Full-bleed Neon Hero */}
      <section className="relative min-h-screen hero-bg flex items-center justify-center overflow-hidden">
        {/* Wallpaper background */}
        <div
          className="absolute inset-0 bg-center bg-cover opacity-35"
          style={{ backgroundImage: `url(${backgrounds[bgIndex]})` }}
          aria-hidden="true"
        />
        {/* subtle overlay noise/waves via gradients provided in .hero-bg */}
        <div className="absolute inset-0 pointer-events-none" aria-hidden="true" />

        {/* Centered brand */}
        <div className="relative z-10 text-center px-6 pt-24">
          <h1 className="neon-text neon-animate font-extrabold tracking-tight select-none text-6xl sm:text-7xl md:text-8xl">
            
          </h1>
          <p className="mt-6 text-slate-300/80 text-sm sm:text-base">
            
          </p>
        </div>

        {/* tiny scroll hint */}
        <div className="absolute bottom-8 inset-x-0 flex justify-center opacity-60">
          <div className="w-1.5 h-6 rounded-full bg-white/20 relative overflow-hidden">
            <span className="absolute left-1/2 -translate-x-1/2 top-1 w-1 h-1.5 rounded-full bg-white/70 animate-[ping_2s_ease-in-out_infinite]" />
          </div>
        </div>

        {/* Quick background selector */}
        <div className="absolute bottom-8 right-8 hidden sm:flex gap-2 bg-black/30 backdrop-blur-sm p-2 rounded-md border border-white/10">
          {backgrounds.map((src, i) => (
            <button
              key={src}
              onClick={() => setBgIndex(i)}
              className={`h-8 w-12 rounded-sm bg-center bg-cover ring-1 transition-all ${
                i === bgIndex ? 'ring-cyan-400 shadow-[0_0_12px_rgba(34,211,238,0.35)]' : 'ring-white/20'
              }`}
              style={{ backgroundImage: `url(${src})` }}
              aria-label={`Set background ${i + 1}`}
            />
          ))}
        </div>
      </section>
    </div>
  );
}