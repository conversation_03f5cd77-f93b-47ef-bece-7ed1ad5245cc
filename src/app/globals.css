@import "tailwindcss";

:root {
  --background: #0b1220; /* slate-950-ish */
  --foreground: #e5e7eb; /* slate-200 */
  --primary: #06b6d4;   /* cyan-500 */
  --secondary: #0ea5e9; /* sky-500 */
  --accent: #0f172a;    /* slate-900 */
  --muted: #1f2937;     /* gray-800 */
}

/* stylelint-disable-next-line at-rule-no-unknown */
@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-muted: var(--muted);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), ui-sans-serif, system-ui, -apple-system, Se<PERSON>e UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html { 
  scroll-behavior: smooth; 
}

::selection {
  background: color-mix(in oklab, var(--primary) 30%, transparent);
  color: var(--foreground);
}

/* Neon hero helpers */
.hero-bg {
  /* Deep navy gradient with soft vignette */
  background:
    radial-gradient(1200px 800px at 70% 40%, color-mix(in oklab, var(--secondary) 12%, transparent), transparent 60%),
    radial-gradient(900px 600px at 20% 70%, color-mix(in oklab, var(--primary) 12%, transparent), transparent 55%),
    linear-gradient(180deg, #060c17 0%, #070f1d 40%, #0a1220 100%);
}

.neon-text {
  text-shadow:
    0 0 8px color-mix(in oklab, var(--secondary) 35%, transparent),
    0 0 18px color-mix(in oklab, var(--secondary) 35%, transparent),
    0 0 42px color-mix(in oklab, var(--secondary) 25%, transparent);
}

@keyframes pulse-glow {
  0%, 100% { filter: drop-shadow(0 0 12px rgba(34,211,238,0.4)); opacity: 0.98; }
  50% { filter: drop-shadow(0 0 24px rgba(56,189,248,0.5)); opacity: 1; }
}

.neon-animate {
  animation: pulse-glow 3s ease-in-out infinite;
}

