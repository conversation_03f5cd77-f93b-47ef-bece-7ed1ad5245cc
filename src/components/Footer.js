import Link from 'next/link';

export default function Footer() {
  const year = new Date().getFullYear();
  return (
    <footer className="bg-black border-t border-cyan-500/50 px-6 py-12">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-2xl font-bold text-cyan-300 mb-4">Nexus Suite</h3>
            <p className="text-gray-400">
              The complete hardware management and security suite for modern IT infrastructure.
            </p>
          </div>
          
          <div>
            <h4 className="text-white font-semibold mb-4">Products</h4>
            <ul className="space-y-2">
              <li><Link href="/features#nexus" className="text-gray-400 hover:text-cyan-300">Nexus</Link></li>
              <li><Link href="/features#foundry" className="text-gray-400 hover:text-cyan-300">Foundry</Link></li>
              <li><Link href="/features#crucible" className="text-gray-400 hover:text-cyan-300">Crucible</Link></li>
              <li><Link href="/features#purge" className="text-gray-400 hover:text-cyan-300">Purge</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-white font-semibold mb-4">Company</h4>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-400 hover:text-cyan-300">About</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-cyan-300">Contact</Link></li>
              <li><Link href="/pricing" className="text-gray-400 hover:text-cyan-300">Pricing</Link></li>
              <li><Link href="/support" className="text-gray-400 hover:text-cyan-300">Support</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 className="text-white font-semibold mb-4">Legal</h4>
            <ul className="space-y-2">
              <li><Link href="/privacy" className="text-gray-400 hover:text-cyan-300">Privacy Policy</Link></li>
              <li><Link href="/terms" className="text-gray-400 hover:text-cyan-300">Terms of Service</Link></li>
              <li><Link href="/refund" className="text-gray-400 hover:text-cyan-300">Refund Policy</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800/60 mt-8 pt-8 text-center">
          <p className="text-gray-400">
            © {year} Nexus Suite. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}