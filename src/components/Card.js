export default function Card({ 
  children, 
  className = '', 
  variant = 'default',
  ...props 
}) {
  const baseClasses = 'rounded-lg border p-6 bg-gradient-to-b from-slate-900 to-black/60 shadow-sm shadow-black/20 transition-shadow duration-200 hover:shadow-cyan-500/20 hover:border-cyan-400/40';
  
  const variants = {
    default: 'border-slate-700',
    accent: 'border-cyan-500/40',
    highlight: 'border-sky-500/40'
  };
  
  const classes = `${baseClasses} ${variants[variant]} ${className}`;
  
  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
}