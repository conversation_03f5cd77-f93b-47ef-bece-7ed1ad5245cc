'use client';

import { useState, useEffect } from 'react';
import Button from '@/components/Button';
import Card from '@/components/Card';

export default function ApiKeyManager() {
  const [apiKeys, setApiKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showNewKey, setShowNewKey] = useState(null);
  const [creating, setCreating] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');

  useEffect(() => {
    fetchApiKeys();
  }, []);

  async function fetchApiKeys() {
    try {
      const res = await fetch('/api/keys');
      if (res.ok) {
        const data = await res.json();
        setApiKeys(data.apiKeys);
      }
    } catch (error) {
      console.error('Failed to fetch API keys:', error);
    } finally {
      setLoading(false);
    }
  }

  async function createApiKey() {
    if (!newKeyName.trim()) return;
    
    setCreating(true);
    try {
      const res = await fetch('/api/keys', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: newKeyName })
      });

      if (res.ok) {
        const data = await res.json();
        setShowNewKey(data.apiKey.key);
        setApiKeys([data.apiKey, ...apiKeys]);
        setNewKeyName('');
      }
    } catch (error) {
      console.error('Failed to create API key:', error);
      alert('Failed to create API key');
    } finally {
      setCreating(false);
    }
  }

  async function deleteApiKey(id) {
    if (!confirm('Are you sure you want to delete this API key? This cannot be undone.')) {
      return;
    }

    try {
      const res = await fetch(`/api/keys?id=${id}`, {
        method: 'DELETE'
      });

      if (res.ok) {
        setApiKeys(apiKeys.filter(k => k.id !== id));
      }
    } catch (error) {
      console.error('Failed to delete API key:', error);
      alert('Failed to delete API key');
    }
  }

  function copyToClipboard(text) {
    navigator.clipboard.writeText(text);
    alert('API key copied to clipboard');
  }

  function formatDate(date) {
    return new Date(date).toLocaleDateString();
  }

  if (loading) {
    return <Card><p className="text-slate-300">Loading API keys...</p></Card>;
  }

  return (
    <Card>
      <h2 className="text-xl font-semibold mb-4">API Keys</h2>
      
      {/* New key display */}
      {showNewKey && (
        <div className="mb-4 p-4 bg-green-900/20 border border-green-700 rounded-lg">
          <p className="text-green-400 font-semibold mb-2">New API Key Created!</p>
          <p className="text-sm text-slate-300 mb-3">
            Save this key now. You won&apos;t be able to see it again.
          </p>
          <div className="flex items-center gap-2">
            <code className="flex-1 p-2 bg-slate-900 rounded text-xs text-green-400 break-all">
              {showNewKey}
            </code>
            <Button 
              size="sm" 
              onClick={() => copyToClipboard(showNewKey)}
            >
              Copy
            </Button>
          </div>
          <Button 
            size="sm" 
            variant="secondary" 
            className="mt-3"
            onClick={() => setShowNewKey(null)}
          >
            I&apos;ve saved the key
          </Button>
        </div>
      )}

      {/* Create new key */}
      <div className="flex gap-2 mb-4">
        <input
          type="text"
          placeholder="Key name (e.g., Production Server)"
          value={newKeyName}
          onChange={(e) => setNewKeyName(e.target.value)}
          onKeyDown={(e) => e.key === 'Enter' && createApiKey()}
          className="flex-1 px-3 py-2 bg-slate-900 border border-slate-700 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-blue-500"
          disabled={creating}
        />
        <Button 
          onClick={createApiKey} 
          disabled={creating || !newKeyName.trim()}
        >
          {creating ? 'Creating...' : 'Create Key'}
        </Button>
      </div>

      {/* API keys list */}
      {apiKeys.length === 0 ? (
        <p className="text-slate-300">No API keys yet. Create one to integrate Nexus software.</p>
      ) : (
        <div className="space-y-2">
          {apiKeys.map((key) => (
            <div key={key.id} className="flex items-center justify-between p-3 bg-slate-900 rounded-lg">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <span className="font-medium text-white">{key.name}</span>
                  <code className="text-xs text-slate-400">•••• {key.lastFourChars}</code>
                  {!key.isActive && (
                    <span className="text-xs px-2 py-0.5 bg-red-900/50 text-red-400 rounded">Inactive</span>
                  )}
                </div>
                <div className="text-sm text-slate-400 mt-1">
                  Created: {formatDate(key.createdAt)}
                  {key.lastUsedAt && ` • Last used: ${formatDate(key.lastUsedAt)}`}
                  {key.expiresAt && ` • Expires: ${formatDate(key.expiresAt)}`}
                </div>
              </div>
              <Button 
                size="sm" 
                variant="secondary"
                onClick={() => deleteApiKey(key.id)}
              >
                Delete
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Integration guide */}
      <div className="mt-6 p-4 bg-slate-900 rounded-lg">
        <h3 className="font-semibold mb-2">Quick Integration Guide</h3>
        <pre className="text-xs text-slate-300 overflow-x-auto">
{`# Check credit balance
curl -H "Authorization: Bearer YOUR_API_KEY" \\
  ${process.env.NEXT_PUBLIC_APP_URL || 'https://your-site.com'}/api/credits/balance

# Consume credits for operation
curl -X POST -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{"operationType":"STANDARD_WIPE","deviceSerial":"ABC123"}' \\
  ${process.env.NEXT_PUBLIC_APP_URL || 'https://your-site.com'}/api/credits/consume`}
        </pre>
      </div>
    </Card>
  );
}
