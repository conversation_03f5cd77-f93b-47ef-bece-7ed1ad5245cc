'use client';

import Button from '@/components/Button';
import { useRouter } from 'next/navigation';

export default function ManageBillingButton({ className = '', size = 'md', variant = 'secondary', children = 'Manage billing' }) {
  const router = useRouter();

  async function onClick() {
    try {
      const res = await fetch('/api/billing/portal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ returnUrl: `${window.location.origin}/account` })
      });

      if (res.status === 401) {
        const next = encodeURIComponent('/account');
        router.push(`/login?next=${next}`);
        return;
      }

      const data = await res.json();
      if (!res.ok || !data?.url) throw new Error(data?.error || 'Unable to create billing portal session');

      window.location.href = data.url;
    } catch (err) {
      console.error('Failed to open billing portal:', err);
      alert('Sorry, something went wrong opening the billing portal.');
    }
  }

  return (
    <Button onClick={onClick} className={className} size={size} variant={variant}>
      {children}
    </Button>
  );
}
