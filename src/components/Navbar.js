'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import LogoutButton from '@/components/LogoutButton';

export default function Navbar() {
  const [open, setOpen] = useState(false);
  const [user, setUser] = useState(null);
  const pathname = usePathname();

  useEffect(() => {
    let ignore = false;
    async function load() {
      try {
        const res = await fetch('/api/auth/me');
        if (!res.ok) {
          if (!ignore) setUser(null);
          return;
        }
        const data = await res.json();
        if (!ignore) setUser(data?.user || null);
      } catch (_) {
        if (!ignore) setUser(null);
      }
    }
    load();

    function onLogout() { setUser(null); }
    if (typeof window !== 'undefined') {
      window.addEventListener('auth:logout', onLogout);
    }
    return () => {
      ignore = true;
      if (typeof window !== 'undefined') {
        window.removeEventListener('auth:logout', onLogout);
      }
    };
  }, [pathname]);
  return (
    <nav className="sticky top-0 z-50 px-6 py-4 backdrop-blur-sm bg-black/60 border-b border-white/10">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <Link href="/" className="text-2xl font-bold text-slate-200">
          Nexus Suite
        </Link>

        <div className="hidden md:flex space-x-8 items-center">
          <Link href="/" className="text-slate-200/90 hover:text-white transition-colors">
            Home
          </Link>
          <Link href="/features" className="text-slate-200/90 hover:text-white transition-colors">
            Features
          </Link>
          <Link href="/pricing" className="text-slate-200/90 hover:text-white transition-colors">
            Pricing
          </Link>
          <Link href="/contact" className="text-slate-200/90 hover:text-white transition-colors">
            Contact
          </Link>
          {user ? (
            <>
              <Link href="/account" className="text-slate-200/90 hover:text-white transition-colors">
                Account
              </Link>
              <LogoutButton variant="secondary" size="sm" />
            </>
          ) : (
            <>
              <Link href={`/login?next=${encodeURIComponent(pathname || '/')}`} className="text-slate-200/90 hover:text-white transition-colors">
                Sign in
              </Link>
              <Link
                href="/register"
                className="px-4 py-2 rounded-md bg-cyan-600 text-white hover:bg-cyan-500 transition-colors"
              >
                Sign up
              </Link>
            </>
          )}
        </div>

        <div className="flex items-center gap-3">
          <button
            aria-label="Toggle menu"
            aria-expanded={open}
            onClick={() => setOpen(!open)}
            className="md:hidden inline-flex items-center justify-center w-10 h-10 rounded-md border border-white/10 text-white hover:bg-white/5"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="1.5" stroke="currentColor" className="w-6 h-6">
              {open ? (
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5M3.75 17.25h16.5" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {open && (
        <div className="md:hidden px-6 pt-3 pb-4 border-t border-white/10 bg-black/70 backdrop-blur-sm">
          <div className="flex flex-col space-y-2">
            <Link href="/" className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Home</Link>
            <Link href="/features" className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Features</Link>
            <Link href="/pricing" className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Pricing</Link>
            <Link href="/contact" className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Contact</Link>
            {user ? (
              <>
                <Link href="/account" className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Account</Link>
                <div className="px-3">
                  <LogoutButton variant="secondary" size="sm" className="w-full" />
                </div>
              </>
            ) : (
              <>
                <Link href={`/login?next=${encodeURIComponent(pathname || '/')}`} className="block px-3 py-2 rounded-md text-white hover:bg-white/5" onClick={() => setOpen(false)}>Sign in</Link>
                <Link href="/register" className="block px-3 py-2 rounded-md bg-cyan-600 text-white hover:bg-cyan-500" onClick={() => setOpen(false)}>Sign up</Link>
              </>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}