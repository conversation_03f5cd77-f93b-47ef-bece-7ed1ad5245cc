export default function Button({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className = '', 
  ...props 
}) {
  const baseClasses = 'font-semibold rounded-md transition-colors shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black';
  
  const variants = {
    primary: 'bg-cyan-500 hover:bg-cyan-400 text-black shadow-cyan-500/30 focus:ring-cyan-400',
    secondary: 'bg-slate-800 hover:bg-slate-700 text-cyan-200 border border-cyan-500/20 shadow-cyan-500/10 focus:ring-cyan-400',
    outline: 'border border-cyan-400 text-cyan-400 hover:bg-cyan-400 hover:text-black focus:ring-cyan-400',
    ghost: 'text-cyan-400 hover:bg-cyan-400 hover:text-black focus:ring-cyan-400'
  };
  
  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };
  
  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`;
  
  return (
    <button className={classes} {...props}>
      {children}
    </button>
  );
}