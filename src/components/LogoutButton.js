'use client';

import { useRouter } from 'next/navigation';
import Button from '@/components/Button';

export default function LogoutButton({ className = '', variant = 'secondary', size = 'md', children = 'Sign out' }) {
  const router = useRouter();

  async function handleLogout() {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (_) {
      // ignore
    } finally {
      if (typeof window !== 'undefined') {
        try { window.dispatchEvent(new CustomEvent('auth:logout')); } catch (_) {}
      }
      router.push('/login');
      router.refresh();
    }
  }

  return (
    <Button variant={variant} size={size} onClick={handleLogout} className={className}>
      {children}
    </Button>
  );
}
