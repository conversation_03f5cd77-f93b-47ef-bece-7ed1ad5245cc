{"name": "nexus-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint", "seed:test-user": "node scripts/seed-test-user.js", "postinstall": "prisma generate"}, "dependencies": {"@prisma/client": "^6.14.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "stripe": "^18.4.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.4.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "eslint": "^9", "eslint-config-next": "15.5.0", "prisma": "^6.14.0", "tailwindcss": "^4"}}