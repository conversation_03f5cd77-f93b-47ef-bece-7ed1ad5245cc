const { PrismaClient } = require('@prisma/client');
const Stripe = require('stripe');
require('dotenv').config();

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function verifyProductionSetup() {
  try {
    console.log('=== VERIFYING PRODUCTION SETUP ===\n');
    
    // 1. Check environment configuration
    console.log('1. Environment Configuration:');
    console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
    console.log(`   NEXT_PUBLIC_APP_URL: ${process.env.NEXT_PUBLIC_APP_URL}`);
    console.log(`   Database: ${process.env.DATABASE_URL ? 'Neon (configured)' : 'NOT SET'}`);
    console.log(`   Stripe Secret Key: ${process.env.STRIPE_SECRET_KEY ? 'Set (test mode)' : 'NOT SET'}`);
    console.log(`   Stripe Webhook Secret: ${process.env.STRIPE_WEBHOOK_SECRET ? 'Set' : 'NOT SET'}`);
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('   ⚠️  NODE_ENV should be "production" for Vercel deployment');
    }
    
    if (process.env.NEXT_PUBLIC_APP_URL?.includes('localhost')) {
      console.log('   ⚠️  NEXT_PUBLIC_APP_URL should be your Vercel domain, not localhost');
    }
    console.log();
    
    // 2. Test database connection
    console.log('2. Database Connection:');
    try {
      const userCount = await prisma.user.count();
      console.log(`   ✅ Connected to Neon database`);
      console.log(`   ✅ Found ${userCount} users`);
    } catch (error) {
      console.log(`   ❌ Database connection failed: ${error.message}`);
    }
    console.log();
    
    // 3. Test Stripe connection
    console.log('3. Stripe Connection:');
    try {
      const account = await stripe.accounts.retrieve();
      console.log(`   ✅ Connected to Stripe account: ${account.id}`);
      console.log(`   ✅ Account type: ${account.type}`);
      console.log(`   ✅ Test mode: ${process.env.STRIPE_SECRET_KEY.includes('test')}`);
    } catch (error) {
      console.log(`   ❌ Stripe connection failed: ${error.message}`);
    }
    console.log();
    
    // 4. Check webhook configuration
    console.log('4. Webhook Configuration:');
    try {
      const webhooks = await stripe.webhookEndpoints.list();
      console.log(`   Found ${webhooks.data.length} webhook endpoints:`);
      
      webhooks.data.forEach((webhook, index) => {
        console.log(`   ${index + 1}. ${webhook.url}`);
        console.log(`      Status: ${webhook.status}`);
        console.log(`      Events: ${webhook.enabled_events.join(', ')}`);
        
        if (webhook.url.includes('localhost')) {
          console.log(`      ⚠️  This webhook points to localhost - update to Vercel domain`);
        }
        if (webhook.url.includes('vercel.app') || webhook.url.includes('your-domain.com')) {
          console.log(`      ✅ This webhook points to production domain`);
        }
      });
      
      if (webhooks.data.length === 0) {
        console.log('   ❌ No webhooks configured in Stripe Dashboard');
        console.log('   📝 You need to add a webhook endpoint in Stripe Dashboard');
      }
    } catch (error) {
      console.log(`   ❌ Failed to fetch webhooks: ${error.message}`);
    }
    console.log();
    
    // 5. Check recent transactions
    console.log('5. Recent Activity:');
    const transactions = await prisma.transaction.findMany({
      orderBy: { createdAt: 'desc' },
      take: 5,
      include: { user: { select: { email: true } } }
    });
    
    console.log(`   Found ${transactions.length} recent transactions:`);
    transactions.forEach(tx => {
      console.log(`   - ${tx.type} | $${tx.amount} | ${tx.status} | ${tx.user.email}`);
    });
    
    if (transactions.length === 0) {
      console.log('   ⚠️  No transactions found - webhooks may not be working');
    }
    console.log();
    
    // 6. Recommendations
    console.log('=== RECOMMENDATIONS ===');
    
    if (process.env.NEXT_PUBLIC_APP_URL?.includes('localhost')) {
      console.log('🔧 Update NEXT_PUBLIC_APP_URL to your Vercel domain');
    }
    
    if (process.env.NODE_ENV !== 'production') {
      console.log('🔧 Set NODE_ENV=production in Vercel environment variables');
    }
    
    console.log('🔧 Configure webhook in Stripe Dashboard:');
    console.log('   1. Go to https://dashboard.stripe.com/webhooks');
    console.log('   2. Click "Add endpoint"');
    console.log('   3. URL: https://your-vercel-domain.com/api/webhooks/stripe');
    console.log('   4. Events: payment_intent.succeeded, checkout.session.completed');
    console.log('   5. Copy the webhook signing secret to STRIPE_WEBHOOK_SECRET');
    
    console.log('\n🔧 Test the purchase flow:');
    console.log('   1. Visit your Vercel site');
    console.log('   2. Go to pricing/checkout page');
    console.log('   3. Use test card: 4242 4242 4242 4242');
    console.log('   4. Check if credits are added to your account');
    
  } catch (error) {
    console.error('Error verifying setup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyProductionSetup();
