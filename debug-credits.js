const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function debugCredits() {
  try {
    console.log('=== DEBUGGING CREDIT PURCHASE ISSUE ===\n');
    
    // Check if we have any users
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true
      }
    });
    
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ${user.email} (ID: ${user.id})`);
    });
    console.log();
    
    // Check recent transactions
    const transactions = await prisma.transaction.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        user: {
          select: { email: true }
        }
      }
    });
    
    console.log(`Found ${transactions.length} recent transactions:`);
    transactions.forEach(tx => {
      console.log(`- ${tx.type} | $${tx.amount} | ${tx.status} | ${tx.user.email} | ${tx.createdAt}`);
      if (tx.stripePaymentIntentId) {
        console.log(`  Stripe Payment Intent: ${tx.stripePaymentIntentId}`);
      }
      if (tx.metadata) {
        console.log(`  Metadata:`, tx.metadata);
      }
    });
    console.log();
    
    // Check credits for each user
    for (const user of users) {
      const credits = await prisma.credit.findMany({
        where: { userId: user.id },
        orderBy: { createdAt: 'desc' }
      });
      
      const totalCredits = credits.reduce((sum, c) => sum + c.amount, 0);
      const remainingCredits = credits.reduce((sum, c) => sum + c.remainingAmount, 0);
      
      console.log(`Credits for ${user.email}:`);
      console.log(`  Total purchased: ${totalCredits}`);
      console.log(`  Remaining: ${remainingCredits}`);
      
      if (credits.length > 0) {
        console.log(`  Credit records:`);
        credits.forEach(credit => {
          console.log(`    - ${credit.amount} credits ($${credit.purchasePrice}) | Remaining: ${credit.remainingAmount} | ${credit.createdAt}`);
        });
      }
      console.log();
    }
    
    // Check API keys
    const apiKeys = await prisma.apiKey.findMany({
      include: {
        user: {
          select: { email: true }
        }
      }
    });
    
    console.log(`Found ${apiKeys.length} API keys:`);
    apiKeys.forEach(key => {
      console.log(`- ${key.user.email} | Active: ${key.isActive} | Last used: ${key.lastUsedAt}`);
    });
    console.log();
    
    // Check recent usage logs
    const usageLogs = await prisma.usageLog.findMany({
      orderBy: { createdAt: 'desc' },
      take: 10,
      include: {
        apiKey: {
          include: {
            user: {
              select: { email: true }
            }
          }
        }
      }
    });
    
    console.log(`Found ${usageLogs.length} recent usage logs:`);
    usageLogs.forEach(log => {
      console.log(`- ${log.action} | Credits: ${log.creditsUsed} | ${log.apiKey.user.email} | ${log.createdAt}`);
    });
    
  } catch (error) {
    console.error('Error debugging credits:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugCredits();
