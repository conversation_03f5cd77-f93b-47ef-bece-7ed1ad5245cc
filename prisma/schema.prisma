// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for customer accounts
model User {
  id                String         @id @default(uuid())
  email             String         @unique
  password          String
  firstName         String?
  lastName          String?
  companyName       String?
  phoneNumber       String?         @map("phone")
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  
  credits           Credit[]
  subscriptions     Subscription[]
  licenses          License[]
  transactions      Transaction[]
  operations        Operation[]
  apiKeys           ApiKey[]
}

// Credit system for pay-per-use model
model Credit {
  id                String            @id @default(cuid())
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  
  amount            Int               // Number of credits
  remainingAmount   Int               // Credits left
  purchasePrice     Decimal           @db.Decimal(10, 2)
  
  createdAt         DateTime          @default(now())
  expiresAt         DateTime?         // NULL means never expires
  
  // Track which transaction created these credits
  transactionId     String?
  transaction       Transaction?      @relation(fields: [transactionId], references: [id])
  
  // Track usage (removed - operations are tracked via user)
  
  @@index([userId])
}

// Subscription model for monthly/annual plans
model Subscription {
  id                String            @id @default(cuid())
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  
  planType          SubscriptionPlan
  workstations      Int               // Number of allowed workstations
  status            SubscriptionStatus
  
  stripeSubscriptionId String?        @unique
  currentPeriodStart   DateTime
  currentPeriodEnd     DateTime
  cancelAtPeriodEnd    Boolean        @default(false)
  
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  
  @@index([userId])
}

// Perpetual license model
model License {
  id                String            @id @default(cuid())
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  
  licenseKey        String            @unique
  licenseType       LicenseType
  serverCount       Int               @default(1)
  
  // Maintenance subscription (optional)
  maintenanceExpiry DateTime?
  
  createdAt         DateTime          @default(now())
  activatedAt       DateTime?
  
  // Track which transaction created this license
  transactionId     String?
  transaction       Transaction?      @relation(fields: [transactionId], references: [id])
  
  @@index([userId])
}

// Transaction history
model Transaction {
  id                String            @id @default(cuid())
  userId            String
  user              User              @relation(fields: [userId], references: [id])
  
  type              TransactionType
  amount            Decimal           @db.Decimal(10, 2)
  currency          String            @default("USD")
  status            TransactionStatus
  
  stripePaymentIntentId String?       @unique
  stripeInvoiceId       String?       @unique
  
  metadata          Json?             // Store additional data
  
  createdAt         DateTime          @default(now())
  
  // Relations to what was purchased
  credits           Credit[]
  licenses          License[]
  
  @@index([userId])
}

// Track operations (wipes, diagnostics)
model Operation {
  id                String         @id @default(uuid())
  userId            String
  user              User           @relation(fields: [userId], references: [id])
  type              OperationType
  status            OperationStatus @default(PENDING)
  creditsUsed       Int
  deviceSerial      String?
  deviceModel       String?
  startedAt         DateTime?
  completedAt       DateTime?
  errorMessage      String?
  metadata          Json?
  createdAt         DateTime       @default(now())
  updatedAt         DateTime       @updatedAt
  
  usageLogs         UsageLog[]
  
  @@index([userId])
  @@index([status])
}

// API Keys for software integration
model ApiKey {
  id                String         @id @default(uuid())
  userId            String
  user              User           @relation(fields: [userId], references: [id])
  name              String         // e.g., "Production Server", "Test Machine"
  keyHash           String         @unique // Hashed API key
  lastFourChars     String         // Last 4 chars for identification
  isActive          Boolean        @default(true)
  lastUsedAt        DateTime?
  createdAt         DateTime       @default(now())
  expiresAt         DateTime?      // Optional expiration
  
  usageLogs         UsageLog[]
  
  @@index([userId])
}

model UsageLog {
  id                String         @id @default(uuid())
  apiKeyId          String
  apiKey            ApiKey         @relation(fields: [apiKeyId], references: [id])
  operationId       String?
  operation         Operation?     @relation(fields: [operationId], references: [id])
  action            String         // e.g., "consume_credit", "check_balance"
  creditsUsed       Int            @default(0)
  deviceSerial      String?        // Device being tested/wiped
  deviceModel       String?
  metadata          Json?          // Additional operation details
  ipAddress         String?
  userAgent         String?
  createdAt         DateTime       @default(now())
  
  @@index([apiKeyId])
  @@index([operationId])
}

// Enums
enum SubscriptionPlan {
  PRO              // $249/mo - 5 workstations
  BUSINESS         // $499/mo - 15 workstations
  ENTERPRISE       // Custom pricing
}

enum SubscriptionStatus {
  ACTIVE
  PAST_DUE
  CANCELED
  INCOMPLETE
  TRIALING
}

enum LicenseType {
  INDUSTRIAL_SERVER    // $4,999 one-time
  INDUSTRIAL_MAINTENANCE // $499/year
}

enum TransactionType {
  CREDIT_PURCHASE
  SUBSCRIPTION_PAYMENT
  LICENSE_PURCHASE
  MAINTENANCE_RENEWAL
  REFUND
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum OperationType {
  STANDARD_WIPE       // 1 credit
  WIPE_WITH_DIAGNOSTICS // 2 credits
  DIAGNOSTICS_ONLY    // 1 credit
}

enum OperationStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  CANCELED
}
