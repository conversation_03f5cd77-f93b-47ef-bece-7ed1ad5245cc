/*
  Warnings:

  - You are about to drop the column `key` on the `ApiKey` table. All the data in the column will be lost.
  - You are about to drop the column `revokedAt` on the `ApiKey` table. All the data in the column will be lost.
  - You are about to drop the column `certificateId` on the `Operation` table. All the data in the column will be lost.
  - You are about to drop the column `certificateUrl` on the `Operation` table. All the data in the column will be lost.
  - You are about to drop the column `creditId` on the `Operation` table. All the data in the column will be lost.
  - You are about to drop the column `diagnosticReport` on the `Operation` table. All the data in the column will be lost.
  - You are about to drop the column `licenseId` on the `Operation` table. All the data in the column will be lost.
  - You are about to drop the column `subscriptionId` on the `Operation` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[keyHash]` on the table `ApiKey` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `keyHash` to the `ApiKey` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lastFourChars` to the `ApiKey` table without a default value. This is not possible if the table is not empty.
  - Added the required column `creditsUsed` to the `Operation` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updatedAt` to the `Operation` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "public"."Operation" DROP CONSTRAINT "Operation_creditId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Operation" DROP CONSTRAINT "Operation_licenseId_fkey";

-- DropForeignKey
ALTER TABLE "public"."Operation" DROP CONSTRAINT "Operation_subscriptionId_fkey";

-- DropIndex
DROP INDEX "public"."ApiKey_key_key";

-- DropIndex
DROP INDEX "public"."Operation_certificateId_idx";

-- DropIndex
DROP INDEX "public"."Operation_certificateId_key";

-- AlterTable
ALTER TABLE "public"."ApiKey" DROP COLUMN "key",
DROP COLUMN "revokedAt",
ADD COLUMN     "isActive" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "keyHash" TEXT NOT NULL,
ADD COLUMN     "lastFourChars" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "public"."Operation" DROP COLUMN "certificateId",
DROP COLUMN "certificateUrl",
DROP COLUMN "creditId",
DROP COLUMN "diagnosticReport",
DROP COLUMN "licenseId",
DROP COLUMN "subscriptionId",
ADD COLUMN     "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "creditsUsed" INTEGER NOT NULL,
ADD COLUMN     "errorMessage" TEXT,
ADD COLUMN     "metadata" JSONB,
ADD COLUMN     "updatedAt" TIMESTAMP(3) NOT NULL,
ALTER COLUMN "status" SET DEFAULT 'PENDING',
ALTER COLUMN "startedAt" DROP NOT NULL,
ALTER COLUMN "startedAt" DROP DEFAULT;

-- AlterTable
ALTER TABLE "public"."User" ALTER COLUMN "firstName" DROP NOT NULL,
ALTER COLUMN "lastName" DROP NOT NULL;

-- CreateTable
CREATE TABLE "public"."UsageLog" (
    "id" TEXT NOT NULL,
    "apiKeyId" TEXT NOT NULL,
    "operationId" TEXT,
    "action" TEXT NOT NULL,
    "creditsUsed" INTEGER NOT NULL DEFAULT 0,
    "deviceSerial" TEXT,
    "deviceModel" TEXT,
    "metadata" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "UsageLog_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "UsageLog_apiKeyId_idx" ON "public"."UsageLog"("apiKeyId");

-- CreateIndex
CREATE INDEX "UsageLog_operationId_idx" ON "public"."UsageLog"("operationId");

-- CreateIndex
CREATE UNIQUE INDEX "ApiKey_keyHash_key" ON "public"."ApiKey"("keyHash");

-- CreateIndex
CREATE INDEX "Operation_status_idx" ON "public"."Operation"("status");

-- AddForeignKey
ALTER TABLE "public"."UsageLog" ADD CONSTRAINT "UsageLog_apiKeyId_fkey" FOREIGN KEY ("apiKeyId") REFERENCES "public"."ApiKey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."UsageLog" ADD CONSTRAINT "UsageLog_operationId_fkey" FOREIGN KEY ("operationId") REFERENCES "public"."Operation"("id") ON DELETE SET NULL ON UPDATE CASCADE;
