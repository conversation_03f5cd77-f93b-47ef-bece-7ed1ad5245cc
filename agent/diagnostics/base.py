"""Common diagnostic interfaces and registry."""
from __future__ import annotations

import abc
from dataclasses import dataclass, asdict
from typing import Callable, Dict, List, Type

__all__ = [
    "DiagnosticResult",
    "Diagnostic",
    "registry",
    "register",
]


@dataclass(slots=True)
class DiagnosticResult:
    """Generic result structure for a diagnostic test."""

    status: str  # "pass" / "fail" / "error"
    data: Dict[str, object]

    def as_dict(self) -> Dict[str, object]:
        return {"status": self.status, **self.data}


class Diagnostic(abc.ABC):
    """Abstract base class for diagnostics."""

    #: Short unique name of diagnostic (e.g. "cpu_stress")
    name: str

    @abc.abstractmethod
    def run(self) -> DiagnosticResult:  # noqa: D401
        """Run the diagnostic and return a result."""


# ---------------------------------------------------------------------------
# Registry helpers
# ---------------------------------------------------------------------------

registry: Dict[str, Type[Diagnostic]] = {}


def register(cls: Type[Diagnostic]) -> Type[Diagnostic]:
    """Class decorator that registers a Diagnostic subclass in *registry*."""
    if not issubclass(cls, Diagnostic):
        raise TypeError("Only Diagnostic subclasses can be registered")
    if cls.name in registry:
        raise KeyError(f"Diagnostic with name '{cls.name}' already registered")
    registry[cls.name] = cls
    return cls
