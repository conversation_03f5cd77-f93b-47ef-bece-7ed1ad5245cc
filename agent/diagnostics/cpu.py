"""CPU stress-test utilities for the Crucible Crucible."""
from __future__ import annotations

import datetime as _dt
import logging
import multiprocessing as _mp
import time
from dataclasses import dataclass, field
from typing import Dict, Tuple

import psutil
from agent.diagnostics.base import Diagnostic, DiagnosticResult, register

log = logging.getLogger(__name__)

# Attempt to determine which backend we will use once, at import time.
try:
    import numpy as _np  # type: ignore

    _USE_NUMPY = True
except ImportError:  # pragma: no cover – numpy optional dependency
    _np = None  # type: ignore
    _USE_NUMPY = False
    log.info("numpy not available – CPU stress test will use fallback implementation")


@dataclass(slots=True)
class CpuTestResult:
    status: str
    fail_reason: str | None
    duration_seconds: int
    avg_cpu_load: float
    max_cpu_load: float
    temperature_delta: Dict[str, float] = field(default_factory=dict)

    started_at: str = ""
    finished_at: str = ""

    def as_dict(self) -> Dict[str, object]:
        return {
            "status": self.status,
            "fail_reason": self.fail_reason,
            "duration_seconds": self.duration_seconds,
            "avg_cpu_load": self.avg_cpu_load,
            "max_cpu_load": self.max_cpu_load,
            "temperature_delta": self.temperature_delta,
        }


# ---------------------------------------------------------------------------
# Internal helpers
# ---------------------------------------------------------------------------

def _cpu_task_numpy(end_time: float) -> None:  # pragma: no cover
    """Matrix multiplication loop to keep CPUs busy until *end_time*."""
    assert _np is not None
    matrix_size = 128
    while time.time() < end_time:
        a = _np.random.rand(matrix_size, matrix_size)
        b = _np.random.rand(matrix_size, matrix_size)
        _ = a @ b  # noqa: E221


def _cpu_task_fallback(end_time: float) -> None:  # pragma: no cover
    while time.time() < end_time:
        # Arbitrary arithmetic workload
        for i in range(1000000):
            _ = (i * i / 3.14159) ** 0.5
            if i % 100000 == 0 and time.time() >= end_time:
                break


_CPU_TASK = _cpu_task_numpy if _USE_NUMPY else _cpu_task_fallback


# ---------------------------------------------------------------------------
# Public API
# ---------------------------------------------------------------------------

def run_stress_test(duration_seconds: int = 5) -> CpuTestResult:
    """Run a CPU stress-test and return a *CpuTestResult*."""
    start_dt = _dt.datetime.now()
    start_time = time.time()
    end_time = start_time + duration_seconds

    # Capture starting temperatures (best-effort)
    start_temps: Dict[str, float] = {}
    try:
        for chip, sensors in psutil.sensors_temperatures().items():
            for s in sensors:
                start_temps[f"{chip}_{s.label or 'unk'}"] = s.current
    except (AttributeError, RuntimeError):
        pass

    # Spawn worker processes (one per physical core)
    workers: Tuple[_mp.Process, ...] = tuple(
        _mp.Process(target=_CPU_TASK, args=(end_time,)) for _ in range(psutil.cpu_count(logical=False) or 1)
    )
    for w in workers:
        w.start()

    # Monitor CPU load
    samples = []
    while time.time() < end_time:
        samples.append(psutil.cpu_percent(interval=0.5))

    for w in workers:
        if w.is_alive():
            w.terminate()
        w.join()

    # Ending temperatures
    end_temps: Dict[str, float] = {}
    try:
        for chip, sensors in psutil.sensors_temperatures().items():
            for s in sensors:
                end_temps[f"{chip}_{s.label or 'unk'}"] = s.current
    except (AttributeError, RuntimeError):
        pass

    avg_cpu = sum(samples) / len(samples) if samples else 0.0
    max_cpu = max(samples) if samples else 0.0

    status = "pass" if max_cpu >= 50 else "fail"
    fail_reason = None if status == "pass" else f"Max CPU load only {max_cpu:.1f}%"

    delta = {k: end_temps.get(k, 0) - start_temps.get(k, 0) for k in set(start_temps) | set(end_temps)}

    result = CpuTestResult(
        status=status,
        fail_reason=fail_reason,
        duration_seconds=duration_seconds,
        avg_cpu_load=avg_cpu,
        max_cpu_load=max_cpu,
        temperature_delta=delta,
    )
    result.started_at = start_dt.isoformat()
    result.finished_at = _dt.datetime.now().isoformat()
    return result


# ---------------------------------------------------------------------------
# Diagnostic wrapper class
# ---------------------------------------------------------------------------

@register
class CpuStressDiagnostic(Diagnostic):
    """Diagnostic plug-in wrapper around run_stress_test()."""

    name = "cpu_stress"

    def run(self) -> CpuTestResult:  # type: ignore[override]
        return run_stress_test()
