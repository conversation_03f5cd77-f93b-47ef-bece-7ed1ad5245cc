"""Thin wrapper around *NexusClient* providing typed helper functions."""
from __future__ import annotations

import logging
from typing import Any, Dict

from rich.console import Console

from agent.nexus_client import NexusClient

log = logging.getLogger(__name__)
console = Console()


async def send_result(server_url: str, result: Dict[str, Any]) -> bool:
    """Post *result* to the given *server_url*.

    Returns *True* on success, otherwise *False* (and logs / prints the error).
    """
    client = NexusClient(server_url)
    success, msg = await client.post_result_async(result)
    if success:
        console.print("[green]Result sent successfully![/green]")
    else:
        console.print(f"[yellow]{msg}[/yellow]")
    return success
