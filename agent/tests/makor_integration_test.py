#!/usr/bin/env python3
"""
Test module for the Makor ERP integration.
This test allows verifying that the Makor ERP integration is properly configured
and can successfully send audit reports to the Makor ERP system.
"""
import logging
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, Any, List

from agent.tests.test_framework import test, TestCategory, TestSeverity, TestStatus
from agent.integrations.makor_erp import (
    send_wipe_results_to_makor,
    generate_audit_report_xml,
    MAKOR_ENABLED,
    MAKOR_API_URL
)
from agent.hardware.system_info import get_system_info
from agent.hardware.drive_info import get_detailed_drive_info

LOGGER = logging.getLogger(__name__)

class MakorIntegrationTestWindow(tk.Toplevel):
    """Window for testing the Makor ERP integration."""

    def __init__(self, parent: tk.Tk, log_callback=None):
        super().__init__(parent)
        self.title("Makor ERP Integration Test")
        self.grab_set()  # Make modal
        
        self.log_callback = log_callback
        self.result_var = tk.StringVar(value="Not tested")
        self.test_result = None
        
        self._build_ui()
        self._check_config()
        
    def _log(self, message, level="info"):
        """Log a message to the GUI and callback if available."""
        if self.log_callback:
            self.log_callback(message, level)
        self.log_text.insert(tk.END, f"[{level.upper()}] {message}\n")
        self.log_text.see(tk.END)
        
    def _build_ui(self):
        """Build the user interface."""
        # Main frame
        main_frame = ttk.Frame(self, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Configuration status
        config_frame = ttk.LabelFrame(main_frame, text="Configuration Status", padding=5)
        config_frame.pack(fill=tk.X, pady=5)
        
        self.enabled_label = ttk.Label(config_frame, text="Enabled: Checking...")
        self.enabled_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.url_label = ttk.Label(config_frame, text="API URL: Checking...")
        self.url_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        
        # Test options
        options_frame = ttk.LabelFrame(main_frame, text="Test Options", padding=5)
        options_frame.pack(fill=tk.X, pady=5)
        
        self.include_system_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Include System Information", variable=self.include_system_var).grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.include_drives_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Include Drive Information", variable=self.include_drives_var).grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.include_wipe_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Include Simulated Wipe Results", variable=self.include_wipe_var).grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        
        # Log area
        log_frame = ttk.LabelFrame(main_frame, text="Test Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        self.log_text = tk.Text(log_frame, height=10, width=60, wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True, side=tk.LEFT)
        
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(fill=tk.Y, side=tk.RIGHT)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
        # Result and buttons
        result_frame = ttk.Frame(main_frame)
        result_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(result_frame, text="Test Result:").grid(row=0, column=0, sticky=tk.W, padx=5)
        ttk.Label(result_frame, textvariable=self.result_var).grid(row=0, column=1, sticky=tk.W, padx=5)
        
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        self.test_button = ttk.Button(button_frame, text="Run Test", command=self.run_test)
        self.test_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Close", command=self.on_close).pack(side=tk.RIGHT, padx=5)
        
    def _check_config(self):
        """Check and display the current configuration status."""
        # Check if integration is enabled
        if MAKOR_ENABLED:
            self.enabled_label.config(text="Enabled: Yes", foreground="green")
        else:
            self.enabled_label.config(text="Enabled: No", foreground="red")
            self.test_button.config(state="disabled")
            self._log("Makor ERP integration is disabled in configuration. Enable it in makor_config.json to run this test.", "warning")
        
        # Check API URL
        if MAKOR_API_URL and MAKOR_API_URL != "http://your-makor-erp-api-url.com":
            self.url_label.config(text=f"API URL: {MAKOR_API_URL}", foreground="green")
        else:
            self.url_label.config(text="API URL: Not configured", foreground="red")
            self.test_button.config(state="disabled")
            self._log("Makor ERP API URL is not configured. Update it in makor_config.json to run this test.", "warning")
    
    def run_test(self):
        """Run the integration test."""
        self._log("Starting Makor ERP integration test...", "info")
        self.test_button.config(state="disabled")
        self.result_var.set("Testing...")
        
        try:
            # Get system information if requested
            system_info = {}
            if self.include_system_var.get():
                self._log("Collecting system information...", "info")
                system_info = get_system_info()
                self._log(f"System information collected: {system_info.get('serial_number', 'N/A')} - {system_info.get('model', 'N/A')}", "info")
            
            # Get drive information if requested
            drives_info = []
            if self.include_drives_var.get():
                self._log("Collecting drive information...", "info")
                drives_info = get_detailed_drive_info()
                self._log(f"Drive information collected for {len(drives_info)} drives", "info")
                for drive in drives_info:
                    self._log(f"Drive: {drive.get('path')} - {drive.get('model', 'N/A')} - {drive.get('size_gb', 'N/A')} GB", "info")
            
            # Create simulated wipe results if requested
            wipe_results = []
            if self.include_wipe_var.get():
                self._log("Creating simulated wipe results...", "info")
                for drive in drives_info:
                    wipe_result = {
                        "device_path": drive.get("path"),
                        "method": "test_method",
                        "status": "pass",
                        "details": "Test wipe (simulated)",
                        "errors": "",
                        "verification": "Test verification",
                        "timestamp_start": "2023-01-01T00:00:00",
                        "timestamp_end": "2023-01-01T00:30:00"
                    }
                    wipe_results.append(wipe_result)
                self._log(f"Created simulated wipe results for {len(wipe_results)} drives", "info")
            
            # Generate XML report
            self._log("Generating XML audit report...", "info")
            xml_report = generate_audit_report_xml(system_info, drives_info, wipe_results)
            self._log("XML audit report generated successfully", "info")
            self._log(f"XML Report Preview: {xml_report[:200]}...", "info")
            
            # Send report to Makor ERP
            self._log("Sending audit report to Makor ERP...", "info")
            success = send_wipe_results_to_makor(wipe_results)
            
            if success:
                self._log("Audit report sent successfully to Makor ERP", "info")
                self.result_var.set("PASS")
                self.test_result = (TestStatus.PASS, "Makor ERP integration test passed")
                messagebox.showinfo("Test Result", "Makor ERP integration test passed successfully!", parent=self)
            else:
                self._log("Failed to send audit report to Makor ERP", "error")
                self.result_var.set("FAIL")
                self.test_result = (TestStatus.FAIL, "Failed to send audit report to Makor ERP")
                messagebox.showerror("Test Result", "Makor ERP integration test failed. Check the logs for details.", parent=self)
        
        except Exception as e:
            self._log(f"Error during Makor ERP integration test: {e}", "error")
            self.result_var.set("ERROR")
            self.test_result = (TestStatus.ERROR, f"Error during test: {e}")
            messagebox.showerror("Test Error", f"An error occurred during the test: {e}", parent=self)
        
        finally:
            self.test_button.config(state="normal")
    
    def on_close(self):
        """Handle window close."""
        self.destroy()
    
    def get_result(self):
        """Get the test result."""
        return self.test_result or (TestStatus.NOT_APPLICABLE, "Test not run")


@test(
    category=TestCategory.INTEGRATION,
    severity=TestSeverity.MEDIUM,
    name="Makor ERP Integration Test",
    description="Tests the integration with Makor ERP by sending a test audit report."
)
def run_makor_integration_test(**kwargs):
    """
    Test the Makor ERP integration by sending a test audit report.
    
    This test will:
    1. Check if the Makor ERP integration is properly configured
    2. Collect system information
    3. Collect drive information
    4. Create simulated wipe results
    5. Generate an XML audit report
    6. Send the report to Makor ERP
    
    Returns:
        A tuple (TestStatus, str) indicating the test result and notes.
    """
    log_callback = kwargs.get('log_callback')
    parent_window = kwargs.get('parent_window')
    
    if log_callback:
        log_callback("Starting Makor ERP integration test...", "info")
    
    # Check if integration is enabled
    if not MAKOR_ENABLED:
        if log_callback:
            log_callback("Makor ERP integration is disabled in configuration. Enable it in makor_config.json to run this test.", "warning")
        return TestStatus.SKIPPED, "Makor ERP integration is disabled in configuration"
    
    # Check if API URL is configured
    if not MAKOR_API_URL or MAKOR_API_URL == "http://your-makor-erp-api-url.com":
        if log_callback:
            log_callback("Makor ERP API URL is not configured. Update it in makor_config.json to run this test.", "warning")
        return TestStatus.SKIPPED, "Makor ERP API URL is not configured"
    
    # Create and show the test window
    test_window = MakorIntegrationTestWindow(parent_window, log_callback)
    parent_window.wait_window(test_window)
    
    # Return the test result
    return test_window.get_result()


if __name__ == "__main__":
    # For standalone testing
    root = tk.Tk()
    root.withdraw()
    
    def log_to_console(message, level="info"):
        print(f"[{level.upper()}] {message}")
    
    result = run_makor_integration_test(log_callback=log_to_console, parent_window=root)
    print(f"Test result: {result}")
    
    root.destroy()
