#!/usr/bin/env python3
"""
Web-compatible Visual RAM Test Module

This module provides a web-compatible version of the visual RAM test
that can be called from the test orchestrator when running in headless mode.
"""
import datetime
import time
from typing import Dict, Any

from agent.tests.test_framework import (
    TestCategory, TestSeverity, TestStatus, test
)


@test(
    category=TestCategory.MEMORY,
    severity=TestSeverity.HIGH,
    description="Web-compatible visual RAM test"
)
def run_web_visual_ram_test(test_size_mb=1024, duration_seconds=30, log_callback=None, **kwargs) -> Dict[str, Any]:
    """
    Run a web-compatible visual RAM test.
    
    This function provides a fallback for the visual RAM test when running in headless mode.
    It performs the same memory testing logic but without the GUI components.
    
    Args:
        test_size_mb: Memory size to test in MB
        duration_seconds: Test duration in seconds
        log_callback: Optional callback for logging
        **kwargs: Additional arguments (ignored for compatibility)
        
    Returns:
        Dictionary with test results
    """
    if log_callback:
        log_callback(f"Starting web-compatible RAM test (size: {test_size_mb} MB, duration: {duration_seconds}s)")
    
    start_dt = datetime.datetime.now()
    
    try:
        import psutil
        
        # Get system memory information
        mem_info = psutil.virtual_memory()
        total_mb = mem_info.total / (1024 * 1024)
        available_mb = mem_info.available / (1024 * 1024)
        
        if log_callback:
            log_callback(f"System Memory: {total_mb:.0f} MB total, {available_mb:.0f} MB available")
        
        # Determine actual test size
        test_size_mb = min(test_size_mb, int(available_mb * 0.25))
        test_size_bytes = test_size_mb * 1024 * 1024
        
        if log_callback:
            log_callback(f"Adjusted test size to {test_size_mb} MB based on available memory")
        
        # Test patterns
        patterns = [b'\xAA', b'\x55', b'\xFF', b'\x00']
        pattern_names = [f"0x{p[0]:02X}" for p in patterns]
        
        # Track statistics
        cycles_completed = 0
        total_errors = 0
        start_time = time.time()
        end_time = start_time + duration_seconds
        
        if log_callback:
            log_callback(f"Starting RAM test with {len(patterns)} patterns for {duration_seconds} seconds")
        
        # Main test loop
        while time.time() < end_time:
            cycles_completed += 1
            
            if log_callback:
                elapsed = time.time() - start_time
                progress = min(100, (elapsed / duration_seconds) * 100)
                log_callback(f"Cycle {cycles_completed} - Progress: {progress:.1f}%")
            
            # Allocate memory
            try:
                buf = bytearray(test_size_bytes)
            except MemoryError:
                error_msg = "Memory allocation failed - not enough available memory"
                if log_callback:
                    log_callback(error_msg, "error")
                
                end_dt = datetime.datetime.now()
                return {
                    "test_details": {
                        "status": TestStatus.FAIL.value,
                        "notes": error_msg,
                        "error_type": "MemoryError",
                        "cycles_completed": cycles_completed,
                        "patterns_tested": pattern_names,
                        "errors": 1,
                        "total_memory_mb": int(total_mb),
                        "available_memory_mb": int(available_mb),
                        "test_size_mb": test_size_mb
                    },
                    "started_at": start_dt.isoformat(),
                    "finished_at": end_dt.isoformat()
                }
            
            # Test each pattern
            for pattern_idx, pattern_byte in enumerate(patterns):
                if time.time() >= end_time:
                    break
                
                pattern_hex = f"0x{pattern_byte[0]:02X}"
                
                if log_callback:
                    log_callback(f"Testing pattern {pattern_hex} in cycle {cycles_completed}")
                
                # Write pattern
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break
                    
                    chunk_size = min(4096, test_size_bytes - i)
                    buf[i:i+chunk_size] = pattern_byte * chunk_size
                
                # Verify pattern
                errors = 0
                for i in range(0, test_size_bytes, 4096):
                    if time.time() >= end_time:
                        break
                    
                    chunk_size = min(4096, test_size_bytes - i)
                    chunk = buf[i:i+chunk_size]
                    expected = pattern_byte * chunk_size
                    
                    if chunk != expected:
                        errors += 1
                
                total_errors += errors
                if errors > 0 and log_callback:
                    log_callback(f"Pattern {pattern_hex} test found {errors} errors", "warning")
        
        # Test complete
        total_time = time.time() - start_time
        status = TestStatus.PASS if total_errors == 0 else TestStatus.FAIL
        
        if log_callback:
            log_callback(f"RAM test completed: {status.value} - {cycles_completed} cycles, {total_errors} errors in {total_time:.1f}s")
        
        end_dt = datetime.datetime.now()
        
        return {
            "test_details": {
                "status": status.value,
                "notes": f"Completed {cycles_completed} cycles with {total_errors} errors in {total_time:.1f} seconds",
                "total_memory_mb": int(total_mb),
                "available_memory_mb": int(available_mb),
                "test_size_mb": test_size_mb,
                "patterns_tested": pattern_names,
                "cycles_completed": cycles_completed,
                "errors": total_errors,
                "duration_seconds": total_time
            },
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat()
        }
    
    except Exception as e:
        if log_callback:
            log_callback(f"Error during RAM test: {str(e)}", "error")
        
        end_dt = datetime.datetime.now()
        
        return {
            "test_details": {
                "status": TestStatus.ERROR.value,
                "notes": f"Test error: {str(e)}",
                "error_type": type(e).__name__,
                "error_details": str(e)
            },
            "started_at": start_dt.isoformat(),
            "finished_at": end_dt.isoformat()
        }


# Alias for compatibility with existing test orchestrator
def run_visual_ram_test_headless(*args, **kwargs):
    """Alias for headless mode compatibility."""
    return run_web_visual_ram_test(*args, **kwargs)


if __name__ == "__main__":
    # Test the function directly
    def test_log(message, level="info"):
        print(f"[{level.upper()}] {message}")
    
    print("Running web-compatible visual RAM test...")
    result = run_web_visual_ram_test(
        test_size_mb=512,  # Smaller size for testing
        duration_seconds=10,  # Shorter duration for testing
        log_callback=test_log
    )
    
    print("\nTest Result:")
    import json
    print(json.dumps(result, indent=2))
