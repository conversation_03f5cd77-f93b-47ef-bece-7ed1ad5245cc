import unittest
import os
import json
import shutil
import time
from agent.core.result_manager import <PERSON>sul<PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any, Callable, Optional # For callback type hints

# Store original os.path.exists to avoid issues with potential mocking if we were to mock it
_os_path_exists_orig = os.path.exists

class TestResultManagerConsolidation(unittest.TestCase):
    # IMPORTANT: This test suite works by creating a directory named "results"
    # in the current working directory. This is because the method under test
    # (`consolidate_results_for_asset`) has a hardcoded `results_dir = "results"`.
    # These tests will fail if that hardcoding is changed without updating the tests.
    results_dir_name_for_test = "results"

    # Mock callbacks
    def mock_log_callback(self, message: str, level: str):
        # print(f"LOG [{level.upper()}]: {message}")
        pass

    def mock_get_asset_number_for_save(self) -> str:
        return self._current_asset_for_saving

    def mock_get_operator_id_for_save(self) -> str:
        return self._current_operator_for_saving

    def setUp(self):
        # self.test_results_path is the absolute path to the temporary "results" directory
        self.test_results_path = os.path.join(os.getcwd(), self.results_dir_name_for_test)

        # Clean up any existing "results" directory from previous runs
        if _os_path_exists_orig(self.test_results_path):
            shutil.rmtree(self.test_results_path)
        os.makedirs(self.test_results_path, exist_ok=True)

        self._current_asset_for_saving = "default_asset"
        self._current_operator_for_saving = "default_operator"

        self.result_manager = ResultManager(
            log_callback=self.mock_log_callback,
            get_asset_number_callback=self.mock_get_asset_number_for_save,
            get_operator_id_callback=self.mock_get_operator_id_for_save
        )
        # Override the results_dir used by the instance to our temporary one
        # This is a bit of a hack; ideally, ResultManager would take results_dir as a constructor arg
        # or have a method to set it. For now, we'll directly modify it.
        # We use the relative path name, as ResultManager methods seem to treat it as such.
        self.result_manager.results_dir = self.results_dir_name_for_test
        # For calls to os.makedirs, etc. within ResultManager, if it doesn't prepend cwd, this could be an issue.
        # However, save_result_to_file prepends self.results_dir, so it should work.
        # To be safe, ensure the directory exists for ResultManager's operations
        # os.makedirs(self.results_dir_name, exist_ok=True) # This line is redundant and potentially buggy, removed.


    def tearDown(self):
        if _os_path_exists_orig(self.test_results_path): # Uses absolute path for shutil.rmtree
            shutil.rmtree(self.test_results_path)
        # No need to reset ResultManager.results_dir here as each test's setUp will configure its instance.
        # If ResultManager.results_dir was a class variable affecting all instances, then reset it.
        # The current ResultManager code in the prompt does not show results_dir as a class variable,
        # but as an instance variable implicitly through its usage in save_result_to_file.
        # Re-checking ResultManager: it does `results_dir = "results"` at class level in `save_result_to_file`
        # if `self.results_dir` is not present, which is not the case here as we set it.
        # The provided ResultManager's consolidate_results_for_asset uses a local `results_dir = "results"`.
        # This is a key problem. The consolidate_results_for_asset method *must* use self.results_dir.
        # This needs to be fixed in ResultManager itself, or the test will not work as intended.
        # For now, I will assume ResultManager will be fixed or this test will have to also patch that.
        # *Self-correction*: The prompt's ResultManager.py for `consolidate_results_for_asset` indeed uses a local `results_dir = "results"`.
        # This test will fail to direct ResultManager to the temp folder for consolidation unless that is changed.
        # I will proceed by also patching `results_dir` for `consolidate_results_for_asset` within the test temporarily,
        # or more cleanly, ensure `self.result_manager.results_dir` is used by it.
        # For the purpose of this subtask, I will assume `consolidate_results_for_asset` uses `self.results_dir`.
        # If not, the tests I write here would fail in practice or test the wrong directory.

    def _create_dummy_result_file(self,
                                  asset_number_to_save: str,
                                  operator_id_to_save: str,
                                  test_name: str,
                                  data_payload: Dict[str, Any],
                                  timestamp_override: Optional[int] = None) -> Optional[str]:
        """
        Helper to create individual result files using ResultManager's save logic.
        Returns the path to the created file or None.
        """
        # Temporarily set the asset/operator for the save operation
        self._current_asset_for_saving = asset_number_to_save
        self._current_operator_for_saving = operator_id_to_save

        original_time = time.time
        if timestamp_override:
            time.time = lambda: float(timestamp_override)

        # Construct filename similar to how save_result_to_file does it to ensure it's found
        # This is because save_result_to_file itself creates the filename with a live timestamp
        # and we need to predict that for our tests or control it.
        # For simplicity, we'll let save_result_to_file do its job and find the file later if needed,
        # or use a controlled timestamp.

        # Using a controlled timestamp for predictability
        controlled_ts = timestamp_override if timestamp_override else int(original_time())

        # Temporarily override time.time for predictable filenames if not already done
        if not timestamp_override:
            time.time = lambda: float(controlled_ts)

        success = self.result_manager.save_result_to_file(test_name, data_payload)

        time.time = original_time # Restore original time function

        if success:
            # Find the created file. This is a bit complex due to the timestamp in the name.
            # We assume it's the latest file created for that asset and test.
            # This logic is simplified; in a real scenario, more robust file finding or direct return from save_result_to_file would be better.
            # For now, since we control the timestamp, we can construct the expected filename.
            safe_test_name = test_name.replace(' ', '_').replace('/', '_').replace('\\', '_')
            # The ResultManager.save_result_to_file uses int(time.time()) for the timestamp in the filename
            expected_filename = f"{self.test_results_path}/nexus_result_{asset_number_to_save}_{safe_test_name}_{controlled_ts}.json"
            if _os_path_exists_orig(expected_filename):
                return expected_filename
            # Fallback scan if timestamp had sub-second precision issues (though int() should prevent this)
            # This part is more of a defensive coding for tests and might not be strictly necessary
            # if timestamping is perfectly controlled.
            for fname in os.listdir(self.test_results_path):
                if fname.startswith(f"nexus_result_{asset_number_to_save}_{safe_test_name}_") and fname.endswith(".json"):
                    # This could pick up other files if tests run very fast; controlled timestamp is better.
                    return os.path.join(self.test_results_path, fname)
        return None

    def test_consolidation_basic(self):
        asset_A = "asset_A_123"
        op_id_consolidate = "op_consolidate_XYZ"
        op_id_save = "op_save_XYZ" # Individual files might have a different op_id

        # Create files for asset_A
        ts1 = int(time.time()) - 300
        ts2 = int(time.time()) - 200
        ts3 = int(time.time()) - 100
        self._create_dummy_result_file(asset_A, op_id_save, "CPU Test", {"detail": "cpu ok"}, timestamp_override=ts1)
        self._create_dummy_result_file(asset_A, op_id_save, "RAM Test", {"detail": "ram ok"}, timestamp_override=ts2)
        self._create_dummy_result_file(asset_A, op_id_save, "Disk Test", {"detail": "disk ok"}, timestamp_override=ts3)

        # Create a file for a different asset (should not be included)
        self._create_dummy_result_file("asset_B_456", op_id_save, "Network Test", {"detail": "net ok"}, timestamp_override=ts1)

        consolidated_file_path = self.result_manager.consolidate_results_for_asset(asset_A, op_id_consolidate)
        self.assertIsNotNone(consolidated_file_path, "Consolidation should create a file.")
        self.assertTrue(_os_path_exists_orig(consolidated_file_path), f"Consolidated file not found at {consolidated_file_path}")

        with open(consolidated_file_path, 'r') as f:
            data = json.load(f)

        self.assertEqual(data["asset_number"], asset_A)
        self.assertEqual(data["operator_id"], op_id_consolidate) # Verify operator_id from consolidation call
        self.assertTrue("consolidation_timestamp_unix" in data)
        self.assertTrue("consolidation_timestamp_iso" in data)
        self.assertIsInstance(data["consolidated_results"], list)
        self.assertEqual(len(data["consolidated_results"]), 3, "Should only consolidate files for asset_A.")

        # Verify content of one of the consolidated results (optional, but good)
        # The order of consolidated_results is not guaranteed, so search for one.
        cpu_result_found = False
        for res in data["consolidated_results"]:
            if res["test_name"] == "CPU Test":
                self.assertEqual(res["asset_number"], asset_A) # Asset number in individual file
                self.assertEqual(res["operator_id"], op_id_save)   # Operator ID in individual file
                self.assertEqual(res["result"]["detail"], "cpu ok")
                cpu_result_found = True
                break
        self.assertTrue(cpu_result_found, "CPU test result not found in consolidated data.")

        # Check that filenames of consolidated files are somewhat unique (timestamp based)
        time.sleep(1.1) # Ensure timestamp changes for next consolidation
        consolidated_file_path_2 = self.result_manager.consolidate_results_for_asset(asset_A, op_id_consolidate)
        self.assertIsNotNone(consolidated_file_path_2)
        self.assertNotEqual(consolidated_file_path, consolidated_file_path_2, "Consolidated filenames should be unique for different runs.")


    def test_consolidation_no_files(self):
        asset_no_files = "asset_C_nofiles"
        op_id_consolidate = "op_nofiles"
        consolidated_file_path = self.result_manager.consolidate_results_for_asset(asset_no_files, op_id_consolidate)

        self.assertIsNotNone(consolidated_file_path, "Consolidation should still create a file even if no individual results exist.")
        self.assertTrue(_os_path_exists_orig(consolidated_file_path))

        with open(consolidated_file_path, 'r') as f:
            data = json.load(f)

        self.assertEqual(data["asset_number"], asset_no_files)
        self.assertEqual(data["operator_id"], op_id_consolidate)
        self.assertIsInstance(data["consolidated_results"], list)
        self.assertEqual(len(data["consolidated_results"]), 0)

    def test_consolidation_one_file(self):
        asset_one_file = "asset_D_onefile"
        op_id_consolidate = "op_onefile_C"
        op_id_save = "op_onefile_S"
        ts = int(time.time()) - 50
        payload = {"status": "pass", "value": 42}

        self._create_dummy_result_file(asset_one_file, op_id_save, "Single Test", payload, timestamp_override=ts)

        consolidated_file_path = self.result_manager.consolidate_results_for_asset(asset_one_file, op_id_consolidate)
        self.assertIsNotNone(consolidated_file_path)
        self.assertTrue(_os_path_exists_orig(consolidated_file_path))

        with open(consolidated_file_path, 'r') as f:
            data = json.load(f)

        self.assertEqual(data["asset_number"], asset_one_file)
        self.assertEqual(data["operator_id"], op_id_consolidate)
        self.assertEqual(len(data["consolidated_results"]), 1)

        single_res = data["consolidated_results"][0]
        self.assertEqual(single_res["test_name"], "Single Test")
        self.assertEqual(single_res["asset_number"], asset_one_file)
        self.assertEqual(single_res["operator_id"], op_id_save)
        self.assertEqual(single_res["result"], payload)

if __name__ == '__main__':
    unittest.main()
