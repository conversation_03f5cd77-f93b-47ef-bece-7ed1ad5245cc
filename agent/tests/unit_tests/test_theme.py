import unittest
import tkinter as tk
from tkinter import ttk
import tkinter.font
from agent.gui.main_window import NexusApp
from agent.gui.theme import COLORS, FONTS, SIZES

class TestThemeIntegration(unittest.TestCase):

    @classmethod
    def setUpClass(cls):
        # It's possible Tkinter applications don't like being instantiated multiple times
        # or without a proper mainloop in some environments.
        # This setup might need adjustment if NexusApp() has issues.
        try:
            # Attempt to create a root window temporarily for NexusApp, then withdraw it.
            # This might help in environments where a default root isn't implicitly available or desired.
            cls.root = tk.Tk()
            cls.root.withdraw() # Hide the root window
            cls.app = NexusApp() # NexusApp creates its own Toplevel which becomes the main window
            # We need to ensure styles are applied.
            # __init__ calls setup_ui_styles and setup_ui.
        except tk.TclError as e:
            print(f"TclError during NexusApp initialization in test: {e}")
            print("This might be due to the testing environment not fully supporting Tkinter GUI instantiation.")
            cls.app = None # Allow tests to be skipped or fail gracefully
            cls.root = None
        except Exception as e:
            print(f"Unexpected error during NexusApp initialization: {e}")
            cls.app = None
            cls.root = None


    @classmethod
    def tearDownClass(cls):
        if hasattr(cls, 'app') and cls.app:
            try:
                cls.app.destroy()
            except tk.TclError:
                pass # Ignore errors during destroy in test teardown
        if hasattr(cls, 'root') and cls.root:
            try:
                cls.root.destroy()
            except tk.TclError:
                pass


    def test_app_background_color(self):
        if not self.app:
            self.skipTest("NexusApp could not be initialized.")
        # Check the root window's background
        # Tk.cget('bg') might not be reliable for the main window if it's entirely covered by frames.
        # Instead, let's check a main frame if possible, or rely on ttk style.
        style = ttk.Style()
        # Note: "." refers to the root style. NexusApp itself is a tk.Tk window.
        # Its background is configured via self.configure(bg=COLORS["bg_dark"]) in setup_ui
        self.assertEqual(self.app.cget("bg"), COLORS["bg_dark"], "App background should match COLORS['bg_dark']")
        
        # Check default foreground for styled widgets, assuming it's set via "." style in setup_ui_styles
        self.assertEqual(style.lookup(".", "foreground"), COLORS["text_light"], "Default widget foreground should match COLORS['text_light']")
        self.assertEqual(style.lookup(".", "background"), COLORS["bg_dark"], "Default widget background should match COLORS['bg_dark']")


    def test_log_area_style(self):
        if not self.app:
            self.skipTest("NexusApp could not be initialized.")
        log_area = self.app.log_text_area
        self.assertEqual(log_area.cget("background"), COLORS["bg_dark"])
        self.assertEqual(log_area.cget("foreground"), COLORS["text_light"])
        
        # Robust font checking using tkinter.font.Font
        log_font_config = FONTS["log"] # Expected: (family, size, [style])
        log_font_actual = tkinter.font.Font(font=log_area.cget("font")).actual()
        
        self.assertEqual(log_font_actual["family"], log_font_config[0], "Log area font family mismatch")
        self.assertEqual(log_font_actual["size"], log_font_config[1], "Log area font size mismatch")
        
        expected_weight = "normal"
        if len(log_font_config) > 2 and "bold" in log_font_config[2].lower():
            expected_weight = "bold"
        self.assertEqual(log_font_actual["weight"], expected_weight, "Log area font weight mismatch")


    def test_button_style(self):
        if not self.app:
            self.skipTest("NexusApp could not be initialized.")
        style = ttk.Style()
        
        # Check general TButton style configured in setup_ui_styles
        self.assertEqual(style.lookup("TButton", "background"), COLORS["button"], "TButton background mismatch")
        self.assertEqual(style.lookup("TButton", "foreground"), COLORS["button_text"], "TButton foreground mismatch")
        
        # Padding check
        # STYLES["padding_m"] is an int. ttk.Style().lookup() for padding returns a list of strings.
        # e.g. if padding=5, it might be ['5', '5', '5', '5'] or just ['5'] if uniform.
        # The style is set with `padding=STYLES["padding_m"]` in `setup_ui_styles`.
        # This usually implies uniform padding.
        button_padding_actual = style.lookup("TButton", "padding")
        # In main_window.py, padding is set to self.responsive_padding["medium"] or SIZES["padding_medium"]
        # For testing, we'll assume SIZES["padding_medium"] as a baseline or if responsive_padding isn't complexly mocked.
        # If NexusApp is initialized, self.responsive_padding should be set.
        # If self.app.responsive_padding is available and not None, use its "medium" value.
        if self.app and hasattr(self.app, 'responsive_padding') and self.app.responsive_padding:
            expected_padding_val = self.app.responsive_padding.get("medium", SIZES["padding_medium"])
        else:
            expected_padding_val = SIZES["padding_medium"]
        expected_padding_str = str(expected_padding_val)

        self.assertTrue(all(p == expected_padding_str for p in button_padding_actual), 
                        f"TButton padding mismatch. Expected uniform '{expected_padding_str}', got {button_padding_actual}")

        # Relief is hardcoded to tk.RAISED in main_window.py's setup_ui_styles
        self.assertEqual(style.lookup("TButton", "relief"), tk.RAISED, "TButton relief mismatch")
        
        # Borderwidth is hardcoded to 0 in main_window.py's setup_ui_styles
        self.assertEqual(str(style.lookup("TButton", "borderwidth")), "0", "TButton borderwidth mismatch")


    def test_header_label_style(self):
        if not self.app:
            self.skipTest("NexusApp could not be initialized.")
        style = ttk.Style()
        
        # Using FONTS["title"] as FONTS["large_bold_font"] is not defined in theme.py
        expected_font_config = FONTS["title"] # Expected: (family, size, style)
        
        # Get the actual font properties for Header.TLabel
        header_font_actual = tkinter.font.Font(font=style.lookup("Header.TLabel", "font")).actual()

        self.assertEqual(style.lookup("Header.TLabel", "foreground"), COLORS["primary"], "Header.TLabel foreground mismatch")
        self.assertEqual(style.lookup("Header.TLabel", "background"), COLORS["bg_dark"], "Header.TLabel background mismatch")
        
        self.assertEqual(header_font_actual["family"], expected_font_config[0], "Header.TLabel font family mismatch")
        self.assertEqual(header_font_actual["size"], expected_font_config[1], "Header.TLabel font size mismatch")
        
        expected_weight = "normal"
        if len(expected_font_config) > 2 and "bold" in expected_font_config[2].lower():
            expected_weight = "bold"
        self.assertEqual(header_font_actual["weight"], expected_weight, "Header.TLabel font weight mismatch")


if __name__ == '__main__':
    # This allows running the tests directly from this file
    # Create a dummy Tk root if not running via unittest discover which might handle it
    # However, setUpClass should handle root window creation.
    unittest.main()
