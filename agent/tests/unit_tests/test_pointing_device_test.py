import unittest
from agent.tests.pointing_device_test import TestUIConfig

class TestPointingDeviceUIConfig(unittest.TestCase):
    """
    Unit tests for the TestUIConfig class to ensure default values are as expected.
    """

    def test_default_config_values(self):
        """
        Tests that an instance of TestUIConfig has the expected default values.
        """
        config = TestUIConfig()

        # Assertions for values from create_test_elements_now
        self.assertEqual(config.title_y_offset, 120)
        self.assertEqual(config.circle_radius, 40)
        self.assertEqual(config.circle_center_y, 370)
        self.assertEqual(config.pointer_circle_outline_width, 2)

        self.assertEqual(config.touchpad_top_y, 500)
        self.assertEqual(config.touchpad_height, 180)
        self.assertEqual(config.touchpad_width, 400)
        self.assertEqual(config.touchpad_outline_width, 2)

        self.assertEqual(config.button_height, 50)
        self.assertEqual(config.button_width, 130)
        self.assertEqual(config.button_spacing_divisor, 3)
        self.assertEqual(config.button_outline_width, 2)
        
        self.assertEqual(config.button_y_offset_above_touchpad, 5)
        self.assertEqual(config.button_y_offset_below_touchpad, 5)

        self.assertEqual(config.instruction_y_offset_from_bottom_ref_point, 60)

        self.assertEqual(config.done_button_width, 300)
        self.assertEqual(config.done_button_height, 50)
        self.assertEqual(config.done_button_padding_from_edge, 30)
        self.assertEqual(config.done_button_outline_width, 3)

        # Assertions for values from exit_fullscreen
        self.assertEqual(config.exit_fullscreen_width, 1200)
        self.assertEqual(config.exit_fullscreen_height, 900)

if __name__ == '__main__':
    unittest.main()
