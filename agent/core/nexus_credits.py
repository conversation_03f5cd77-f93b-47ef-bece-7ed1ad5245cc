"""
Minimal Nexus Credits client for Crucible.

For now, this only implements starting an operation to consume a credit
with the Nexus website API.

Configuration via environment variables:
- NEXUS_WEBSITE_BASE_URL (e.g., https://nexus.example.com)
- NEXUS_API_KEY (Bearer token from Nexus website)
"""
from __future__ import annotations
import os
import logging
from typing import Optional, Dict, Any
import json
from pathlib import Path

try:
    import requests
except ImportError:  # Should be present per requirements.txt
    requests = None  # type: ignore

logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


class OperationType:
    STANDARD_WIPE = "STANDARD_WIPE"
    WIPE_WITH_DIAGNOSTICS = "WIPE_WITH_DIAGNOSTICS"
    DIAGNOSTICS_ONLY = "DIAGNOSTICS_ONLY"


class OperationStatus:
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    CANCELED = "CANCELED"


def _read_agent_file_config() -> Dict[str, str]:
    """
    Read optional agent config file to discover Nexus API base URL without env vars.
    Search order:
      - $XDG_CONFIG_HOME/nexus/agent.json
      - ~/.config/nexus/agent.json
      - ~/.nexus_agent.json (legacy)
    Example contents: { "api_base": "http://nexus.lan:8080/api" }
    """
    candidates = []
    xdg = os.getenv("XDG_CONFIG_HOME")
    if xdg:
        candidates.append(Path(xdg) / "nexus" / "agent.json")
    candidates.append(Path.home() / ".config" / "nexus" / "agent.json")
    candidates.append(Path.home() / ".nexus_agent.json")
    for p in candidates:
        try:
            if p.is_file():
                with p.open("r", encoding="utf-8") as f:
                    data = json.load(f)
                    if isinstance(data, dict):
                        api_base = str(data.get("api_base", "")).strip()
                        if api_base:
                            return {"api_base": api_base.rstrip("/")}
        except Exception:
            continue
    return {}


def _config() -> Dict[str, str]:
    # Prefer Nexus API relay mode (no website key on agent)
    file_cfg = _read_agent_file_config()
    api_base = file_cfg.get("api_base") or os.getenv("NEXUS_API_BASE", "").strip()
    if api_base:
        return {"api_base": api_base.rstrip("/")}

    # Sensible default for local/all-in-one deployments
    default_api = "http://localhost:8080/api"
    # If resolvable, we'll try it (we can't test resolution here, so just return it)
    return {
        "api_base": default_api,
        # Direct website mode fallbacks (if someone explicitly sets envs)
        "base_url": os.getenv("NEXUS_WEBSITE_BASE_URL", "").rstrip("/"),
        "api_key": os.getenv("NEXUS_API_KEY", "").strip(),
    }


def start_operation(
    operation_type: str,
    device_serial: str,
    device_model: str,
    metadata: Optional[Dict[str, Any]] = None,
    timeout_seconds: int = 10,
) -> Optional[str]:
    """
    Start a credit-consuming operation with the Nexus website API.

    Returns operationId string on success, or None on failure.
    """
    cfg = _config()

    # Prefer Nexus API relay
    api_base = cfg.get("api_base", "")
    if api_base:
        if requests is None:
            logger.error("requests library not available; cannot call Nexus API")
            return None
        # Normalize path: ensure single /api
        path = "/credits/consume"
        url = f"{api_base}{path if path.startswith('/') else '/' + path}"
        headers = {"Content-Type": "application/json"}
        payload = {
            "operationType": operation_type,
            "deviceSerial": device_serial,
            "deviceModel": device_model,
            "metadata": metadata or {},
        }
        try:
            resp = requests.post(url, headers=headers, json=payload, timeout=timeout_seconds)
            if not resp.ok:
                body = resp.text[:300] if resp.text else ""
                logger.error("Nexus API consume failed: HTTP %s %s: %s", resp.status_code, resp.reason, body)
                return None
            data = resp.json()
            if data.get("success") and data.get("operationId"):
                op_id = data["operationId"]
                logger.info("Nexus API: started operation %s (id=%s)", operation_type, op_id)
                return op_id
            logger.error("Nexus API: unexpected response: %s", data)
            return None
        except requests.RequestException as e:
            logger.error("Error calling Nexus API consume endpoint: %s", e)
            return None

    # Direct website mode (env-based) fallback
    base_url = cfg.get("base_url", "")
    api_key = cfg.get("api_key", "")
    if not base_url or not api_key:
        logger.warning("Nexus API base not configured and no website API env provided; skipping credit consume. Create ~/.config/nexus/agent.json with {\"api_base\":\"http://<server>:8080/api\"}.")
        return None

    if requests is None:
        logger.error("requests library not available; cannot call Nexus website API")
        return None

    url = f"{base_url}/api/credits/consume"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    payload = {
        "operationType": operation_type,
        "deviceSerial": device_serial,
        "deviceModel": device_model,
        "metadata": metadata or {},
    }

    try:
        resp = requests.post(url, headers=headers, json=payload, timeout=timeout_seconds)

        if resp.status_code == 401:
            logger.error("Nexus API key rejected (401). Check NEXUS_API_KEY.")
            return None
        if resp.status_code == 402:
            logger.warning("Insufficient credits (402) for operation %s", operation_type)
            return None
        if not resp.ok:
            # Log trimmed body for context
            body = resp.text[:300] if resp.text else ""
            logger.error("Credit consume failed: HTTP %s %s: %s", resp.status_code, resp.reason, body)
            return None

        data = resp.json()
        if data.get("success") and data.get("operationId"):
            op_id = data["operationId"]
            credits = data.get("creditsConsumed")
            logger.info("Started operation %s (id=%s). Consumed credits=%s", operation_type, op_id, credits)
            return op_id
        else:
            logger.error("Unexpected response from Nexus consume endpoint: %s", data)
            return None

    except requests.RequestException as e:
        logger.error("Error calling Nexus consume endpoint: %s", e)
        return None


def complete_operation(
    operation_id: str,
    success: bool,
    metadata: Optional[Dict[str, Any]] = None,
    error_message: Optional[str] = None,
    timeout_seconds: int = 10,
) -> bool:
    """
    Complete/Fail an operation. Failed/canceled should refund credits.

    Returns True on success, False on failure.
    """
    cfg = _config()

    # Prefer Nexus API relay
    api_base = cfg.get("api_base", "")
    if api_base:
        if requests is None:
            logger.error("requests library not available; cannot call Nexus API")
            return False
        url = f"{api_base}/credits/consume"
        headers = {"Content-Type": "application/json"}
        status_str = OperationStatus.COMPLETED if success else OperationStatus.FAILED
        payload: Dict[str, Any] = {
            "operationId": operation_id,
            "status": status_str,
            "metadata": metadata or {},
        }
        if not success and error_message:
            payload["errorMessage"] = error_message
        try:
            resp = requests.patch(url, headers=headers, json=payload, timeout=timeout_seconds)
            if not resp.ok:
                body = resp.text[:300] if resp.text else ""
                logger.error("Nexus API completion failed: HTTP %s %s: %s", resp.status_code, resp.reason, body)
                return False
            data = resp.json()
            if data.get("success"):
                logger.info("Nexus API: operation %s marked %s", operation_id, status_str)
                return True
            logger.error("Nexus API: unexpected completion response: %s", data)
            return False
        except requests.RequestException as e:
            logger.error("Error calling Nexus API completion endpoint: %s", e)
            return False

    # Direct website mode fallback (env-based)
    base_url = cfg.get("base_url", "")
    api_key = cfg.get("api_key", "")
    if not base_url or not api_key:
        logger.warning("Nexus API base not configured and no website API env provided; skipping operation completion. Create ~/.config/nexus/agent.json with api_base.")
        return False

    if requests is None:
        logger.error("requests library not available; cannot call Nexus website API")
        return False

    url = f"{base_url}/api/credits/consume"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    status_str = OperationStatus.COMPLETED if success else OperationStatus.FAILED
    payload: Dict[str, Any] = {
        "operationId": operation_id,
        "status": status_str,
        "metadata": metadata or {},
    }
    if not success and error_message:
        payload["errorMessage"] = error_message

    try:
        resp = requests.patch(url, headers=headers, json=payload, timeout=timeout_seconds)

        if resp.status_code == 401:
            logger.error("Nexus API key rejected (401) on completion. Check NEXUS_API_KEY.")
            return False
        if not resp.ok:
            body = resp.text[:300] if resp.text else ""
            logger.error("Operation completion failed: HTTP %s %s: %s", resp.status_code, resp.reason, body)
            return False

        data = resp.json()
        if data.get("success"):
            op = data.get("operation", {})
            if op.get("refunded"):
                logger.info("Operation %s marked %s and refunded", operation_id, status_str)
            else:
                logger.info("Operation %s marked %s", operation_id, status_str)
            return True
        else:
            logger.error("Unexpected response from Nexus completion endpoint: %s", data)
            return False

    except requests.RequestException as e:
        logger.error("Error calling Nexus completion endpoint: %s", e)
        return False
