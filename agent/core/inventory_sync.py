import os
import json
import logging
from typing import Dict, Any

try:
    import requests  # External but already used elsewhere in the project
except ImportError:  # Graceful fallback if requests is not installed yet
    requests = None  # type: ignore

logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


def _inventory_config() -> Dict[str, str]:
    """Load inventory API configuration from environment variables.

    ENV NAMES:
      INVENTORY_API_URL  - Full URL to POST consolidated result JSON.
      INVENTORY_API_KEY  - Optional bearer/API key token.
    """
    return {
        'url': os.getenv('INVENTORY_API_URL', '').strip(),
        'api_key': os.getenv('INVENTORY_API_KEY', '').strip(),
    }


def send_consolidated_to_inventory(asset_number: str, consolidated_data: Dict[str, Any]) -> bool:
    """Push consolidated results to the inventory system.

    Returns True on success, False otherwise.  Function is intentionally silent on
    exception – callers should rely on the boolean or log review.
    """
    cfg = _inventory_config()
    url = cfg['url']
    if not url:
        logger.debug('INVENTORY_API_URL not set; inventory sync skipped.')
        return False

    if requests is None:
        logger.error('requests library not available – cannot sync to inventory.')
        return False

    headers = {'Content-Type': 'application/json'}
    if cfg['api_key']:
        headers['Authorization'] = f'Bearer {cfg["api_key"]}'

    payload = {
        'asset_number': asset_number,
        'results': consolidated_data
    }

    try:
        resp = requests.post(url, headers=headers, json=payload, timeout=10)
        if resp.ok:
            logger.info(f'Consolidated results for {asset_number} sent to inventory ({resp.status_code}).')
            return True
        else:
            logger.error(f'Inventory sync failed for {asset_number}: HTTP {resp.status_code} - {resp.text[:200]}')
            return False
    except Exception as e:
        logger.error(f'Error while syncing results for {asset_number} to inventory: {e}')
        return False
