import json
import os
from typing import Dict, Any, Optional # Optional might be useful

# Define the directory for storing device condition files
CONDITIONS_DIR = "device_conditions"

def get_conditions_file_path(asset_number: str) -> str:
    """
    Get the path to the conditions file for the specified asset.
    Args:
        asset_number: The asset number to use in the filename
    Returns:
        Path to the conditions file, ensuring the directory exists.
    """
    if not os.path.exists(CONDITIONS_DIR):
        try:
            os.makedirs(CONDITIONS_DIR)
        except OSError as e:
            # Handle potential race condition if another process creates it
            if not os.path.isdir(CONDITIONS_DIR):
                raise # Reraise if it's not a directory issue

    # Sanitize asset number for use as filename
    safe_asset = "".join(c if c.isalnum() else "_" for c in asset_number.strip()) if asset_number else "unknown_asset"
    return os.path.join(CONDITIONS_DIR, f"conditions_{safe_asset}.json")

def save_device_conditions(conditions: Dict[str, Any], asset_number: str) -> bool:
    """
    Save device conditions to a file.
    Args:
        conditions: Dictionary of device conditions
        asset_number: The asset number to use in the filename
    Returns:
        True if successful, False otherwise
    """
    if not asset_number or not asset_number.strip():
        # Consider logging this or raising an error, as asset_number is crucial
        print("Error: Asset number is required to save device conditions.")
        return False
    try:
        file_path = get_conditions_file_path(asset_number)
        with open(file_path, 'w') as f:
            json.dump(conditions, f, indent=2)
        # print(f"Device conditions for asset '{asset_number}' saved to {file_path}") # Optional: for server logging
        return True
    except Exception as e:
        print(f"Error saving device conditions for asset '{asset_number}': {e}")
        return False

def load_device_conditions(asset_number: str) -> Dict[str, Any]:
    """
    Load device conditions from a file.
    Args:
        asset_number: The asset number to use in the filename
    Returns:
        Dictionary of device conditions or empty dict if not found or error.
    """
    if not asset_number or not asset_number.strip():
        # print("Warning: Asset number is required to load device conditions. Returning empty.")
        return {}
    try:
        file_path = get_conditions_file_path(asset_number)
        if os.path.exists(file_path):
            with open(file_path, 'r') as f:
                return json.load(f)
    except Exception as e:
        print(f"Error loading device conditions for asset '{asset_number}': {e}")

    return {} # Return empty dict if file not found or on error

# Example default structure (could be defined here or elsewhere if needed by backend)
DEFAULT_DEVICE_CONDITIONS_STRUCTURE = {
    "case_condition": "Good",
    "screen_condition": "Good",
    "missing_items": "N/A",
    "keyboard_condition": "Good",
    "touchpad_condition": "Good",
    "usb_ports": "Good",
    "grade": "A"
}
