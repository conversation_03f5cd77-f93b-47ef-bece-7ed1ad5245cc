import tkinter as tk
from typing import Any, Callable, Dict, Optional
from agent.tests.display_test import run_lcd_test_gui
from agent.tests.keyboard_test import run_keyboard_test
from agent.tests.pointing_device_test import run_pointing_device_test
import platform
from agent.tests.visual_cpu_test import visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.web_visual_ram_test import run_web_visual_ram_test
from agent.tests.web_display_test import run_web_lcd_test_gui
from agent.tests.touch_screen_test import run_touch_screen_test
from agent.tests.profiles import Profile, calculate_ram_test_size
from agent.core.result_manager import ResultManager
from agent.core import nexus_credits
from agent.hardware import system_info
import psutil
TYPE_A_TEST_PATHS_QUALIFIED = ['agent.tests.battery_test.run_battery_test', 'agent.tests.battery_test.run_battery_discharge_test', 'agent.tests.battery_test.run_battery_charge_test', 'agent.tests.battery_test.run_battery_full_assessment']
try:
    from agent.tests.cpu_test import run_basic_cpu_test, run_cpu_stress_test
except ImportError:
    from agent.tests.visual_cpu_test import visual_cpu_test as run_basic_cpu_test
    run_cpu_stress_test = None
try:
    from agent.tests.ram_test import run_ram_test, run_advanced_ram_test
except ImportError:
    from agent.tests.visual_ram_test import run_visual_ram_test as run_ram_test
    run_advanced_ram_test = None
if platform.system() == 'Linux':
    from agent.tests.drive_wipe_test import run_secure_wipe_test
else:
    run_secure_wipe_test = None
run_battery_test = run_battery_charge_test = run_battery_discharge_test = run_battery_full_assessment = None

class TestOrchestrator:

    def __init__(self, log_callback: Callable[[str, str], None], result_manager_instance: ResultManager, main_app_ref: tk.Tk, get_current_profile_callback: Callable[[], Optional[Profile]], get_asset_number_callback: Callable[[], str], get_operator_id_callback: Callable[[], str]):
        self.log_callback = log_callback
        self.result_manager_instance = result_manager_instance
        self.main_app_ref = main_app_ref
        self.get_current_profile_callback = get_current_profile_callback
        self.get_asset_number_callback = get_asset_number_callback
        self.get_operator_id_callback = get_operator_id_callback
        self.end_screen_summary_data: Dict[str, Any] = {}

    def _get_status_and_notes_from_result(self, result_data: Any) -> tuple[str, str]:
        """Helper function to extract status and notes from various result formats."""
        status = 'unknown'
        notes = ''
        if isinstance(result_data, dict):
            if 'test_details' in result_data:
                raw_status = result_data.get('test_details', {}).get('status', 'unknown')
                status = str(raw_status).lower() if raw_status is not None else 'unknown'
                notes = result_data.get('test_details', {}).get('notes', '')
            elif 'status' in result_data:
                raw_status = result_data.get('status', 'unknown')
                status = str(raw_status).lower() if raw_status is not None else 'unknown'
                raw_notes = result_data.get('notes', '')
                if isinstance(raw_notes, list):
                    notes = '; '.join(raw_notes)
                else:
                    notes = str(raw_notes)
            else:
                notes = str(result_data)
                if len(notes) > 200:
                    notes = notes[:200] + '...'
        elif isinstance(result_data, str):
            status = result_data.lower()
        else:
            status = 'error'
            notes = f'Unexpected result format: {type(result_data)}'
        return (status, notes)

    def _check_memory_availability(self, requested_mb: int) -> Dict[str, Any]:
        """
        Check memory availability and determine safe test size.
        
        Args:
            requested_mb: Requested memory size in MB
            
        Returns:
            Dictionary containing memory check results and safe size
        """
        try:
            mem_info = psutil.virtual_memory()
            available_mb = mem_info.available / (1024 * 1024)
            total_mb = mem_info.total / (1024 * 1024)
            used_mb = mem_info.used / (1024 * 1024)
            
            # Calculate safe limits
            conservative_limit_mb = int(available_mb * 0.25)  # 25% of available
            aggressive_limit_mb = int(available_mb * 0.5)     # 50% of available
            absolute_minimum_mb = 512                         # Absolute minimum for any test
            
            # Determine if adjustment is needed
            needs_adjustment = requested_mb > conservative_limit_mb
            is_dangerous = requested_mb > aggressive_limit_mb
            
            # Calculate safe size
            if is_dangerous:
                safe_size_mb = conservative_limit_mb
                adjustment_reason = "exceeds safe memory limit"
            elif needs_adjustment:
                safe_size_mb = min(requested_mb, aggressive_limit_mb)
                adjustment_reason = "adjusted to conservative limit"
            else:
                safe_size_mb = max(requested_mb, absolute_minimum_mb)
                adjustment_reason = "no adjustment needed"
            
            return {
                "requested_mb": requested_mb,
                "safe_size_mb": safe_size_mb,
                "available_mb": available_mb,
                "total_mb": total_mb,
                "used_mb": used_mb,
                "needs_adjustment": needs_adjustment,
                "is_dangerous": is_dangerous,
                "adjustment_reason": adjustment_reason,
                "memory_pressure": used_mb / total_mb,  # Percentage of total memory used
                "conservative_limit_mb": conservative_limit_mb,
                "aggressive_limit_mb": aggressive_limit_mb
            }
            
        except Exception as e:
            self.log_callback(f"Error checking memory availability: {str(e)}", 'error')
            return {
                "requested_mb": requested_mb,
                "safe_size_mb": 1024,  # 1GB fallback
                "available_mb": 0,
                "total_mb": 0,
                "used_mb": 0,
                "needs_adjustment": True,
                "is_dangerous": True,
                "adjustment_reason": f"memory check failed: {str(e)}",
                "memory_pressure": 1.0,  # Assume high pressure on error
                "conservative_limit_mb": 1024,
                "aggressive_limit_mb": 1024,
                "error": str(e)
            }

    def _log_ram_test_summary(self, profile: Profile) -> None:
        """
        Log a summary of all RAM test configurations used in this test session.
        
        Args:
            profile: The profile used for testing
        """
        try:
            if not profile:
                return
                
            # Define RAM test paths
            ram_test_paths = [
                'agent.tests.visual_ram_test.run_visual_ram_test',
                'agent.tests.web_visual_ram_test.run_web_visual_ram_test',
                'agent.tests.ram_test.run_ram_test',
                'agent.tests.ram_test.run_advanced_ram_test'
            ]
            
            # Find RAM tests that were configured or executed
            ram_tests_in_profile = [test for test in profile.tests if test in ram_test_paths]
            
            if not ram_tests_in_profile:
                return
            
            self.log_callback("=== RAM Test Configuration Summary ===", 'info')
            self.log_callback(f"Profile: {profile.name} ({profile.device_type})", 'info')
            
            # Get system memory info for context
            try:
                mem_info = psutil.virtual_memory()
                total_gb = mem_info.total / (1024 * 1024 * 1024)
                available_gb = mem_info.available / (1024 * 1024 * 1024)
                self.log_callback(f"System Memory: {total_gb:.1f} GB total, {available_gb:.1f} GB available", 'info')
            except:
                self.log_callback("System Memory: Unable to determine", 'info')
            
            # Log configuration for each RAM test
            for test_path in ram_tests_in_profile:
                test_name = test_path.split('.')[-1].replace('run_', '').replace('_test', ' Test').title()
                config = profile.get_ram_test_config(test_path)
                
                config_source = "profile" if test_path in profile.test_args else "default"
                mode = config.get('test_size_mode', 'percentage')
                value = config.get('test_size_value', 25)
                duration = config.get('duration_seconds', 30)
                
                if mode == 'percentage':
                    config_desc = f"{value}% of available memory"
                else:
                    config_desc = f"{value} MB absolute"
                
                self.log_callback(
                    f"  {test_name}: {config_desc}, {duration}s duration (source: {config_source})", 
                    'info'
                )
            
            self.log_callback("=== End RAM Test Summary ===", 'info')
            
        except Exception as e:
            self.log_callback(f"Error logging RAM test summary: {str(e)}", 'error')

    def _log_ram_test_audit_trail(self, test_name: str, ram_params: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        Log comprehensive audit trail for RAM test execution.
        
        Args:
            test_name: Name of the test
            ram_params: Resolved RAM test parameters
            result: Test execution result
        """
        try:
            # Extract configuration details
            config_info = ram_params.get('original_config', {})
            memory_check = ram_params.get('memory_check', {})
            config_source = ram_params.get('config_source', 'unknown')
            
            # Log configuration audit trail
            audit_info = [
                f"=== RAM Test Audit Trail: {test_name} ===",
                f"Configuration Source: {config_source}",
                f"Original Config Mode: {config_info.get('test_size_mode', 'unknown')}",
                f"Original Config Value: {config_info.get('test_size_value', 'unknown')}",
                f"Configured Duration: {config_info.get('duration_seconds', 'unknown')}s",
                f"Actual Test Size: {ram_params.get('test_size_mb', 'unknown')} MB",
                f"Actual Duration: {ram_params.get('duration_seconds', 'unknown')}s"
            ]
            
            # Add memory status information
            if memory_check:
                audit_info.extend([
                    f"Available Memory: {memory_check.get('available_mb', 0):.0f} MB",
                    f"Total Memory: {memory_check.get('total_mb', 0):.0f} MB",
                    f"Memory Pressure: {memory_check.get('memory_pressure', 0):.1%}",
                    f"Safety Adjusted: {memory_check.get('needs_adjustment', False)}",
                    f"Adjustment Reason: {memory_check.get('adjustment_reason', 'none')}"
                ])
            
            # Add calculation metadata if available
            calc_metadata = ram_params.get('calculation_metadata', {})
            if calc_metadata:
                if calc_metadata.get('calculated_from_percentage'):
                    audit_info.append(f"Percentage Calculation: {calc_metadata.get('requested_percentage', 0)}% of {calc_metadata.get('available_memory_mb', 0):.0f} MB")
                if calc_metadata.get('safety_adjusted'):
                    audit_info.append(f"Profile Safety Adjustment: Yes")
                if 'error' in calc_metadata:
                    audit_info.append(f"Calculation Error: {calc_metadata['error']}")
            
            # Add test result information
            if isinstance(result, dict):
                if 'test_details' in result:
                    test_details = result['test_details']
                    audit_info.extend([
                        f"Test Status: {test_details.get('status', 'unknown')}",
                        f"Test Notes: {test_details.get('notes', 'none')}"
                    ])
                    if 'error_type' in test_details:
                        audit_info.append(f"Error Type: {test_details['error_type']}")
            
            audit_info.append("=== End Audit Trail ===")
            
            # Log each line of the audit trail
            for line in audit_info:
                self.log_callback(line, 'info')
                
        except Exception as e:
            self.log_callback(f"Error logging RAM test audit trail: {str(e)}", 'error')

    def _safe_ram_test_execution(self, test_func: Callable, test_name: str, **kwargs) -> Dict[str, Any]:
        """
        Safely execute RAM test with comprehensive error handling and memory safety checks.
        
        Args:
            test_func: The test function to execute
            test_name: Name of the test for logging
            **kwargs: Test function arguments
            
        Returns:
            Test result dictionary with error handling
        """
        try:
            # Pre-execution memory check
            requested_mb = kwargs.get('test_size_mb', 1024)
            
            # Final memory availability check before execution
            pre_exec_check = self._check_memory_availability(requested_mb)
            
            if pre_exec_check["is_dangerous"]:
                # Adjust to safe size and log warning
                safe_mb = pre_exec_check["safe_size_mb"]
                kwargs['test_size_mb'] = safe_mb
                self.log_callback(
                    f"Pre-execution safety check: Adjusted {test_name} from {requested_mb} MB to {safe_mb} MB "
                    f"(available: {pre_exec_check['available_mb']:.0f} MB)", 
                    'warning'
                )
            
            # Log test execution parameters
            self.log_callback(
                f"Executing {test_name} with {kwargs.get('test_size_mb', 'unknown')} MB, "
                f"{kwargs.get('duration_seconds', 'unknown')}s duration", 
                'info'
            )
            
            # Execute the test with error handling
            try:
                result = test_func(**kwargs)
                
                # Validate result format
                if result is None:
                    return {
                        "test_details": {
                            "status": "FAIL",
                            "notes": f"{test_name} returned None result"
                        }
                    }
                
                # Ensure result has proper structure
                if isinstance(result, dict):
                    if 'test_details' not in result and 'status' not in result:
                        # Wrap raw result in proper structure
                        return {
                            "test_details": {
                                "status": "PASS",
                                "notes": f"{test_name} completed",
                                "raw_result": result
                            }
                        }
                    return result
                else:
                    # Handle non-dict results
                    return {
                        "test_details": {
                            "status": "PASS" if str(result).lower() in ['pass', 'success', 'true'] else "FAIL",
                            "notes": f"{test_name} result: {str(result)}"
                        }
                    }
                    
            except MemoryError as me:
                error_msg = f"Insufficient memory for {test_name}: {str(me)}"
                self.log_callback(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": "MemoryError",
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }
                
            except OSError as oe:
                # Handle OS-level memory allocation errors
                error_msg = f"OS memory allocation error in {test_name}: {str(oe)}"
                self.log_callback(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": "OSError",
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }
                
            except Exception as e:
                # Handle any other test execution errors
                error_msg = f"Error executing {test_name}: {str(e)}"
                self.log_callback(error_msg, 'error')
                return {
                    "test_details": {
                        "status": "FAIL",
                        "notes": error_msg,
                        "error_type": type(e).__name__,
                        "requested_mb": kwargs.get('test_size_mb', 'unknown')
                    }
                }
                
        except Exception as e:
            # Handle errors in the safety wrapper itself
            error_msg = f"Critical error in RAM test safety wrapper for {test_name}: {str(e)}"
            self.log_callback(error_msg, 'error')
            return {
                "test_details": {
                    "status": "FAIL",
                    "notes": error_msg,
                    "error_type": "SafetyWrapperError"
                }
            }

    def _resolve_ram_test_parameters(self, test_path: str, profile: Profile) -> Dict[str, Any]:
        """
        Resolve RAM test parameters from profile configuration with comprehensive safety checks.
        
        Args:
            test_path: The test path to resolve parameters for
            profile: The current profile containing test configuration
            
        Returns:
            Dictionary containing resolved RAM test parameters
        """
        try:
            # Get RAM test configuration from profile
            config = profile.get_ram_test_config(test_path)
            
            # Calculate actual test size using the utility function
            calculation_result = calculate_ram_test_size(config)
            
            # Perform additional memory availability check
            memory_check = self._check_memory_availability(calculation_result["test_size_mb"])
            
            # Use the safer of the two calculations
            final_size_mb = memory_check["safe_size_mb"]
            
            # Extract resolved parameters
            resolved_params = {
                "test_size_mb": final_size_mb,
                "duration_seconds": config.get("duration_seconds", 30),
                "original_config": calculation_result["original_config"],
                "calculation_metadata": calculation_result,
                "memory_check": memory_check,
                "config_source": "profile" if test_path in profile.test_args else "default"
            }
            
            # Log parameter resolution details
            if calculation_result.get("safety_adjusted", False) or memory_check["needs_adjustment"]:
                if calculation_result.get("calculated_from_percentage", False):
                    original_calc_mb = int(calculation_result.get('available_memory_mb', 0) * calculation_result.get('requested_percentage', 0) / 100)
                    self.log_callback(
                        f"RAM test size adjusted for safety: requested {calculation_result.get('requested_percentage', 0)}% "
                        f"({original_calc_mb} MB), using {final_size_mb} MB instead "
                        f"({memory_check['adjustment_reason']})", 
                        'warning'
                    )
                else:
                    self.log_callback(
                        f"RAM test size adjusted for safety: requested {calculation_result.get('requested_mb', 0)} MB, "
                        f"using {final_size_mb} MB instead ({memory_check['adjustment_reason']})", 
                        'warning'
                    )
            
            # Log memory pressure warning if high
            if memory_check["memory_pressure"] > 0.8:
                self.log_callback(
                    f"High memory pressure detected ({memory_check['memory_pressure']:.1%} used). "
                    f"RAM test size limited to {final_size_mb} MB for system stability", 
                    'warning'
                )
            
            return resolved_params
            
        except Exception as e:
            # Log error and return safe defaults
            self.log_callback(f"Error resolving RAM test parameters for {test_path}: {str(e)}", 'error')
            
            # Return safe default parameters with error handling
            try:
                mem_info = psutil.virtual_memory()
                safe_size_mb = max(512, int(mem_info.available / (1024 * 1024) * 0.1))  # 10% of available or 512MB minimum
            except:
                safe_size_mb = 512  # 512MB fallback
                
            return {
                "test_size_mb": safe_size_mb,
                "duration_seconds": 30,
                "original_config": {"test_size_mode": "percentage", "test_size_value": 25, "duration_seconds": 30},
                "calculation_metadata": {"error": str(e), "safety_adjusted": True},
                "memory_check": {"error": str(e), "safe_size_mb": safe_size_mb},
                "config_source": "error_fallback"
            }

    def execute_tests(self, headless_mode=False):
        current_profile = self.get_current_profile_callback()
        if not current_profile or not current_profile.tests:
            self.log_callback('No profile loaded or profile has no tests.', 'warning')
            self.end_screen_summary_data = {'overall_status': 'ERROR', 'test_results': [], 'drive_wipe_results': [], 'errors': ['No profile loaded or profile has no tests.']}
            return
        self.log_callback(f'Starting tests for profile: {current_profile.name}...', 'info')
        self.end_screen_summary_data = {'overall_status': 'PASS', 'test_results': [], 'drive_wipe_results': [], 'errors': []}

        # Start Nexus credit operation (diagnostics). Non-fatal if not configured.
        nexus_operation_id = None
        try:
            serial = system_info.get_machine_serial_number() or 'UNKNOWN'
            model = system_info.get_model() or system_info.get_system_product_name() or 'UNKNOWN'
            nexus_operation_id = nexus_credits.start_operation(
                operation_type=nexus_credits.OperationType.DIAGNOSTICS_ONLY,
                device_serial=serial,
                device_model=model,
                metadata={
                    'profile_name': current_profile.name,
                    'headless_mode': bool(headless_mode),
                },
            )
            if nexus_operation_id:
                self.log_callback(f"Nexus credits: started diagnostics operation id={nexus_operation_id}", 'info')
            else:
                self.log_callback("Nexus credits: start skipped or failed (not configured or insufficient credits)", 'debug')
        except Exception as e:
            self.log_callback(f"Nexus credits: error starting operation: {e}", 'warning')
        tests_to_run = current_profile.tests
        # Choose appropriate visual tests based on mode
        visual_ram_test_func = run_web_visual_ram_test if headless_mode else run_visual_ram_test
        visual_ram_test_kwargs = {'log_callback': self.log_callback} if headless_mode else {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}

        lcd_test_func = run_web_lcd_test_gui if headless_mode else run_lcd_test_gui
        lcd_test_kwargs = {'log_callback': self.log_callback} if headless_mode else {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}

        # Build test function map - exclude keyboard test in headless mode since frontend handles it
        test_function_map = {
            'agent.tests.cpu_test.run_basic_cpu_test': (run_basic_cpu_test, {'log_callback': self.log_callback}),
            'agent.tests.ram_test.run_ram_test': (run_ram_test, {'log_callback': self.log_callback}),
            'agent.tests.ram_test.run_advanced_ram_test': (run_advanced_ram_test, {'log_callback': self.log_callback}),
            'agent.tests.display_test.run_lcd_test_gui': (lcd_test_func, lcd_test_kwargs),
            'agent.tests.pointing_device_test.run_pointing_device_test': (run_pointing_device_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.visual_cpu_test.visual_cpu_test': (visual_cpu_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.visual_ram_test.run_visual_ram_test': (visual_ram_test_func, visual_ram_test_kwargs),
            'agent.tests.web_visual_ram_test.run_web_visual_ram_test': (visual_ram_test_func, visual_ram_test_kwargs),
            'agent.tests.drive_wipe_test.run_secure_wipe_test': (run_secure_wipe_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            'agent.tests.touch_screen_test.run_touch_screen_test': (run_touch_screen_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback}),
            **({'agent.tests.cpu_test.run_cpu_stress_test': (run_cpu_stress_test, {'log_callback': self.log_callback})} if run_cpu_stress_test else {})
        }
        
        # Add keyboard test only in non-headless mode (frontend handles it in headless mode with enhanced UI)
        if not headless_mode:
            test_function_map['agent.tests.keyboard_test.run_keyboard_test'] = (run_keyboard_test, {'parent_window': self.main_app_ref, 'log_callback': self.log_callback})
        
        # Define RAM test paths that need parameter resolution
        ram_test_paths = [
            'agent.tests.visual_ram_test.run_visual_ram_test',
            'agent.tests.web_visual_ram_test.run_web_visual_ram_test',
            'agent.tests.ram_test.run_ram_test',
            'agent.tests.ram_test.run_advanced_ram_test'
        ]
        for test_path_in_profile in tests_to_run:
            test_name_simple = test_path_in_profile.split('.')[-1].replace('run_', '').replace('_test', ' Test').title()
            if headless_mode and test_path_in_profile not in TYPE_A_TEST_PATHS_QUALIFIED:
                skip_msg = 'Skipped in headless mode'
                self.log_callback(f'Skipping GUI test {test_name_simple} in headless mode.', 'warning')
                self.result_manager_instance.add_result(
                    self.get_asset_number_callback(), 
                    self.get_operator_id_callback(), 
                    test_name_simple, 
                    {'test_details': {'status': 'skipped', 'notes': skip_msg}},
                    profile_name=(current_profile.name if current_profile else None)
                )
                self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED', 'notes': skip_msg})
                continue
            if test_path_in_profile not in test_function_map:
                if test_path_in_profile.startswith('agent.tests.battery_test'):
                    skip_msg = f"Deprecated battery test '{test_name_simple}' skipped; battery info collected automatically."
                    self.log_callback(skip_msg, 'info')
                    self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'skipped', 'notes': skip_msg}}, profile_name=(current_profile.name if current_profile else None))
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED'})
                    continue
                error_msg = f"Test function for '{test_path_in_profile}' not found in orchestrator map."
                self.log_callback(error_msg, 'error')
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'fail', 'notes': error_msg}}, profile_name=(current_profile.name if current_profile else None))
                self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'FAIL'})
                self.end_screen_summary_data['errors'].append(error_msg)
                self.end_screen_summary_data['overall_status'] = 'FAIL'
                continue
            test_func, test_kwargs = test_function_map[test_path_in_profile]
            if test_func is None:
                skip_msg = f"Test '{test_name_simple}' ({test_path_in_profile}) is not available on this platform ({platform.system()}) and will be skipped."
                self.log_callback(skip_msg, 'warning')
                self.result_manager_instance.add_result(
                    self.get_asset_number_callback(),
                    self.get_operator_id_callback(),
                    test_name_simple,
                    {'test_details': {'status': 'skipped', 'notes': skip_msg}},
                    profile_name=(current_profile.name if current_profile else None)
                )
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Platform Skip', 'status': 'SKIPPED', 'details': skip_msg})
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'SKIPPED', 'notes': skip_msg})
                continue
            
            # Resolve RAM test parameters if this is a RAM test
            ram_params = None
            if test_path_in_profile in ram_test_paths:
                try:
                    ram_params = self._resolve_ram_test_parameters(test_path_in_profile, current_profile)
                    
                    # Update test kwargs with resolved parameters
                    test_kwargs = test_kwargs.copy()  # Don't modify the original
                    test_kwargs.update({
                        'test_size_mb': ram_params['test_size_mb'],
                        'duration_seconds': ram_params['duration_seconds']
                    })
                    
                    # Log the configuration being used with detailed information
                    config_info = ram_params['original_config']
                    config_source = ram_params.get('config_source', 'unknown')
                    
                    if config_info.get('test_size_mode') == 'percentage':
                        config_desc = f"{config_info.get('test_size_value', 25)}% ({ram_params['test_size_mb']} MB)"
                    else:
                        config_desc = f"{ram_params['test_size_mb']} MB"
                    
                    # Log configuration source and parameters
                    self.log_callback(
                        f"RAM test configuration ({config_source}): {config_desc}, "
                        f"{ram_params['duration_seconds']}s duration", 
                        'info'
                    )
                    
                    # Log memory status for audit trail
                    if 'memory_check' in ram_params:
                        mem_check = ram_params['memory_check']
                        self.log_callback(
                            f"Memory status: {mem_check.get('available_mb', 0):.0f} MB available, "
                            f"{mem_check.get('memory_pressure', 0):.1%} memory pressure", 
                            'debug'
                        )
                    
                except Exception as e:
                    self.log_callback(f"Error resolving RAM test parameters for {test_name_simple}: {str(e)}", 'error')
                    # Continue with default parameters already in test_kwargs
            
            self.log_callback(f'Running {test_name_simple}...', 'info')
            try:
                # Use safe execution for RAM tests, regular execution for others
                if test_path_in_profile in ram_test_paths:
                    result = self._safe_ram_test_execution(test_func, test_name_simple, **test_kwargs)
                    
                    # Add comprehensive RAM test metadata to result for audit trail
                    if isinstance(result, dict) and ram_params:
                        ram_audit_data = {
                            'configured_mb': ram_params.get('test_size_mb'),
                            'duration_seconds': ram_params.get('duration_seconds'),
                            'config_source': ram_params.get('config_source'),
                            'original_config': ram_params.get('original_config'),
                            'safety_adjusted': ram_params.get('memory_check', {}).get('needs_adjustment', False),
                            'memory_status': {
                                'available_mb': ram_params.get('memory_check', {}).get('available_mb', 0),
                                'total_mb': ram_params.get('memory_check', {}).get('total_mb', 0),
                                'memory_pressure': ram_params.get('memory_check', {}).get('memory_pressure', 0),
                                'adjustment_reason': ram_params.get('memory_check', {}).get('adjustment_reason', 'none')
                            },
                            'calculation_metadata': ram_params.get('calculation_metadata', {}),
                            'test_timestamp': __import__('time').time()
                        }
                        
                        if 'test_details' in result:
                            result['test_details']['ram_config'] = ram_audit_data
                        else:
                            result['ram_config'] = ram_audit_data
                    
                    # Log comprehensive audit trail
                    if ram_params:
                        self._log_ram_test_audit_trail(test_name_simple, ram_params, result)
                        
                else:
                    result = test_func(**test_kwargs)
                # Add result with enhanced metadata for RAM tests
                enhanced_result = result
                if test_path_in_profile in ram_test_paths and ram_params:
                    # Ensure the result includes comprehensive RAM test information
                    if isinstance(enhanced_result, dict):
                        # Add top-level metadata for easier access
                        enhanced_result['test_type'] = 'ram_test'
                        enhanced_result['test_path'] = test_path_in_profile
                        enhanced_result['profile_name'] = current_profile.name if current_profile else 'unknown'
                        
                        # Log final configuration summary
                        config_summary = (
                            f"Final RAM test configuration recorded: "
                            f"{ram_params.get('test_size_mb', 'unknown')} MB, "
                            f"{ram_params.get('duration_seconds', 'unknown')}s, "
                            f"source: {ram_params.get('config_source', 'unknown')}"
                        )
                        self.log_callback(config_summary, 'info')
                
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, enhanced_result, profile_name=(current_profile.name if current_profile else None))
                status, notes = self._get_status_and_notes_from_result(result)
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    if isinstance(result, list):
                        for drive_res in result:
                            drive_path = drive_res.get('drive', 'Unknown Drive')
                            drive_status = str(drive_res.get('status', 'unknown')).lower()
                            drive_details = drive_res.get('details', '')
                            self.end_screen_summary_data['drive_wipe_results'].append({'drive': drive_path, 'status': drive_status.upper(), 'details': drive_details})
                            if drive_status not in ['success', 'pass']:
                                self.end_screen_summary_data['overall_status'] = 'FAIL'
                                if drive_details:
                                    self.end_screen_summary_data['errors'].append(f'Wipe Error ({drive_path}): {drive_details}')
                    elif isinstance(result, dict) and 'status' in result:
                        wipe_status_overall = str(result.get('status', 'unknown')).lower()
                        wipe_details_overall = result.get('notes', result.get('details', ''))
                        self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Overall Wipe Status', 'status': wipe_status_overall.upper(), 'details': wipe_details_overall})
                        if wipe_status_overall not in ['success', 'pass']:
                            self.end_screen_summary_data['overall_status'] = 'FAIL'
                            if wipe_details_overall:
                                self.end_screen_summary_data['errors'].append(f'{test_name_simple}: {wipe_details_overall}')
                    else:
                        self.log_callback(f'{test_name_simple} result in unexpected format: {result}', 'warning')
                        self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Unknown', 'status': 'FAIL', 'details': 'Result in unexpected format'})
                        self.end_screen_summary_data['overall_status'] = 'FAIL'
                        self.end_screen_summary_data['errors'].append(f'{test_name_simple}: Result in unexpected format.')
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': status.upper()})
                    if status not in ['pass', 'success']:
                        self.end_screen_summary_data['overall_status'] = 'FAIL'
                        if notes:
                            self.end_screen_summary_data['errors'].append(f'{test_name_simple} ({status.upper()}): {notes}')
            except Exception as e:
                error_msg = f'Error running {test_name_simple}: {str(e)}'
                self.log_callback(error_msg, 'error')
                self.result_manager_instance.add_result(self.get_asset_number_callback(), self.get_operator_id_callback(), test_name_simple, {'test_details': {'status': 'fail', 'notes': error_msg}})
                if test_path_in_profile == 'agent.tests.drive_wipe_test.run_secure_wipe_test':
                    self.end_screen_summary_data['drive_wipe_results'].append({'drive': 'Execution Error', 'status': 'FAIL', 'details': error_msg})
                else:
                    self.end_screen_summary_data['test_results'].append({'name': test_name_simple, 'status': 'FAIL'})
                self.end_screen_summary_data['errors'].append(error_msg)
                self.end_screen_summary_data['overall_status'] = 'FAIL'
        # Log RAM test configuration summary
        self._log_ram_test_summary(current_profile)
        
        final_log_level = 'success' if self.end_screen_summary_data['overall_status'] == 'PASS' else 'error'
        self.log_callback(f"All tests completed. Overall Status: {self.end_screen_summary_data['overall_status']}", final_log_level)
        if self.end_screen_summary_data['errors']:
            self.log_callback(f"Encountered {len(self.end_screen_summary_data['errors'])} error(s)/issue(s) during testing:", 'warning')
            for err_entry in self.end_screen_summary_data['errors']:
                self.log_callback(f' - {err_entry}', 'warning')
        asset_number = self.get_asset_number_callback()
        operator_id = self.get_operator_id_callback()
        if not asset_number:
            self.log_callback('Asset number not available, skipping results consolidation.', 'warning')
            asset_number = 'unknown_asset'
        if not operator_id:
            self.log_callback("Operator ID not available, will be set to 'unknown' in consolidation.", 'info')
            operator_id = 'unknown_operator'
        if asset_number != 'unknown_asset':
            try:
                consolidated_file_path = self.result_manager_instance.consolidate_results_for_asset(asset_number, operator_id)
                if consolidated_file_path:
                    self.log_callback(f'Successfully consolidated results for asset {asset_number} to {consolidated_file_path}', 'success')
                else:
                    self.log_callback(f'Failed to consolidate results for asset {asset_number}. See previous logs for details.', 'warning')
            except Exception as e:
                self.log_callback(f'An unexpected error occurred during results consolidation for asset {asset_number}: {str(e)}', 'error')
        else:
            self.log_callback(f"Skipping results consolidation as asset number is '{asset_number}'.", 'info')

        # Complete or refund Nexus credit operation
        if nexus_operation_id:
            try:
                success = self.end_screen_summary_data.get('overall_status') == 'PASS'
                meta = {
                    'tests_total': len(self.end_screen_summary_data.get('test_results', [])),
                    'drive_wipe_results_total': len(self.end_screen_summary_data.get('drive_wipe_results', [])),
                }
                ok = nexus_credits.complete_operation(
                    operation_id=nexus_operation_id,
                    success=success,
                    metadata=meta,
                    error_message='; '.join(self.end_screen_summary_data.get('errors', [])) if not success else None,
                )
                if ok:
                    self.log_callback("Nexus credits: operation completion reported", 'info')
                else:
                    self.log_callback("Nexus credits: operation completion failed", 'warning')
            except Exception as e:
                self.log_callback(f"Nexus credits: error completing operation: {e}", 'warning')

    def get_summary_data(self) -> Dict[str, Any]:
        return self.end_screen_summary_data
if __name__ == '__main__':

    class MockResultManager:
        def add_result(self, asset_number, operator_id, test_name, result, mac=None, profile_name=None):
            status = result.get('status', result.get('test_details', {}).get('status')) if isinstance(result, dict) else str(result)
            print(f"MOCK_RESULT_ADD asset={asset_number} operator={operator_id} test={test_name} status={status} mac={mac} profile={profile_name}")
        
        def consolidate_results_for_asset(self, asset_number, operator_id):
            print(f"Consolidating for {asset_number}")
            return "mock_path.json"

    class MockApp(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title('Mock App')
            self.withdraw()

    mock_app = MockApp()
    mock_logger = lambda msg, lvl: print(f'LOG [{lvl.upper()}]: {msg}')
    mock_res_manager = MockResultManager()
    sample_profile = Profile(name='Full Test', device_type='laptop', description='Full suite')
    sample_profile.tests = ['agent.tests.cpu_test.run_basic_cpu_test', 'agent.tests.ram_test.run_ram_test', 'agent.tests.drive_wipe_test.run_secure_wipe_test', 'non.existent.test.run_fake_test']
    
    get_profile_cb = lambda: sample_profile
    get_asset_cb = lambda: "MOCK_ASSET_123"
    get_operator_cb = lambda: "MOCK_OPERATOR_456"

    orchestrator = TestOrchestrator(
        log_callback=mock_logger,
        result_manager_instance=mock_res_manager,
        main_app_ref=mock_app,
        get_current_profile_callback=get_profile_cb,
        get_asset_number_callback=get_asset_cb,
        get_operator_id_callback=get_operator_cb
    )

    print('--- Executing Tests ---')
    orchestrator.execute_tests()
    summary = orchestrator.get_summary_data()
    print('\n--- Test Summary ---')
    import json
    print(json.dumps(summary, indent=2))
    mock_app.destroy()