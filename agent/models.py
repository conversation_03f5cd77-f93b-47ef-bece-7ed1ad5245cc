"""Data models for agent payloads."""
from __future__ import annotations

from dataclasses import asdict, dataclass, field
from typing import Any, Dict


@dataclass(slots=True)
class AgentPayload:
    asset_serial: str
    asset_number: str
    operator_id: str
    test_name: str
    status: str
    data: Dict[str, Any]
    started_at: str
    finished_at: str

    def as_dict(self) -> Dict[str, Any]:
        return asdict(self)
