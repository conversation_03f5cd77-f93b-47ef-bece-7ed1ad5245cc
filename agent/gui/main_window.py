"""
Crucible Nexus Main GUI Window

This module provides the main GUI window for the Crucible Nexus diagnostics tool.
"""
from agent.gui.results_display_window import ResultsDisplayWindow
from agent.gui.end_screen_window import EndScreenWindow
from agent.core.test_orchestrator import TestOrchestrator
from agent.core.result_manager import ResultManager
from agent.gui.frames.test_control_frame import TestControlFrame
from agent.gui.frames.asset_input_frame import AssetInputFrame
from agent.gui.frames.log_panel import LogPanel
import asyncio
import datetime
import json
from logging import debug
import os
import time
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
from typing import Dict, Any, List
from agent.hardware.system_info import get_system_info, get_screen_resolution
try:
    from agent.tests.cpu_test import run_basic_cpu_test
except ImportError:
    from agent.tests.visual_cpu_test import visual_cpu_test as run_basic_cpu_test
try:
    from agent.tests.ram_test import run_ram_test
except ImportError:
    from agent.tests.visual_ram_test import run_visual_ram_test as run_ram_test
from agent.tests.display_test import run_lcd_test_gui
from agent.tests.keyboard_test import run_keyboard_test
from agent.tests.pointing_device_test import run_pointing_device_test
import platform
if platform.system() == 'Linux':
    from agent.tests.drive_wipe_test import run_secure_wipe_test
else:
    run_secure_wipe_test = None
from agent.tests.visual_cpu_test import visual_cpu_test
from agent.tests.visual_ram_test import run_visual_ram_test
from agent.tests.profiles import Profile, get_all_profiles, load_profile
from agent.tests.test_framework import run_test_by_name
from agent.gui.profile_editor import ProfileEditor
from agent.gui.device_condition import DeviceConditionWindow, save_device_conditions, load_device_conditions
from agent.utils.network import send_result
from agent.gui.theme import COLORS, FONTS, SIZES, get_font_size_for_resolution, get_padding_for_resolution, get_log_height_for_resolution
import os
import json
CONFIG_PATH = os.path.join(os.path.dirname(__file__), 'resolution_config.json')
try:
    RESOLUTION_CONFIG = json.load(open(CONFIG_PATH))
except Exception:
    RESOLUTION_CONFIG = {'resolutions': {}}

class NexusApp(tk.Tk):

    def __init__(self):
        super().__init__()
        self.title('')
        self.geometry('1024x768')
        self.minsize(1024, 700)
        self.attributes('-fullscreen', True)
        self.system_info = None
        self.serial_number = 'Unknown'
        self.base_font_size = COLORS['small_font_size']
        self.current_resolution = None
        self.responsive_padding = None
        self.responsive_log_height = None
        try:
            res_str = get_screen_resolution(self)
            if res_str and 'x' in res_str:
                width, height = map(int, res_str.split('x'))
                self.current_resolution = (width, height)
                self.base_font_size = get_font_size_for_resolution(width, height)
                self.responsive_padding = get_padding_for_resolution(width, height)
                self.responsive_log_height = get_log_height_for_resolution(width, height)
                print(f'Detected resolution: {width}x{height}, using font size: {self.base_font_size}')
        except Exception as e:
            print(f'Error determining screen resolution for font sizing: {e}')
            self.responsive_padding = {'large': SIZES['padding_medium'], 'medium': SIZES['padding_small'], 'small': SIZES['padding_tiny']}
            self.responsive_log_height = SIZES['log_window_height_small']
        self.current_profile = None
        self.result_manager = ResultManager()
        self.test_orchestrator = TestOrchestrator(log_callback=lambda m, l: print(f'Early Orchestrator Log: {m}'), result_manager_instance=self.result_manager, main_app_ref=self, get_current_profile_callback=lambda: self.current_profile, get_asset_number_callback=self.asset_input_frame.get_asset_number, get_operator_id_callback=self.asset_input_frame.get_operator_id)
        self.setup_ui_styles()
        self.setup_ui()
        self.result_manager.log_callback = self.log_panel.log
        self.test_orchestrator.log_callback = self.log_panel.log
        self.log_panel.update_status('Collecting system information...')
        self.system_info = get_system_info(self)
        self.serial_number = self.system_info.get('serial_number', 'Unknown')
        self.update_system_info_display()
        self.log_panel.update_status('Ready')
        self.auto_detect_and_apply_resolution()
        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            logo_path = os.path.join(base_dir, 'pictures', 'cruciblelogo.png')
            if os.path.exists(logo_path):
                self.logo_image = tk.PhotoImage(file=logo_path)
                original_height = self.logo_image.height()
                target_height = 100
                subsample_factor = max(1, int(original_height / target_height))
                self.logo_image = self.logo_image.subsample(subsample_factor)
                if hasattr(self, 'logo_label'):
                    self.logo_label.configure(image=self.logo_image)
                    self.log_panel.log('Logo loaded successfully.', 'info')
                else:
                    self.log_panel.log('logo_label not found during logo loading.', 'warning')
            else:
                self.log_panel.log(f'Logo image not found at: {logo_path}', 'warning')
        except Exception as e:
            self.log_panel.log(f'Error loading logo: {str(e)}', 'error')
        if hasattr(self, 'test_control_frame'):
            try:
                from agent.hardware.system_info import get_device_type
                device_type = get_device_type()
                self.log_panel.log(f'Detected device_type for auto-profile: {device_type}', 'debug')
                all_profiles = get_all_profiles()
                profile_names_only = [p.name for p in all_profiles]
                self.log_panel.log(f'Available profiles for auto-selection: {profile_names_only}', 'debug')
                preferred_profile_name = None
                if device_type == 'laptop' and 'Laptop' in profile_names_only:
                    preferred_profile_name = 'Laptop'
                elif device_type == 'desktop' and 'Desktop' in profile_names_only:
                    preferred_profile_name = 'Desktop'
                if preferred_profile_name:
                    self.log_panel.log(f'Attempting to auto-select profile: {preferred_profile_name}', 'info')
                    self.test_control_frame.set_profile_by_name_and_load(preferred_profile_name)
                elif all_profiles:
                    first_profile_name = all_profiles[0].name
                    self.log_panel.log(f'Auto-profile selection: fallback to first profile: {first_profile_name}', 'warning')
                    self.test_control_frame.set_profile_by_name_and_load(first_profile_name)
                else:
                    self.log_panel.log('No profiles available for auto-selection.', 'warning')
            except Exception as e:
                self.log_panel.log(f'Auto-profile selection failed: {e}', 'error')
        else:
            self.log_panel.log('test_control_frame not found during default profile loading.', 'warning')
        self.lift()
        self.focus_force()
        self.update_idletasks()
        self._check_run_conditions()
        self.after(200, self.asset_input_frame.focus_operator_entry)
        try:
            detected_res = get_screen_resolution(self)
            if detected_res:
                self.apply_resolution(detected_res, self.base_font_size)
                self.auto_detect_and_apply_resolution()
            else:
                self.update_all_component_fonts()
                self.update_component_padding()
        except Exception as e:
            print(f'Error applying initial resolution: {e}')
            self.update_all_component_fonts()
            self.update_component_padding()

    def setup_ui_styles(self):
        """Configure ttk styles for the application with isolation, accessible globally."""
        self.style = ttk.Style(self)
        self.style.theme_use('clam')
        self.style.configure('.', background=COLORS['bg_dark'], foreground=COLORS['text_light'], font=(FONTS['normal_font'], self.base_font_size))
        self.style.configure('TFrame', background=COLORS['bg_dark'])
        self.style.configure('TLabel', background=COLORS['bg_dark'], foreground=COLORS['text_light'], font=(FONTS['normal_font'], self.base_font_size))
        self.style.configure('TNotebook', background=COLORS['bg_dark'], borderwidth=0)
        self.style.configure('TNotebook.Tab', background=COLORS['bg_light'], foreground=COLORS['text_dark'], font=(FONTS['normal_font'], COLORS['small_font_size']), padding=[5, 2])
        self.style.map('TNotebook.Tab', background=[('selected', COLORS['primary']), ('active', COLORS['accent'])], foreground=[('selected', COLORS['text_light']), ('active', COLORS['text_light'])])
        self.style.configure('TButton', background=COLORS['button'], foreground=COLORS['button_text'], font=(FONTS['normal_font'], self.base_font_size), padding=5, relief=tk.FLAT, borderwidth=0)
        self.style.map('TButton', background=[('pressed', COLORS['button_active']), ('active', COLORS['button_hover']), ('disabled', COLORS['button_disabled'])], foreground=[('disabled', COLORS['button_text_disabled'])])
        self.style.configure('Header.TLabel', font=(FONTS['normal_font'], COLORS['large_font_size'], 'bold'), foreground=COLORS['primary'], background=COLORS['bg_dark'])
        self.style.configure('SectionTitle.TLabel', font=(FONTS['normal_font'], self.base_font_size + 2, 'bold'), foreground=COLORS['text_light'], background=COLORS['bg_dark'])
        self.style.configure('TListbox', background=COLORS['bg_light'], foreground=COLORS['text_light'], font=(FONTS['normal_font'], self.base_font_size), selectbackground=COLORS['primary'], selectforeground=COLORS['text_light'], relief=tk.FLAT, borderwidth=1)
        self.style.configure('TEntry', fieldbackground=COLORS['bg_light'], foreground=COLORS['text_light'], font=(FONTS['normal_font'], self.base_font_size), insertcolor=COLORS['text_light'], relief=tk.FLAT, borderwidth=1)
        self.style.map('TEntry', bordercolor=[('focus', COLORS['primary']), ('!focus', COLORS['bg_light'])], fieldbackground=[('disabled', COLORS['button_disabled'])])
        self.style.configure('Vertical.TScrollbar', background=COLORS['bg_light'], troughcolor=COLORS['bg_dark'], arrowcolor=COLORS['text_dark'], borderwidth=0)
        self.style.map('Vertical.TScrollbar', background=[('active', COLORS['accent'])])
        self.style.configure('TLabelframe', background=COLORS['bg_dark'], foreground=COLORS['accent'], font=(FONTS['normal_font'], self.base_font_size, 'bold'), borderwidth=1, relief='solid')
        self.style.configure('TLabelframe.Label', background=COLORS['bg_dark'], foreground=COLORS['primary'], font=(FONTS['normal_font'], self.base_font_size - 2, 'bold'))
        self.style.configure('TScrolledText', background=COLORS['bg_light'], foreground=COLORS['text_light'])
        self.style.configure('TCanvas', background=COLORS['bg_light'])
        print('Main window styles applied and accessible globally.')

    def setup_ui(self):
        """Set up the user interface components with a dark theme and large fonts."""
        style = ttk.Style()
        self.configure(bg=COLORS['bg_dark'])
        style.theme_use('clam')
        base_font_family = FONTS['normal_font']
        style.configure('TFrame', background=COLORS['bg_dark'])
        style.configure('TLabel', background=COLORS['bg_dark'], foreground=COLORS['text_light'], font=(base_font_family, self.base_font_size))
        style.configure('TLabelframe', background=COLORS['bg_dark'], foreground=COLORS['accent'], font=(base_font_family, self.base_font_size, 'bold'), borderwidth=1, relief='solid')
        style.configure('TLabelframe.Label', background=COLORS['bg_dark'], foreground=COLORS['primary'], font=(base_font_family, self.base_font_size - 2, 'bold'))
        style.configure('TButton', background=COLORS['button'], foreground=COLORS['button_text'], font=(base_font_family, self.base_font_size, 'bold'), borderwidth=1, relief='raised')
        style.map('TButton', background=[('active', COLORS['button_hover']), ('pressed', COLORS['button_active']), ('disabled', COLORS['button_disabled'])], foreground=[('disabled', COLORS['button_text_disabled'])], relief=[('pressed', 'sunken'), ('!pressed', 'raised')])
        style.configure('TCheckbutton', background=COLORS['bg_dark'], foreground=COLORS['text_light'], font=(base_font_family, self.base_font_size))
        style.map('TCheckbutton', indicatorbackground=[('selected', COLORS['primary']), ('!selected', COLORS['accent'])], indicatorforeground=[('selected', COLORS['text_light']), ('!selected', COLORS['text_light'])])
        style.configure('TEntry', fieldbackground=COLORS['bg_light'], foreground=COLORS['text_light'], font=(base_font_family, self.base_font_size), insertbackground=COLORS['text_light'])
        style.map('TEntry', bordercolor=[('focus', COLORS['primary']), ('!focus', COLORS['accent'])], borderwidth=[('focus', 2), ('!focus', 1)])
        style.configure('TCombobox', fieldbackground=COLORS['bg_light'], background=COLORS['accent'], foreground=COLORS['text_light'], arrowcolor=COLORS['text_light'], font=(base_font_family, self.base_font_size - 2), insertbackground=COLORS['text_light'], selectbackground=COLORS['bg_light'], selectforeground=COLORS['text_light'])
        style.map('TCombobox', bordercolor=[('focus', COLORS['primary']), ('!focus', COLORS['accent'])], borderwidth=[('focus', 2), ('!focus', 1)], fieldbackground=[('readonly', COLORS['bg_light'])], foreground=[('readonly', COLORS['text_light'])])
        self.option_add('*TCombobox*Listbox.background', COLORS['bg_light'])
        self.option_add('*TCombobox*Listbox.foreground', COLORS['text_light'])
        self.option_add('*TCombobox*Listbox.selectBackground', COLORS['primary'])
        self.option_add('*TCombobox*Listbox.selectForeground', COLORS['text_light'])
        self.option_add('*TCombobox*Listbox.font', (base_font_family, self.base_font_size - 4))
        style.configure('Horizontal.TProgressbar', background=COLORS['primary'], troughcolor=COLORS['bg_light'])
        style.configure('Vertical.TScrollbar', background=COLORS['button'], troughcolor=COLORS['bg_light'], arrowcolor=COLORS['text_light'], borderwidth=0, relief='flat')
        style.map('Vertical.TScrollbar', background=[('active', COLORS['button_hover'])])
        style.configure('Horizontal.TScrollbar', background=COLORS['button'], troughcolor=COLORS['bg_light'], arrowcolor=COLORS['text_light'], borderwidth=0, relief='flat')
        style.map('Horizontal.TScrollbar', background=[('active', COLORS['button_hover'])])
        style.configure('TPanedwindow', background=COLORS['bg_dark'])
        style.configure('Sash', background=COLORS['accent'], sashthickness=6, gripcount=0, relief='raised', borderwidth=1)
        style.map('Sash', background=[('active', COLORS['primary'])])
        main_frame = ttk.Frame(self, padding='10')
        main_frame.pack(fill=tk.BOTH, expand=True)
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=0)
        self.logo_label = ttk.Label(title_frame, background=COLORS['bg_dark'])
        self.logo_label.pack(side=tk.LEFT, padx=(0, 10))
        title_label = ttk.Label(title_frame, text='', font=(base_font_family, self.base_font_size + 8, 'bold'), foreground=COLORS['primary'])
        title_label.pack(side=tk.LEFT)
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        paned_window = ttk.PanedWindow(content_frame, orient=tk.VERTICAL)
        paned_window.pack(fill=tk.BOTH, expand=True)
        self.paned_window = paned_window
        left_panel_container = ttk.Frame(paned_window)
        paned_window.add(left_panel_container, weight=3)
        left_canvas = tk.Canvas(left_panel_container, background=COLORS['bg_dark'], highlightthickness=0)
        left_scrollbar = ttk.Scrollbar(left_panel_container, orient='vertical', command=left_canvas.yview)
        left_panel = ttk.Frame(left_canvas, padding='5')
        left_canvas.configure(yscrollcommand=left_scrollbar.set)
        left_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        left_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        left_canvas_frame = left_canvas.create_window((0, 0), window=left_panel, anchor=tk.NW)

        def configure_left_scroll(event):
            left_canvas.configure(scrollregion=left_canvas.bbox('all'))
            left_canvas.itemconfig(left_canvas_frame, width=left_canvas.winfo_width())
        left_panel.bind('<Configure>', configure_left_scroll)
        left_canvas.bind('<Configure>', lambda e: left_canvas.itemconfig(left_canvas_frame, width=left_canvas.winfo_width()))
        right_panel = ttk.Frame(paned_window, padding='2')
        paned_window.add(right_panel, weight=1)
        self.log_panel = LogPanel(right_panel, base_font_size=self.base_font_size, log_height=self.responsive_log_height)
        self.log_panel.pack(fill=tk.BOTH, expand=True)
        self.asset_input_frame = AssetInputFrame(left_panel, log_callback=self.log_panel.log, asset_change_callback=self._check_run_conditions_callback, main_app_ref=self, base_font_size=self.base_font_size)
        self.asset_input_frame.pack(fill=tk.X, pady=5)
        system_frame = ttk.LabelFrame(left_panel, text='System Information', padding='3')
        system_frame.pack(fill=tk.X, pady=2)
        self.system_frame = system_frame
        self.test_control_frame = TestControlFrame(parent=left_panel, log_callback=self.log_panel.log, main_app_ref=self, base_font_size=self.base_font_size, on_load_profile_action=self._on_profile_loaded_action, on_run_tests_action=self.run_tests, on_view_results_action=self.view_results, on_edit_profiles_action=self._on_edit_profiles_action)
        self.test_control_frame.pack(fill=tk.X, pady=5)
        self.log_panel.log('Arc Nexus initialized.')
        self.log_panel.log('Select tests to run and enter asset information.')

    def update_system_info_display(self):
        """Update the system information display with collected data."""
        for widget in self.system_frame.winfo_children():
            widget.destroy()
        self.system_frame.columnconfigure(0, weight=0)
        self.system_frame.columnconfigure(1, weight=1)
        self.system_frame.columnconfigure(2, weight=0)
        self.system_frame.columnconfigure(3, weight=1)
        label_font = (FONTS['normal_font'], self.base_font_size)
        pady_val = 1
        padx_val = (3, 5)
        ttk.Label(self.system_frame, text='Serial#:', font=label_font).grid(row=0, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        serial_frame = ttk.Frame(self.system_frame)
        serial_frame.grid(row=0, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        serial_text = tk.Text(serial_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        serial_text.pack(fill=tk.BOTH, expand=True)
        serial_text.tag_configure('serial', foreground=COLORS['serial_color'])
        serial_text.insert(tk.END, self.serial_number, 'serial')
        serial_text.configure(state='disabled')
        ttk.Label(self.system_frame, text='CPU:', font=label_font).grid(row=0, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        cpu_frame = ttk.Frame(self.system_frame)
        cpu_frame.grid(row=0, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        cpu_text = tk.Text(cpu_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        cpu_text.pack(fill=tk.BOTH, expand=True)
        cpu_text.tag_configure('cpu_model', foreground=COLORS['cpu_model'])
        cpu_text.tag_configure('cpu_speed', foreground=COLORS['cpu_speed'])
        cpu_info = self.system_info['cpu']
        if '@' in cpu_info:
            cpu_parts = cpu_info.split('@')
            cpu_text.insert(tk.END, cpu_parts[0].strip(), 'cpu_model')
            cpu_text.insert(tk.END, ' @ ')
            cpu_text.insert(tk.END, cpu_parts[1].strip(), 'cpu_speed')
        else:
            cpu_text.insert(tk.END, cpu_info, 'cpu_model')
        cpu_text.configure(state='disabled')
        ttk.Label(self.system_frame, text='Memory:', font=label_font).grid(row=1, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        mem_frame = ttk.Frame(self.system_frame)
        mem_frame.grid(row=1, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        mem_text = tk.Text(mem_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        mem_text.pack(fill=tk.BOTH, expand=True)
        mem_text.tag_configure('memory', foreground=COLORS['memory'])
        mem_text.insert(tk.END, self.system_info['memory'], 'memory')
        mem_text.configure(state='disabled')
        ttk.Label(self.system_frame, text='Graphics:', font=label_font).grid(row=1, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        gpu_frame = ttk.Frame(self.system_frame)
        gpu_frame.grid(row=1, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        gpu_text = tk.Text(gpu_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        gpu_text.pack(fill=tk.BOTH, expand=True)
        gpu_text.tag_configure('gpu', foreground=COLORS['gpu'])
        gpus = self.system_info['gpus']
        gpu_display = ', '.join(gpus) if isinstance(gpus, list) else str(gpus)
        gpu_text.insert(tk.END, gpu_display, 'gpu')
        gpu_text.configure(state='disabled')
        ttk.Label(self.system_frame, text='Resolution:', font=label_font).grid(row=2, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        res_frame = ttk.Frame(self.system_frame)
        res_frame.grid(row=2, column=1, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        res_text = tk.Text(res_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        res_text.pack(fill=tk.BOTH, expand=True)
        res_text.tag_configure('resolution', foreground=COLORS['resolution'])
        res_text.insert(tk.END, self.system_info['screen_resolution'], 'resolution')
        res_text.configure(state='disabled')
        ttk.Label(self.system_frame, text='Battery:', font=label_font).grid(row=2, column=2, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        batt_frame = ttk.Frame(self.system_frame)
        batt_frame.grid(row=2, column=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        batt_text = tk.Text(batt_frame, wrap=tk.WORD, height=1, width=30, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
        batt_text.pack(fill=tk.BOTH, expand=True)
        batt_text.tag_configure('batt_percent', foreground=COLORS['battery_percent'])
        batt_text.tag_configure('batt_status', foreground=COLORS['battery_status'])
        batt_text.tag_configure('batt_health', foreground=COLORS['battery_health'])
        batt_text.tag_configure('batt_cycles', foreground=COLORS['battery_cycles'])
        batt_text.tag_configure('no_battery', foreground=COLORS['no_battery'])
        batt = self.system_info.get('battery', {})
        if batt.get('present'):
            batt_text.insert(tk.END, f"{batt.get('percent', 'N/A')}%", 'batt_percent')
            batt_text.insert(tk.END, f" ({batt.get('status', 'N/A')})", 'batt_status')
            if batt.get('health'):
                batt_text.insert(tk.END, f" {batt['health']}", 'batt_health')
            if batt.get('cycle_count'):
                batt_text.insert(tk.END, f" {batt['cycle_count']}", 'batt_cycles')
        else:
            batt_text.insert(tk.END, 'No battery', 'no_battery')
        batt_text.configure(state='disabled')
        disks_info = self.system_info.get('disks', [])
        disks_frame = ttk.Frame(self.system_frame)
        disks_frame.grid(row=3, column=1, columnspan=3, sticky=tk.EW, padx=padx_val[1], pady=pady_val)
        ttk.Label(self.system_frame, text='Disks:', font=label_font).grid(row=3, column=0, sticky=tk.W, padx=padx_val[0], pady=pady_val)
        if not disks_info:
            ttk.Label(disks_frame, text='No disks detected', font=label_font).pack(anchor=tk.W)
        else:
            disk_text = tk.Text(disks_frame, wrap=tk.WORD, height=len(disks_info) if len(disks_info) < 5 else 5, width=60, font=label_font, borderwidth=0, highlightthickness=0, background=COLORS['bg_dark'])
            disk_text.pack(fill=tk.BOTH, expand=True)
            disk_text.configure(foreground=COLORS['disk'])
            disk_text.tag_configure('model', foreground=COLORS['disk_model'])
            disk_text.tag_configure('size', foreground=COLORS['disk_size'])
            disk_text.tag_configure('type', foreground=COLORS['disk_type'])
            disk_text.tag_configure('status_ok', foreground=COLORS['disk_status_ok'])
            disk_text.tag_configure('status_bad', foreground=COLORS['disk_status_bad'])
            disk_text.tag_configure('status_unknown', foreground=COLORS['disk_status_unknown'])
            for i, d in enumerate(disks_info):
                if isinstance(d, dict):
                    model = d.get('model', 'Unknown').strip()
                    size = f"{d.get('size_gb', '?')}GB"
                    drive_type = d.get('type', 'Unknown')
                    status = d.get('smart_passed')
                    disk_text.insert(tk.END, f'Drive[{i}]: ')
                    disk_text.insert(tk.END, f'{model} ', 'model')
                    disk_text.insert(tk.END, f'[{size}] ', 'size')
                    disk_text.insert(tk.END, f'{{{drive_type}}} ', 'type')
                    if status is not None:
                        status_tag = 'status_ok' if status else 'status_bad'
                        status_text = 'OK' if status else 'BAD'
                        disk_text.insert(tk.END, f'[{status_text}]', status_tag)
                    else:
                        disk_text.insert(tk.END, '[?]', 'status_unknown')
                    if i < len(disks_info) - 1:
                        disk_text.insert(tk.END, '\n')
                else:
                    disk_text.insert(tk.END, f'Drive[{i}]: {str(d)}\n')
            disk_text.configure(state='disabled')

    def _on_profile_loaded_action(self, profile: Profile | None):
        """Callback for TestControlFrame when a profile is loaded (or selection cleared)."""
        self.current_profile = profile
        if hasattr(self, 'result_manager'):
            self.result_manager.clear_results()
        self._check_run_conditions()

    def _on_edit_profiles_action(self):
        """Callback for TestControlFrame after ProfileEditor is closed."""
        self.log_panel.log('Profile editor closed, refreshing profile list in TestControlFrame.', 'info')
        all_profiles = get_all_profiles()
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.update_profile_list(all_profiles)
        else:
            self.log_panel.log('Error: test_control_frame not found to update profile list.', 'error')

    def log(self, message: str, level: str='info'):
        """Convenience method for logging, usable by child windows."""
        if hasattr(self, 'log_panel') and self.log_panel:
            self.log_panel.log(message, level)
        else:
            print(f'NEXUS_APP_LOG [{level.upper()}]: {message}')

    def validate_inputs(self) -> bool:
        """Validate user inputs before running tests."""
        if not self.asset_input_frame.get_operator_id().strip():
            messagebox.showerror('Validation Error', 'Please enter an Operator ID')
            return False
        if not self.asset_input_frame.get_asset_number().strip():
            messagebox.showerror('Validation Error', 'Please enter an Asset Number')
            return False
        if not self.asset_input_frame.get_server_url().strip():
            messagebox.showerror('Validation Error', 'Please enter a Server URL')
            return False
        if not self.current_profile or not self.current_profile.tests:
            messagebox.showerror('Validation Error', 'Please select at least one test to run')
            return False
        return True

    def run_tests(self):
        """Run the selected tests based on the current profile."""
        self.result_manager.clear_results()
        self._check_run_conditions()
        if not self.current_profile or not self.current_profile.tests:
            messagebox.showwarning('No Profile Loaded', 'Please load a profile with tests to run, or ensure the profile has tests defined.')
            return
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(False)
        self.test_orchestrator.execute_tests()
        summary_data_to_pass = self.test_orchestrator.get_summary_data()
        overall_status_from_orchestrator = summary_data_to_pass.get('overall_status', 'UNKNOWN')
        final_log_level = 'success' if overall_status_from_orchestrator == 'PASS' else 'error'
        self.log_panel.update_status('Tests completed')
        self.log_panel.log(f'NexusApp: All tests completed. Overall Status: {overall_status_from_orchestrator}', final_log_level)
        self._check_run_conditions()
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(True)
        self.log_panel.log('Displaying End Screen Summary.', 'info')
        if not summary_data_to_pass:
            summary_data_to_pass = {'overall_status': 'ERROR', 'test_results': [], 'drive_wipe_results': [], 'errors': ['No test summary data was generated by orchestrator.']}
            self.log_panel.log('End screen summary data from orchestrator was missing or empty, using error default.', 'warning')
        end_screen = EndScreenWindow(parent=self, summary_data=summary_data_to_pass, asset_number=self.asset_input_frame.get_asset_number(), base_font_size=self.base_font_size)
        self.wait_window(end_screen)

    def _check_run_conditions_callback(self, *args):
        """Callback for AssetInputFrame to trigger check."""
        self._check_run_conditions()

    def _check_run_conditions(self):
        """Enable or disable the 'Run Tests' and 'View Results' buttons based on conditions."""
        op_id = self.asset_input_frame.get_operator_id().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ''
        asset_num = self.asset_input_frame.get_asset_number().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ''
        run_enabled = bool(op_id and asset_num and self.current_profile)
        view_results_enabled = bool(hasattr(self, 'result_manager') and self.result_manager.test_results_available and asset_num)
        if hasattr(self, 'test_control_frame'):
            self.test_control_frame.set_run_button_state(run_enabled)
            self.test_control_frame.set_view_results_button_state(view_results_enabled)

    def view_results(self):
        self.log_panel.log('View Results button clicked.', 'info')
        asset_num_val = self.asset_input_frame.get_asset_number().strip() if hasattr(self, 'asset_input_frame') and self.asset_input_frame else ''
        if not asset_num_val:
            self.log_panel.log('Asset number is missing. Cannot view results.', 'warning')
            messagebox.showwarning('Missing Information', 'Please enter an Asset Number to view results.')
            return
        results_dir = 'results'
        self.log_panel.log(f'Checking for results in directory: {results_dir} for asset: {asset_num_val}', 'info')
        if not os.path.exists(results_dir):
            self.log_panel.log(f"Results directory '{results_dir}' not found.", 'warning')
            messagebox.showinfo('No Results', f"Results directory '{results_dir}' does not exist. No results to display.")
            return
        try:
            all_files = os.listdir(results_dir)
        except FileNotFoundError:
            self.log_panel.log(f"Results directory '{results_dir}' was not found during listing. It might have been deleted.", 'error')
            messagebox.showerror('Error', f'Could not access results directory: {results_dir}. It may have been deleted.')
            return
        except Exception as e:
            self.log_panel.log(f"Error listing files in '{results_dir}': {e}", 'error')
            messagebox.showerror('Error', f'An unexpected error occurred while listing files in {results_dir}.')
            return
        asset_file_prefix = f'nexus_result_{asset_num_val}_'
        result_files = [os.path.join(results_dir, filename) for filename in all_files if filename.startswith(asset_file_prefix) and filename.endswith('.json')]
        if not result_files:
            self.log_panel.log(f"No result files found for asset '{asset_num_val}' in '{results_dir}'.", 'info')
            messagebox.showinfo('No Results Found', f"No result files found for asset number '{asset_num_val}'.")
            return
        self.log_panel.log(f"Found {len(result_files)} result file(s) for asset '{asset_num_val}':", 'info')
        for file_path in result_files:
            self.log_panel.log(f' - {file_path}', 'info')
        parsed_data = self.result_manager._load_and_parse_results(result_files)
        if not parsed_data:
            self.log_panel.log(f"No data could be parsed from the found result files for asset '{asset_num_val}'.", 'warning')
            messagebox.showinfo('Parsing Incomplete', f'Found {len(result_files)} file(s), but none could be successfully parsed. Check logs.')
            return
        self.log_panel.log(f"Successfully parsed {len(parsed_data)} of {len(result_files)} result file(s) for asset '{asset_num_val}'.", 'success')
        if parsed_data:
            first_result_summary = {'file': parsed_data[0]['file_path'], 'test_name': parsed_data[0]['test_name'], 'status': parsed_data[0]['status']}
            self.log_panel.log(f'First parsed result summary: {json.dumps(first_result_summary, indent=2)}', 'debug')
        results_window = ResultsDisplayWindow(self, parsed_data, asset_num_val, base_font_size=self.base_font_size)
        self.wait_window(results_window)

    def create_menu(self):
        menubar = tk.Menu(self)
        resolution_menu = tk.Menu(menubar, tearoff=0)
        configs = {'Small (1024x768)': ('1024x768', COLORS['small_font_size']), 'Medium (1440x900)': ('1440x900', COLORS['medium_font_size']), 'Standard (1920x1080)': ('1920x1080', COLORS['normal_font_size']), 'Large (2560x1440)': ('2560x1440', COLORS['large_font_size']), 'Fullscreen (Auto)': ('fullscreen', None)}
        for label, (res_str, font_size) in configs.items():
            resolution_menu.add_command(label=label, command=lambda r=res_str, fs=font_size: self.apply_resolution(r, fs))
        resolution_menu.add_separator()
        resolution_menu.add_command(label='Auto-detect Resolution', command=self.auto_detect_and_apply_resolution)
        menubar.add_cascade(label='Resolution', menu=resolution_menu)
        self.config(menu=menubar)

    def auto_detect_and_apply_resolution(self):
        """Auto-detect current resolution and apply appropriate settings."""
        try:
            res_str = get_screen_resolution(self)
            if res_str and 'x' in res_str:
                width, height = map(int, res_str.split('x'))
                font_size = get_font_size_for_resolution(width, height)
                self.apply_resolution(res_str, font_size)
                print(f'Auto-detected and applied resolution: {res_str} with font size {font_size}')
            else:
                print('Could not auto-detect resolution')
        except Exception as e:
            print(f'Error auto-detecting resolution: {e}')

    def update_widget_fonts(self, widget, new_font_size):
        """Recursively update the font for a widget and all its descendants."""
        try:
            widget.configure(font=(FONTS['normal_font'], new_font_size))
        except Exception:
            pass
        for child in widget.winfo_children():
            self.update_widget_fonts(child, new_font_size)

    def apply_resolution(self, res_str, _font_size):
        """
        Apply configuration from resolution_config.json with enhanced responsive design:
        - Remain fullscreen
        - Scale fonts using responsive calculation or config
        - Adjust control/log split
        - Update all component fonts and sizes
        """
        self.attributes('-fullscreen', True)
        width, height = (None, None)
        if res_str != 'fullscreen':
            try:
                width, height = map(int, res_str.split('x'))
                self.geometry(f'{width}x{height}')
                self.minsize(width, height)
                self.maxsize(width, height)
                self.current_resolution = (width, height)
            except Exception:
                pass
        cfg = RESOLUTION_CONFIG.get('resolutions', {}).get(res_str) or {}
        if _font_size:
            self.base_font_size = _font_size
        elif width and height:
            self.base_font_size = get_font_size_for_resolution(width, height)
            self.responsive_padding = get_padding_for_resolution(width, height)
            self.responsive_log_height = get_log_height_for_resolution(width, height)
        else:
            self.base_font_size = cfg.get('font_size', self.base_font_size)
        if not hasattr(self, 'responsive_padding') or self.responsive_padding is None:
            self.responsive_padding = {'large': cfg.get('padding_large', SIZES.get('padding_large', 10)), 'medium': cfg.get('padding_medium', SIZES.get('padding_medium', 8)), 'small': cfg.get('padding_small', SIZES.get('padding_small', 5))}
        if not hasattr(self, 'responsive_log_height') or self.responsive_log_height is None:
            self.responsive_log_height = cfg.get('log_panel_height', 10)
        self.setup_ui_styles()
        ratio = cfg.get('control_log_ratio', 0.6)
        self.update_idletasks()
        window_height = self.winfo_height()
        if hasattr(self, 'paned_window'):
            self.paned_window.sashpos(0, int(window_height * ratio))
        self.update_all_component_fonts()
        self.update_component_padding()
        print(f'Applied resolution {res_str} with font size {self.base_font_size}')

    def update_all_component_fonts(self):
        """Update fonts for all components that support it."""
        try:
            if hasattr(self, 'log_panel'):
                self.log_panel.update_font_size(self.base_font_size, self.responsive_log_height)
            if hasattr(self, 'asset_input_frame'):
                self.asset_input_frame.update_font_size(self.base_font_size)
            if hasattr(self, 'test_control_frame'):
                self.test_control_frame.update_font_size(self.base_font_size)
            if hasattr(self, 'system_info') and self.system_info:
                self.update_system_info_display()
        except Exception as e:
            print(f'Error updating component fonts: {e}')

    def update_component_padding(self):
        """Update padding for components using responsive values."""
        try:
            p_large = self.responsive_padding['large']
            p_medium = self.responsive_padding['medium']
            p_small = self.responsive_padding['small']
            if hasattr(self, 'main_frame'):
                self.main_frame.pack_configure(padding=p_large)
            if hasattr(self, 'system_frame'):
                self.system_frame.pack_configure(pady=p_medium)
            if hasattr(self, 'asset_input_frame'):
                self.asset_input_frame.pack_configure(pady=p_small)
            if hasattr(self, 'test_control_frame'):
                self.test_control_frame.pack_configure(pady=p_small)
        except Exception as e:
            print(f'Error updating component padding: {e}')
if __name__ == '__main__':
    pass