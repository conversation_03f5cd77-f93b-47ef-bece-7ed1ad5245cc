import tkinter as tk
from tkinter import ttk, scrolledtext
import datetime
from agent.gui.theme import COLORS, FONTS

class LogPanel(ttk.Frame):
    def __init__(self, parent, base_font_size=None, log_height=None):
        super().__init__(parent)

        # Store font size and calculate log font size
        self.base_font_size = base_font_size if base_font_size is not None else FONTS["log_font_size"]
        self.log_font_size = max(6, self.base_font_size - 1)  # Log font slightly smaller, minimum 6
        self.log_height = log_height if log_height is not None else 10

        # Log area
        self.log_text_area = scrolledtext.ScrolledText(
            self,
            wrap=tk.WORD,
            font=(FONTS["log_font"], self.log_font_size),
            bg=COLORS["bg_light"],
            fg=COLORS["text_dark"],
            state="disabled",
            height=self.log_height,
        )
        self.log_text_area.pack(pady=5, padx=5, fill=tk.BOTH, expand=True)

        # Status label
        self.status_label = ttk.Label(
            self,
            text="Status: Idle",
            font=(FONTS["normal_font"], self.base_font_size),
            anchor="w"
        )
        self.status_label.pack(pady=(0, 5), padx=5, fill=tk.X)

        self.log("LogPanel initialized.", "debug")

    def update_status(self, message: str):
        """Updates the status label."""
        self.status_label.config(text=f"Status: {message}")
        # self.log(f"Status updated: {message}", "debug") # Avoid self-logging status updates unless specifically needed

    def log(self, message: str, level: str = "info"):
        """Logs a message to the text area with a timestamp and level."""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"[{timestamp}] [{level.upper()}] {message}\n"

        self.log_text_area.config(state="normal")
        if level.lower() == "error":
            self.log_text_area.insert(tk.END, formatted_message, "error")
        elif level.lower() == "warning":
            self.log_text_area.insert(tk.END, formatted_message, "warning")
        elif level.lower() == "debug":
            self.log_text_area.insert(tk.END, formatted_message, "debug")
        else:
            self.log_text_area.insert(tk.END, formatted_message, "info")
        
        self.log_text_area.tag_config("error", foreground=COLORS["error"])
        self.log_text_area.tag_config("warning", foreground=COLORS["warning"])
        self.log_text_area.tag_config("debug", foreground=COLORS["debug_fg"]) # Assuming a debug color
        self.log_text_area.tag_config("info", foreground=COLORS["text_dark"]) # Default color for info

        self.log_text_area.see(tk.END)  # Scroll to the end
        self.log_text_area.config(state="disabled")

    def update_font_size(self, new_base_font_size, new_log_height=None):
        """Update the font sizes and log height for responsive design."""
        self.base_font_size = new_base_font_size
        self.log_font_size = max(6, self.base_font_size - 1)

        # Update log text area font
        self.log_text_area.config(font=(FONTS["log_font"], self.log_font_size))

        # Update log height if provided
        if new_log_height is not None:
            self.log_height = new_log_height
            self.log_text_area.config(height=self.log_height)

        # Update status label font
        self.status_label.config(font=(FONTS["normal_font"], self.base_font_size))
