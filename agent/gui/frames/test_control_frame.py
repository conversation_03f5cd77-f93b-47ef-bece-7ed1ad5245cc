import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Optional, List as PyList

# Import from our modules
from agent.gui.theme import FONTS
from agent.gui.profile_editor import ProfileEditor
from agent.tests.profiles import Profile
# Use aliasing to avoid potential name collisions if methods with same names are created
from agent.tests.profiles import get_all_profiles as system_get_all_profiles
from agent.tests.profiles import load_profile as system_load_profile


class TestControlFrame(ttk.LabelFrame):
    def __init__(self, parent,
                 log_callback: Callable[[str, str], None],
                 main_app_ref: tk.Tk, # Should be NexusApp instance
                 base_font_size: int,
                 on_load_profile_action: Callable[[Optional[Profile]], None], # Callback to NexusApp
                 on_run_tests_action: Callable[[], None],    # Callback to NexusApp.run_tests
                 on_view_results_action: Callable[[], None], # Callback to NexusApp.view_results
                 on_edit_profiles_action: Callable[[], None], # Callback to NexusApp to refresh list
                 **kwargs):
        super().__init__(parent, text="Tests", padding="5", **kwargs)
        
        self.log_callback = log_callback
        self.main_app_ref = main_app_ref
        self.base_font_size = base_font_size
        
        self.on_load_profile_action = on_load_profile_action
        self.on_run_tests_action_callback = on_run_tests_action # Storing the direct callback
        self.on_view_results_action_callback = on_view_results_action # Storing the direct callback
        self.on_edit_profiles_action_callback = on_edit_profiles_action # Storing the direct callback

        self.profile_name = tk.StringVar()
        self.current_profile: Optional[Profile] = None

        self._setup_ui()
        self.populate_initial_profiles()

    def _setup_ui(self):
        """Sets up the UI elements within this frame."""
        self.columnconfigure(0, weight=1) # Allow the main content column to expand

        # Profile selection frame
        profile_frame = ttk.Frame(self)
        profile_frame.grid(row=0, column=0, columnspan=2, padx=5, pady=5, sticky=tk.W + tk.E)
        profile_frame.columnconfigure(0, weight=0)  # Label
        profile_frame.columnconfigure(1, weight=1)  # Dropdown
        profile_frame.columnconfigure(2, weight=0)  # Buttons

        ttk.Label(profile_frame, text="Profile:", font=(FONTS["profile_font"], self.base_font_size)).grid(row=0, column=0, padx=(0, 5), sticky=tk.W)

        self.profile_combo = ttk.Combobox(
            profile_frame,
            textvariable=self.profile_name,
            width=15, # Adjust as needed
            font=(FONTS["normal_font"], self.base_font_size - 2),
            state="readonly" # Typically users select, not type profile names
        )
        self.profile_combo.grid(row=0, column=1, padx=5, sticky=tk.W + tk.E)
        self.profile_combo.bind("<<ComboboxSelected>>", self.on_profile_combo_select)


        profile_buttons_frame = ttk.Frame(profile_frame)
        profile_buttons_frame.grid(row=0, column=2, padx=5, sticky=tk.E)

        load_button = ttk.Button(
            profile_buttons_frame,
            text="Load",
            command=self.on_load_profile_button_pressed, # Calls internal method first
            style="TButton"
        )
        load_button.pack(side=tk.LEFT, padx=2)

        edit_button = ttk.Button(
            profile_buttons_frame,
            text="Edit",
            command=self.open_profile_editor, # Calls internal method
            style="TButton"
        )
        edit_button.pack(side=tk.LEFT, padx=2)

        # Run button (command is now the passed callback)
        self.run_tests_button = ttk.Button(
            self, 
            text="Run Tests (Profile Based)", 
            command=self.on_run_tests_action_callback, 
            style="TButton"
        )
        self.run_tests_button.grid(row=1, column=0, columnspan=2, pady=(10, 5), padx=5, sticky=tk.EW)

        # View Results button (command is now the passed callback)
        self.view_results_button = ttk.Button(
            self,
            text="View Results",
            command=self.on_view_results_action_callback,
            style="TButton",
            state=tk.DISABLED  # Start disabled
        )
        self.view_results_button.grid(row=2, column=0, columnspan=2, pady=(0, 5), padx=5, sticky=tk.EW)

    def on_profile_combo_select(self, event=None):
        """Handles selection change in combobox."""
        # This can optionally auto-load the profile, or require "Load" button.
        # For now, let's make "Load" button explicit.
        # If auto-load is desired: self.load_profile(self.profile_name.get())
        pass

    def load_profile(self, profile_name_str: str):
        """Loads a profile by name and notifies NexusApp."""
        if not profile_name_str:
            self.log_callback("No profile name provided to load.", "warning")
            self.current_profile = None
            self.on_load_profile_action(None) # Notify with no profile
            return

        profile_obj = system_load_profile(profile_name_str)
        if not profile_obj:
            self.log_callback(f"Profile '{profile_name_str}' not found.", "error")
            self.current_profile = None
        else:
            self.current_profile = profile_obj
            self.profile_name.set(f"{profile_obj.name} ({profile_obj.device_type})") # Update display in combobox
            self.log_callback(f"Loaded profile in TestControlFrame: {profile_obj.name}", "info")
        
        self.on_load_profile_action(self.current_profile) # Notify NexusApp

    def on_load_profile_button_pressed(self):
        """Handles the 'Load' button click."""
        selected_profile_name_full = self.profile_name.get() # e.g., "Laptop (laptop)"
        if not selected_profile_name_full:
            messagebox.showerror("Error", "Please select a profile from the dropdown.", parent=self.main_app_ref)
            return
        
        # Extract just the profile name if it includes device type
        profile_name_actual = selected_profile_name_full.split(" (")[0]
        self.load_profile(profile_name_actual)

    def open_profile_editor(self):
        """Opens the ProfileEditor window."""
        editor = ProfileEditor(self.main_app_ref, callback=self.on_edit_profiles_action_callback)
        self.main_app_ref.wait_window(editor)

    def update_profile_list(self, fresh_profiles: PyList[Profile]):
        """Updates the profile combobox with a new list of profiles."""
        current_selection_full = self.profile_name.get()
        current_selection_actual = current_selection_full.split(" (")[0] if current_selection_full else None

        profile_display_names = [f"{p.name} ({p.device_type})" for p in fresh_profiles]
        self.profile_combo['values'] = profile_display_names

        if not profile_display_names:
            self.profile_name.set("")
            self.current_profile = None
            self.on_load_profile_action(None) # Notify NexusApp about no profile
            return

        # Try to re-select the previously selected profile if it still exists
        reselected = False
        if current_selection_actual:
            for p_display_name in profile_display_names:
                if p_display_name.startswith(current_selection_actual + " ("):
                    self.profile_name.set(p_display_name)
                    # Reload this profile's details
                    self.load_profile(current_selection_actual)
                    reselected = True
                    break
        
        if not reselected:
            # Select the first profile in the new list if previous one is gone or none was selected
            self.profile_name.set(profile_display_names[0])
            first_profile_actual_name = profile_display_names[0].split(" (")[0]
            self.load_profile(first_profile_actual_name)


    def populate_initial_profiles(self):
        """Populates the profile combobox on initialization."""
        profiles = system_get_all_profiles()
        self.update_profile_list(profiles) # This will also load the first profile by default

    def set_run_button_state(self, enabled: bool):
        """Sets the state of the Run Tests button."""
        self.run_tests_button.config(state=tk.NORMAL if enabled else tk.DISABLED)

    def set_view_results_button_state(self, enabled: bool):
        """Sets the state of the View Results button."""
        self.view_results_button.config(state=tk.NORMAL if enabled else tk.DISABLED)

    def get_current_profile(self) -> Optional[Profile]:
        return self.current_profile
    
    def get_selected_profile_display_name(self) -> str:
        return self.profile_name.get()

    def set_profile_by_name_and_load(self, profile_to_select_name: str):
        """Attempts to select a profile by its actual name and load it."""
        all_display_names = self.profile_combo['values']
        target_display_name = None
        for disp_name in all_display_names:
            if disp_name.startswith(profile_to_select_name + " ("):
                target_display_name = disp_name
                break
        
        if target_display_name:
            self.profile_name.set(target_display_name) # Set combobox
            self.load_profile(profile_to_select_name) # Load the profile data
        else:
            self.log_callback(f"Profile '{profile_to_select_name}' not found in combobox to set.", "warning")

    def update_font_size(self, new_base_font_size):
        """Update font sizes for responsive design."""
        self.base_font_size = new_base_font_size

        # Update profile label
        for widget in self.winfo_children():
            if isinstance(widget, ttk.Frame):
                # Handle profile frame
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Label):
                        try:
                            child.config(font=(FONTS["profile_font"], self.base_font_size))
                        except Exception:
                            pass
                    elif isinstance(child, ttk.Combobox):
                        try:
                            child.config(font=(FONTS["normal_font"], self.base_font_size - 2))
                        except Exception:
                            pass
                    elif isinstance(child, ttk.Frame):
                        # Handle button frame
                        for grandchild in child.winfo_children():
                            if isinstance(grandchild, ttk.Button):
                                try:
                                    grandchild.config(font=(FONTS["normal_font"], self.base_font_size))
                                except Exception:
                                    pass
            elif isinstance(widget, ttk.Button):
                # Handle main buttons (Run Tests, View Results)
                try:
                    widget.config(font=(FONTS["normal_font"], self.base_font_size))
                except Exception:
                    pass

if __name__ == '__main__':
    # Example Usage
    root = tk.Tk()
    root.geometry("400x200")
    root.title("Test Control Frame Test")

    # Dummy callbacks and refs
    def log_action(message, level): print(f"LOG [{level.upper()}]: {message}")
    def loaded_action(profile): print(f"PROFILE LOADED: {profile.name if profile else 'None'}")
    def run_action(): print("RUN TESTS ACTION")
    def view_action(): print("VIEW RESULTS ACTION")
    
    # This callback would be in NexusApp to refresh TestControlFrame's list
    def edit_profiles_done_action():
        print("EDIT PROFILES DONE (NexusApp refreshes TestControlFrame's list here)")
        # Simulate getting new profiles
        # In real app, TestControlFrame.update_profile_list would be called by NexusApp
        # For this test, we can't directly call it without instance.

    style = ttk.Style(root)
    style.theme_use('clam') # Ensure a theme is active for ttk widgets

    test_control = TestControlFrame(
        root,
        log_callback=log_action,
        main_app_ref=root,
        base_font_size=10,
        on_load_profile_action=loaded_action,
        on_run_tests_action=run_action,
        on_view_results_action=view_action,
        on_edit_profiles_action=edit_profiles_done_action,
        padding="10"
    )
    test_control.pack(fill=tk.BOTH, expand=True)
    
    # Example: Simulate NexusApp enabling run button
    # test_control.set_run_button_state(True)

    root.mainloop()
