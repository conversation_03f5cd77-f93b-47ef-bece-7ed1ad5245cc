#!/usr/bin/env python3
"""agent.gui.drive_wipe_gui

Tkinter top-level window that lets the operator select drives and initiate a
secure wipe.  All heavy work is delegated to a *wipe_callback* that should run
in a background thread to keep the UI responsive.
"""
from __future__ import annotations

import tkinter as tk
import json
import os
from tkinter import ttk, messagebox, scrolledtext
from typing import Callable, Dict, List, Any

from agent.gui.theme import COLORS  # Re-use existing colour scheme

# Type aliases for readability
ProgressCb = Callable[[str, float, float, str], None]
LogCb = Callable[[str, str], None]
ResultCb = Callable[[str, Dict[str, Any]], None]
WipeCallback = Callable[[List[str], str, ProgressCb, LogCb, ResultCb, Callable[[], None]], None]

WIPE_METHODS = {
    "NIST 800-88 Clear (1-pass zeros)": "zero_fill",
    "NIST 800-88 Clear (1-pass random)": "random_fill",
    "DoD 5220.22-M (3-pass)": "dodshort",
    "Gutmann (35-pass)": "gutmann",
    "ATA Secure Erase (SSD Only)": "ata_secure_erase",
    "NVMe Sanitize (NVMe Only)": "nvme_sanitize",
    "Blkdiscard (Quick SSD/NVMe)": "blkdiscard",
    "Three-Pass Fill (Zero/One/Zero)": "three_pass_fill",
}

WIPE_METHOD_DESCRIPTIONS = {
    "NIST 800-88 Clear (1-pass zeros)": "Overwrites drive with zeros. Fast, effective for most modern drives.",
    "NIST 800-88 Clear (1-pass random)": "Overwrites drive with random data. Potentially more secure than zeros for some older HDDs.",
    "DoD 5220.22-M (3-pass)": "US Dept. of Defense standard (3 passes: zeros, ones, random). Slower, thorough.",
    "Gutmann (35-pass)": "Extremely thorough (35 passes with different patterns). Very slow, generally considered overkill for modern drives.",
    "ATA Secure Erase (SSD Only)": "Hardware-level command for SATA SSDs. Very fast and highly effective. Data is typically unrecoverable.",
    "NVMe Sanitize (NVMe Only)": "Hardware-level command for NVMe drives. Very fast and secure. Data is typically unrecoverable.",
    "Blkdiscard (Quick SSD/NVMe)": "Quickly discards all blocks on SSD/NVMe. Not a cryptographic wipe, but very fast for resetting drive state.",
    "Three-Pass Fill (Zero/One/Zero)": "A 3-pass method: overwrites with zeros, then ones, then zeros again. More thorough than a single pass."
}


class DriveWipeWindow(tk.Toplevel):
    """Modal window for selecting drives and starting wipes."""

    def __init__(
        self,
        parent: tk.Tk,
        drives_info: List[Dict[str, Any]],
        wipe_callback: WipeCallback,
        log_callback_main_gui: LogCb,
    ) -> None:
        super().__init__(parent)
        self.title("Secure Drive Wipe")
        self.attributes('-fullscreen', True)
        self.grab_set()  # Make modal

        self.drives_info = drives_info
        self.wipe_callback = wipe_callback
        self.log_main = log_callback_main_gui

        # Internal state
        self.drive_vars: Dict[str, tk.BooleanVar] = {}
        self.drive_labels: Dict[str, Dict[str, ttk.Label]] = {} # To store labels for SMART, Verification etc.
        self._wipe_in_progress: bool = False
        self.wipe_results_collected: List[Dict[str, Any]] = [] # To store detailed results

        self._build_ui()

    # ------------------------------------------------------------------ UI
    def _build_ui(self) -> None:
        self.configure(bg=COLORS["bg_dark"])
        style = ttk.Style(self)
        style.theme_use("clam")
        style.configure("TLabel", background=COLORS["bg_dark"], foreground=COLORS["text_light"])
        style.configure("TCheckbutton", background=COLORS["bg_dark"], foreground=COLORS["text_light"])
        style.configure("TButton", background=COLORS["accent"], foreground=COLORS["text_light"], font=("Arial", 12, "bold"))
        style.map("TButton", background=[("active", COLORS["primary"]), ("disabled", COLORS["bg_dark"])])
        style.configure("Horizontal.TProgressbar", background=COLORS["accent"], troughcolor=COLORS["bg_light"])
        style.configure("Small.TButton", font=("Arial", 10), padding=(2,2)) # For select/deselect buttons

        # Main layout frames
        top_frame = ttk.Frame(self, padding=10)
        top_frame.pack(fill=tk.BOTH, expand=True)

        # Drives list ----------------------------------------------------
        drives_labelframe = ttk.LabelFrame(top_frame, text="Detected Drives", padding=5)
        drives_labelframe.pack(fill=tk.X, pady=5)

        drives_canvas = tk.Canvas(drives_labelframe, height=150, bg=COLORS["bg_dark"], highlightthickness=0)
        vsb = ttk.Scrollbar(drives_labelframe, orient="vertical", command=drives_canvas.yview)
        drives_canvas.configure(yscrollcommand=vsb.set)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        drives_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Select All / Deselect All buttons for drives
        drive_select_buttons_frame = ttk.Frame(drives_labelframe) 
        drive_select_buttons_frame.pack(fill=tk.X, pady=(5,2), padx=2)

        select_all_btn = ttk.Button(drive_select_buttons_frame, text="Select All", command=self._select_all_drives, style="Small.TButton")
        select_all_btn.pack(side=tk.LEFT, padx=(0,2))

        deselect_all_btn = ttk.Button(drive_select_buttons_frame, text="Deselect All", command=self._deselect_all_drives, style="Small.TButton")
        deselect_all_btn.pack(side=tk.LEFT, padx=2)

        inner = ttk.Frame(drives_canvas)
        drives_canvas.create_window((0, 0), window=inner, anchor="nw")

        def _configure_scroll(_e):
            drives_canvas.configure(scrollregion=drives_canvas.bbox("all"))
        inner.bind("<Configure>", _configure_scroll)

        for idx, d in enumerate(self.drives_info):
            var = tk.BooleanVar(value=False)
            self.drive_vars[d["path"]] = var
            # When drive checkbox toggles, recompute recommended method
            def _make_cb(path):
                def _on_toggle(*_):
                    self._update_recommended_method()
                return _on_toggle
            var.trace_add("write", _make_cb(d["path"]))
            cb = ttk.Checkbutton(inner, variable=var)
            cb.grid(row=idx, column=0, sticky=tk.W)

            # Build description text
            desc_parts = [d.get("path"), d.get("model", "Unknown")]
            size_gb = d.get("size_gb")
            if size_gb:
                desc_parts.append(f"{size_gb} GB")
            desc_parts.append(d.get("type", "?"))
            # smart_ok = d.get("smart_passed") # Removed: S.M.A.R.T. status will be in its own label
            # if smart_ok is not None:
            #     desc_parts.append("SMART OK" if smart_ok else "SMART BAD")
            label = ttk.Label(inner, text=" | ".join(desc_parts), font=("Arial", 10))
            label.grid(row=idx, column=1, sticky=tk.W, padx=2)

            # Display initial S.M.A.R.T. status
            smart_text = "S.M.A.R.T.: Unknown"  # Default
            if "smart_passed" in d:  # Check for boolean 'smart_passed'
                smart_text = f"S.M.A.R.T.: {'PASSED' if d['smart_passed'] else 'FAILED'}"
            elif "smart_status" in d:  # Check for string 'smart_status'
                status_val = d['smart_status']
                if isinstance(status_val, str) and status_val.strip():  # Ensure not empty string
                    smart_text = f"S.M.A.R.T.: {status_val.strip().upper()}"
                elif status_val is None:  # Explicitly handle None
                    smart_text = "S.M.A.R.T.: N/A"
                # If smart_status is present but not a string or is empty, it will use default or previous

            smart_label = ttk.Label(inner, text=smart_text, font=("Arial", 9))
            smart_label.grid(row=idx, column=2, sticky=tk.W, padx=5)
            verification_label = ttk.Label(inner, text="Verification: -", font=("Arial", 9))  # Stays as placeholder
            verification_label.grid(row=idx, column=3, sticky=tk.W, padx=5)

            self.drive_labels[d["path"]] = {
                "smart": smart_label,
                "verification": verification_label,
                "main_desc": label # Store original label if needed later
            }

            # Disable if mounted
            # if d.get("mountpoints"):
            #     cb.state(["disabled"]) # AGENT: This was disabled to allow single drive wipe
            # Simple heuristic: likely system disk (first non-USB etc.)
            # if idx == 0:
            #     cb.state(["disabled"]) # AGENT: This was disabled to allow single drive wipe

        # Wipe method ----------------------------------------------------
        method_frame = ttk.LabelFrame(top_frame, text="Wipe Method", padding=5)
        method_frame.pack(fill=tk.X, pady=5)

        ttk.Label(method_frame, text="Method:").grid(row=0, column=0, sticky=tk.W)
        self.method_var = tk.StringVar()
        method_combo = ttk.Combobox(method_frame, textvariable=self.method_var, values=list(WIPE_METHODS.keys()), state="readonly")
        method_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        self.method_description_label = ttk.Label(method_frame, text="", wraplength=400, justify=tk.LEFT, font=("Arial", 9), foreground=COLORS["text_light"], background=COLORS["bg_dark"])
        self.method_description_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(3,0), padx=5)

        def _update_method_description(*args):
            selected_method_human = self.method_var.get()
            description = WIPE_METHOD_DESCRIPTIONS.get(selected_method_human, "No description available.")
            self.method_description_label.config(text=description)

        self.method_var.trace_add("write", _update_method_description)
        method_combo.current(0) # Set default selection
        _update_method_description() # Call once to set initial description for the default selection

        # Update recommended method initially (none selected yet)
        self._update_recommended_method()

        # Warning --------------------------------------------------------
        warn_label = ttk.Label(top_frame, text="WARNING: This will irreversibly destroy data on the selected drives.", foreground=COLORS["error"], font=("Arial", 12, "bold"))
        warn_label.pack(pady=5)

        # Progress -------------------------------------------------------
        self.progress_label = ttk.Label(top_frame, text="Idle")
        self.progress_label.pack(anchor=tk.W)
        self.progress = ttk.Progressbar(top_frame, mode="determinate", length=400)
        self.progress.pack(fill=tk.X, padx=5, pady=2)

        # Log area -------------------------------------------------------
        log_frame = ttk.LabelFrame(top_frame, text="Wipe Log", padding=5)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, wrap=tk.WORD, bg=COLORS["bg_dark"], fg=COLORS["text_light"], insertbackground=COLORS["accent"])
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Buttons --------------------------------------------------------
        btn_frame = ttk.Frame(top_frame)
        btn_frame.pack(fill=tk.X, pady=5)
        self.start_btn = ttk.Button(btn_frame, text="Start Wipe", command=self.on_start_wipe)
        self.start_btn.pack(side=tk.LEFT, padx=3)
        self.cancel_btn = ttk.Button(btn_frame, text="Cancel", command=self.on_cancel)
        self.cancel_btn.pack(side=tk.RIGHT, padx=3)

    def _select_all_drives(self) -> None:
        for var in self.drive_vars.values():
            var.set(True)

    def _deselect_all_drives(self) -> None:
        for var in self.drive_vars.values():
            var.set(False)

    # ------------------------------------------------------------------ Callbacks
    def on_start_wipe(self):
        selected = [path for path, var in self.drive_vars.items() if var.get()]
        if not selected:
            messagebox.showerror("No Drives Selected", "Select at least one drive to wipe.", parent=self)
            return

        method_human = self.method_var.get()
        method_key = WIPE_METHODS.get(method_human)
        if not method_key:
            messagebox.showerror("Invalid Method", "Please choose a wipe method.", parent=self)
            return

        # Basic compatibility checks (heuristic)
        incompatible = []
        if method_key == "ata_secure_erase":
            for d in self.drives_info:
                if d["path"] in selected and d.get("type") not in {"SSD", "HDD"}:
                    incompatible.append(d["path"])
        if method_key == "nvme_sanitize":
            for d in self.drives_info:
                if d["path"] in selected and d.get("type") != "NVMe":
                    incompatible.append(d["path"])
        if incompatible:
            messagebox.showerror("Incompatible Drives", f"Selected method not supported for: {', '.join(incompatible)}", parent=self)
            return

        # Confirmation dialog
        if not messagebox.askyesno("Confirm Wipe", f"You are about to wipe {len(selected)} drive(s). This CANNOT be undone. Continue?", parent=self):
            return

        # Disable UI
        self.start_btn.state(["disabled"])
        for cb in self.drive_vars.values():
            # We stored BooleanVar, not widget, so can't disable; not critical.
            pass
        self._wipe_in_progress = True

        # Kick off backend wipe process
        self.wipe_callback(selected, method_key, self.update_progress, self.log_to_gui, self.finalize_drive_wipe, self.all_wipes_completed)

    def update_progress(self, current_drive: str, overall: float, drive_progress: float, message: str):
        self.progress_label.config(text=f"{current_drive}: {message} ({drive_progress:.0f}% | overall {overall:.0f}%)")
        self.progress.config(value=overall)
        self.update_idletasks()

    def log_to_gui(self, message: str, level: str = "info"):
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)

    def finalize_drive_wipe(self, drive_path: str, result_data: Dict[str, Any]):
        self.log_main(f"Drive wipe done for {drive_path}: {result_data.get('status', '?')}", "info")
        # Store the detailed result for this specific drive
        self.wipe_results_collected.append(result_data)

        # Update GUI labels for this drive
        if drive_path in self.drive_labels:
            smart_status = result_data.get("smart_status_pre_wipe", "-")
            self.drive_labels[drive_path]["smart"].config(text=f"S.M.A.R.T.: {smart_status}")

            verification_status = result_data.get("verification", "-")
            # Truncate verification string if too long for the column
            max_len = 40
            if len(verification_status) > max_len:
                verification_status = verification_status[:max_len-3] + "..."
            self.drive_labels[drive_path]["verification"].config(text=f"Verification: {verification_status}")

            # Potentially update main description label too, e.g., with overall status
            overall_status = result_data.get("status", "UNKNOWN")
            current_text = self.drive_labels[drive_path]["main_desc"].cget("text")
            self.drive_labels[drive_path]["main_desc"].config(text=f"{current_text} -> Status: {overall_status.upper()}")


        self.update_idletasks()


    def all_wipes_completed(self):
        self._wipe_in_progress = False
        self.progress_label.config(text="All wipes completed")
        
        # Send wipe results to Makor ERP if we have any results
        if self.wipe_results_collected:
            makor_config_path = os.path.join(os.path.dirname(__file__), "..", "integrations", "makor_config.json")
            makor_enabled = False
            try:
                if os.path.exists(makor_config_path):
                    with open(makor_config_path, 'r') as f:
                        makor_config = json.load(f)
                        makor_enabled = makor_config.get("enabled", False)
                else:
                    self.log_to_gui("Makor ERP config file not found.", "warning")
                    self.log_main("Makor ERP config file not found.", "warning")

            except Exception as e:
                self.log_to_gui(f"Error reading Makor ERP config: {e}", "error")
                self.log_main(f"Error reading Makor ERP config: {e}", "error")
                makor_enabled = False # Default to disabled on error

            if makor_enabled:
                try:
                    # Import here to avoid circular imports
                    from agent.integrations.makor_erp import send_wipe_results_to_makor
                    
                    self.log_to_gui("Sending audit report to Makor ERP...", "info")
                    self.progress_label.config(text="Sending audit report to Makor ERP...")
                    self.update_idletasks()
                    
                    # Send the wipe results to Makor ERP
                    success = send_wipe_results_to_makor(self.wipe_results_collected)
                    
                    if success:
                        self.log_to_gui("Successfully sent audit report to Makor ERP", "info")
                        self.log_main("Successfully sent audit report to Makor ERP", "info")
                        self.progress_label.config(text="All wipes completed, audit report sent")
                    else:
                        self.log_to_gui("Failed to send audit report to Makor ERP", "error")
                        self.log_main("Failed to send audit report to Makor ERP", "error")
                        self.progress_label.config(text="All wipes completed, audit report sending failed")
                except ImportError:
                    self.log_to_gui("Makor ERP integration not available", "warning")
                    self.log_main("Makor ERP integration not available", "warning")
                    self.progress_label.config(text="All wipes completed")
                except Exception as e:
                    self.log_to_gui(f"Error sending audit report to Makor ERP: {e}", "error")
                    self.log_main(f"Error sending audit report to Makor ERP: {e}", "error")
                    self.progress_label.config(text="All wipes completed, audit report sending failed")
        else: # This 'else' corresponds to 'if self.wipe_results_collected:'
            self.progress_label.config(text="All wipes completed (no results to send)")
            # Correctly indented lines below, and removed the mis-indented blank line (original line 350)
            self.start_btn.state(["!disabled"])
            self.cancel_btn.config(text="Close")

    def on_cancel(self): # Correctly indented at class level
        if self._wipe_in_progress:
            if not messagebox.askyesno("Abort Wipe", "Wipes are still running. Abort?", parent=self):
                return
            # TODO: backend abort mechanism
        self.destroy()

    # ------------------------------------------------------------------ Recommendation Logic
    def _update_recommended_method(self) -> None:
        """Select a wipe method appropriate for the currently selected drives."""
        selected_paths = [p for p, var in self.drive_vars.items() if var.get()]
        if not selected_paths:
            return  # keep current selection

        selected_types = {d.get("type") for d in self.drives_info if d["path"] in selected_paths}

        recommended_human = None
        if len(selected_types) == 1:
            drive_type = selected_types.pop()
            if drive_type == "NVMe":
                recommended_human = "NVMe Sanitize (NVMe Only)"
            elif drive_type == "SSD":
                recommended_human = "ATA Secure Erase (SSD Only)"
            else:  # Assume HDD or unknown
                recommended_human = "NIST 800-88 Clear (1-pass zeros)"
        else:
            # Mixed types -> choose universal, safe option
            recommended_human = "NIST 800-88 Clear (1-pass zeros)"

        # Only change if different to avoid flickering when user overrides
        current = self.method_var.get()
        if recommended_human and current != recommended_human:
            self.method_var.set(recommended_human)
