#!/usr/bin/env python3
"""Crucible Crucible – legacy wrapper.

This tiny file exists solely for backward compatibility. All real functionality
has moved to :pymod:`agent.cli`. Importing this module has negligible overhead
and will no longer pull heavy dependencies.
"""

from __future__ import annotations

import asyncio
import sys

from agent.cli import main as _main


def _run() -> None:
    asyncio.run(_main())


if __name__ == "__main__":  # pragma: no cover
    _run()
    sys.exit()
