# Nexus Foundry – Debian Live Build Guide

This document explains how to build a reproducible Debian Live image for Nexus Foundry and publish PXE artifacts to Nexus. It uses Debian live-build executed inside a Debian container on Fedora (or Ubuntu/Debian host), outputs an ISO for testing and the PXE trio: `vmlinuz`, `initrd.img`, `filesystem.squashfs`.

- Audience: Developers/ops building the boot image.
- Output targets: `Nexus/artifacts/nexus-foundry/<version>/`.
- Status: Reference guide; recommended to keep the live-build config in a separate repo (e.g., `nexus-foundry`).

---

## Why live-build (vs. editing a VM)

- Reproducible: Config-as-code in Git produces the same image every time.
- Fast iteration: Change files/hooks → rebuild. No manual VM steps.
- PXE-ready: Produces `vmlinuz`, `initrd.img`, and `filesystem.squashfs` for live-boot.

---

## Recommended repository layout (separate repo)

Repo name suggestion: `nexus-foundry`

```
nexus-foundry/
  config/
    package-lists/
      nexus-foundry.list.chroot     # Packages to install
    includes.chroot/
      opt/crucible/...              # App payload (launcher, assets) - adjust path if you rename later
      etc/systemd/system/nexus-foundry.service
    hooks/
      normal/
        10-enable-nexus-foundry.chroot   # systemctl enable service
  Makefile                          # build/extract/publish targets
  scripts/
    build_in_container.sh           # Debian Bookworm privileged container runner
  README.md
```

Notes:
- `includes.chroot/<absolute-path>` mirrors final filesystem paths.
- Hooks ending with `.chroot` run inside the target rootfs during build.

---

## Minimal configuration examples

- `config/package-lists/nexus-foundry.list.chroot`
```text
# Base UI/runtime (adjust as needed)
xorg
openbox
python3
python3-pip
git
network-manager
curl
# Live boot stack (often pulled by live-build but keep explicit)
live-boot
live-config
```

- `config/includes.chroot/etc/systemd/system/nexus-foundry.service`
```ini
[Unit]
Description=Nexus Foundry Launcher
After=multi-user.target network-online.target
Wants=network-online.target

[Service]
Type=simple
ExecStart=/usr/bin/python3 /opt/crucible/updater/updater_gui.py
Restart=always

[Install]
WantedBy=multi-user.target
```

- `config/hooks/normal/10-enable-nexus-foundry.chroot`
```sh
#!/bin/sh
set -e
systemctl enable nexus-foundry.service
```

Make hooks executable:
```bash
chmod +x config/hooks/normal/*.chroot
```

---

## Building on Fedora using a Debian container

Requirements: Podman or Docker with `--privileged` (for loop devices, squashfs).

Quick start script (example `scripts/build_in_container.sh`):
```bash
#!/usr/bin/env bash
set -euo pipefail
IMG=debian:bookworm
WORKDIR=${WORKDIR:-$(pwd)}
RUNNER=${RUNNER:-podman}   # or: docker
EXTRA_ARGS=${EXTRA_ARGS:---privileged}

$RUNNER run --rm -it $EXTRA_ARGS \
  -v "$WORKDIR":/work -w /work \
  $IMG bash -lc '
    set -e
    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get install -y \
      live-build live-config live-tools squashfs-tools xz-utils ca-certificates git
    # Configure for Debian 12 AMD64 ISO-hybrid
    [ -e auto/config ] || lb config --distribution bookworm --architectures amd64 --binary-images iso-hybrid
    # Build
    lb build
  '
```

Run:
```bash
chmod +x scripts/build_in_container.sh
scripts/build_in_container.sh
```

Artifacts after build (paths may vary):
- ISO: `live-image-amd64.hybrid.iso`
- Inside the build tree (often under `live/` or `binary/`):
  - `vmlinuz`
  - `initrd.img`
  - `filesystem.squashfs`

Tip: You can add a Makefile target to copy them into `out/<version>/` automatically.

---

## Publishing to Nexus PXE

Place artifacts on the host at `Nexus/artifacts/nexus-foundry/<version>/`:
```
Nexus/artifacts/nexus-foundry/<version>/
  vmlinuz
  initrd.img
  filesystem.squashfs
```

PXE Settings in Nexus UI (`Nexus/ui` → Dashboard → PXE Settings):
- Kernel URL: `/artifacts/nexus-foundry/<version>/vmlinuz`
- Initrd URL: `/artifacts/nexus-foundry/<version>/initrd.img`
- Kernel Args (example live-boot):
```
boot=live fetch=http://nexus.lan:8080/artifacts/nexus-foundry/<version>/filesystem.squashfs ip=dhcp noprompt
```
Optional args:
- `toram=fs` (loads squashfs to RAM; faster runtime if RAM allows)
- `console=tty0` or `console=ttyS0,115200n8` (serial)
- `nomodeset` (video quirks)

Per-MAC overrides are supported via the Nexus API if specific models need different args.

---

## Makefile idea (optional)

```makefile
VERSION ?= $(shell date +%Y%m%d-%H%M)
OUT := out/$(VERSION)
NEXUS_HOST ?= nexus.lan:8080
TARGET := ../Crucible/Nexus/artifacts/nexus-foundry/$(VERSION)

.PHONY: build
build:
	./scripts/build_in_container.sh

.PHONY: extract
extract:
	@mkdir -p $(OUT)
	@cp -v live/vmlinuz $(OUT)/vmlinuz || cp -v binary/live/vmlinuz $(OUT)/vmlinuz
	@cp -v live/initrd.img $(OUT)/initrd.img || cp -v binary/live/initrd.img $(OUT)/initrd.img
	@cp -v live/filesystem.squashfs $(OUT)/filesystem.squashfs || cp -v binary/live/filesystem.squashfs $(OUT)/filesystem.squashfs
	@sha256sum $(OUT)/* > $(OUT)/sha256sums.txt

.PHONY: publish
publish: extract
	@mkdir -p $(TARGET)
	@cp -v $(OUT)/* $(TARGET)/
	@echo "Kernel: /artifacts/nexus-foundry/$(VERSION)/vmlinuz"
	@echo "Initrd: /artifacts/nexus-foundry/$(VERSION)/initrd.img"
	@echo "Args:   boot=live fetch=http://$(NEXUS_HOST)/artifacts/nexus-foundry/$(VERSION)/filesystem.squashfs ip=dhcp noprompt"
```

Adjust `TARGET` if your live-build repo is outside this mono-repo.

---

## CI/CD (optional)

- GitHub Actions/GitLab CI: Debian runner builds on every tag.
- Upload artifacts to Nexus via SSH/HTTP and call an API to update PXE Settings.
- Keep artifacts versioned (e.g., semantic version or date-based) for easy rollbacks.

---

## Troubleshooting

- Container permissions: If build fails with loop/squashfs errors, ensure `--privileged` is set, or pass `/dev/loop-control` and `/dev/loop*` devices.
- Missing artifacts: Some live-build layouts place files under `binary/live/`; adapt paths in Make/ scripts.
- Network during boot: Ensure `live-boot`/`live-config` are present. Use `ip=dhcp` in kernel args.
- Autostart not running: Confirm service exists and is enabled inside the image (`systemctl status nexus-foundry`). Use a build hook to enable.

---

## References

- Debian Live Manual: https://live-team.pages.debian.net/live-manual/
- live-build package: `apt show live-build`
- Nexus README for PXE endpoints: `Nexus/README.md`
