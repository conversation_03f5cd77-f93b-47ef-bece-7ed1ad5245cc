#!/usr/bin/env python3
"""
Nexus API SDK for Python
Simple SDK for integrating Nexus hardware testing software with the credit system.

Installation:
    pip install requests

Usage:
    from nexus_sdk import NexusAPI
    
    api = NexusAPI('your-api-key', base_url='https://your-site.com')
    
    # Check balance
    balance = api.get_balance()
    
    # Perform operation
    result = api.consume_credits(
        operation_type='STANDARD_WIPE',
        device_serial='ABC123',
        device_model='Dell Optiplex 7090'
    )
"""

import requests
import json
import time
import random
from typing import Optional, Dict, Any
from enum import Enum


class OperationType(Enum):
    STANDARD_WIPE = 'STANDARD_WIPE'  # 1 credit
    WIPE_WITH_DIAGNOSTICS = 'WIPE_WITH_DIAGNOSTICS'  # 2 credits
    DIAGNOSTICS_ONLY = 'DIAGNOSTICS_ONLY'  # 1 credit


class OperationStatus(Enum):
    COMPLETED = 'COMPLETED'
    FAILED = 'FAILED'
    CANCELED = 'CANCELED'


class NexusAPIError(Exception):
    """Base exception for Nexus API errors"""
    pass


class InsufficientCreditsError(NexusAPIError):
    """Raised when there are insufficient credits for an operation"""
    pass


class NexusAPI:
    """
    Nexus API client for credit management and operation tracking.
    """
    
    def __init__(self, api_key: str, base_url: str = 'http://localhost:3000', timeout: float = 10.0, max_retries: int = 3, backoff_factor: float = 0.5, debug: bool = False):
        """
        Initialize the Nexus API client.
        
        Args:
            api_key: Your API key (starts with 'nxs_')
            base_url: Base URL of the Nexus website
            timeout: Per-request timeout in seconds
            max_retries: Max automatic retries on transient errors (429/5xx, timeouts)
            backoff_factor: Base delay factor for exponential backoff
            debug: Enable simple stdout debug logs
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'nexus-sdk/1.0'
        })
        self.timeout = timeout
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.debug = debug

    def _request(self, method: str, path: str, json: Optional[Dict[str, Any]] = None) -> requests.Response:
        """
        Internal request helper with retries and exponential backoff.
        Retries on 429, 500, 502, 503, 504 and on network timeouts/connection errors.
        """
        url = f'{self.base_url}{path}'
        last_err: Optional[Exception] = None
        for attempt in range(self.max_retries + 1):
            try:
                resp = self.session.request(method, url, json=json, timeout=self.timeout)
                if resp.status_code in (429, 500, 502, 503, 504):
                    if attempt < self.max_retries:
                        delay = self.backoff_factor * (2 ** attempt) + random.uniform(0, self.backoff_factor)
                        if self.debug:
                            print(f"[nexus-sdk] transient HTTP {resp.status_code}, retrying in {delay:.2f}s (attempt {attempt+1}/{self.max_retries})")
                        time.sleep(delay)
                        continue
                return resp
            except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                last_err = e
                if attempt < self.max_retries:
                    delay = self.backoff_factor * (2 ** attempt) + random.uniform(0, self.backoff_factor)
                    if self.debug:
                        print(f"[nexus-sdk] network error, retrying in {delay:.2f}s (attempt {attempt+1}/{self.max_retries}): {e}")
                    time.sleep(delay)
                    continue
                break
            except requests.exceptions.RequestException as e:
                # Non-retryable request exception
                raise NexusAPIError(f'Request error: {e}')
        if last_err:
            raise NexusAPIError(f'Network error contacting Nexus API: {last_err}')
        raise NexusAPIError('Unknown error contacting Nexus API')
    
    def get_balance(self) -> Dict[str, Any]:
        """
        Get current credit balance and subscription info.
        
        Returns:
            Dict containing balance, subscription, and user info
            
        Raises:
            NexusAPIError: If the API request fails
        """
        try:
            resp = self._request('GET', '/api/credits/balance')
            if resp.status_code == 401:
                raise NexusAPIError('Invalid API key or key expired')
            if not resp.ok:
                raise NexusAPIError(f'Failed to get balance: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to get balance: {e}')
    
    def get_license_status(self) -> Dict[str, Any]:
        """
        Get license summary and list for the authenticated user.
        """
        try:
            resp = self._request('GET', '/api/licenses/status')
            if resp.status_code == 401:
                raise NexusAPIError('Invalid API key or key expired')
            if not resp.ok:
                raise NexusAPIError(f'Failed to get license status: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to get license status: {e}')

    def activate_license(self, license_key: str) -> Dict[str, Any]:
        """
        Activate a license key for the authenticated account.
        Idempotent: if already activated, returns current state.
        """
        payload = {'licenseKey': license_key}
        try:
            resp = self._request('POST', '/api/licenses/activate', json=payload)
            if resp.status_code == 401:
                raise NexusAPIError('Invalid API key or key expired')
            if resp.status_code == 400:
                raise NexusAPIError('Missing or invalid licenseKey')
            if resp.status_code == 404:
                raise NexusAPIError('License not found for this account')
            if not resp.ok:
                raise NexusAPIError(f'Failed to activate license: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to activate license: {e}')

    def consume_credits(
        self,
        operation_type: OperationType,
        device_serial: str,
        device_model: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Consume credits for an operation.
        
        Args:
            operation_type: Type of operation to perform
            device_serial: Serial number of the device
            device_model: Model of the device (optional)
            metadata: Additional metadata for the operation (optional)
            
        Returns:
            Dict containing operation details and remaining balance
            
        Raises:
            InsufficientCreditsError: If there are not enough credits
            NexusAPIError: If the API request fails
        """
        payload = {
            'operationType': operation_type.value if isinstance(operation_type, OperationType) else operation_type,
            'deviceSerial': device_serial
        }
        
        if device_model:
            payload['deviceModel'] = device_model
        
        if metadata:
            payload['metadata'] = metadata
        
        try:
            resp = self._request('POST', '/api/credits/consume', json=payload)
            if resp.status_code == 402:
                try:
                    error_data = resp.json()
                    msg = error_data.get('error', 'Insufficient credits')
                except Exception:
                    msg = 'Insufficient credits'
                raise InsufficientCreditsError(msg)
            if resp.status_code == 401:
                raise NexusAPIError('Invalid API key or key expired')
            if not resp.ok:
                raise NexusAPIError(f'Failed to consume credits: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except InsufficientCreditsError:
            raise
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to consume credits: {e}')
    
    def complete_operation(
        self,
        operation_id: str,
        status: OperationStatus,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Mark an operation as completed, failed, or canceled.
        Failed/canceled operations will refund credits.
        
        Args:
            operation_id: ID of the operation to update
            status: New status for the operation
            error_message: Error message if operation failed (optional)
            metadata: Additional metadata (optional)
            
        Returns:
            Dict containing operation details and refund status
            
        Raises:
            NexusAPIError: If the API request fails
        """
        payload = {
            'operationId': operation_id,
            'status': status.value if isinstance(status, OperationStatus) else status
        }
        
        if error_message:
            payload['errorMessage'] = error_message
        
        if metadata:
            payload['metadata'] = metadata
        
        try:
            resp = self._request('PATCH', '/api/credits/consume', json=payload)
            if resp.status_code == 401:
                raise NexusAPIError('Invalid API key or key expired')
            if not resp.ok:
                raise NexusAPIError(f'Failed to update operation: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to update operation: {e}')
    
    def create_checkout_session(
        self,
        purchase_type: str,
        product_id: str,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Initiate a Stripe Checkout session using API key authentication.
        Returns the session payload including a URL to open in the default browser.
        
        Args:
            purchase_type: 'credits' | 'subscription' | 'license'
            product_id: e.g. 'credits_100' (see server catalog)
            success_url: Optional success redirect URL
            cancel_url: Optional cancel redirect URL
        """
        payload: Dict[str, Any] = {
            'type': purchase_type,
            'productId': product_id,
        }
        if success_url:
            payload['successUrl'] = success_url
        if cancel_url:
            payload['cancelUrl'] = cancel_url
        try:
            resp = self._request('POST', '/api/checkout', json=payload)
            if resp.status_code == 401:
                raise NexusAPIError('Authentication required: invalid or expired API key')
            if not resp.ok:
                raise NexusAPIError(f'Failed to create checkout session: HTTP {resp.status_code} - {resp.text}')
            return resp.json()
        except NexusAPIError:
            raise
        except Exception as e:
            raise NexusAPIError(f'Failed to create checkout session: {e}')

    def initiate_credit_purchase(
        self,
        credit_pack_id: str,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None
    ) -> str:
        """
        Convenience wrapper to create a credit purchase checkout session.
        Returns the Stripe Checkout URL to open in a browser.
        """
        data = self.create_checkout_session(
            purchase_type='credits',
            product_id=credit_pack_id,
            success_url=success_url,
            cancel_url=cancel_url,
        )
        return data.get('url')
    
    def check_credits_before_operation(self, operation_type: OperationType) -> bool:
        """
        Check if there are enough credits for an operation.
        
        Args:
            operation_type: Type of operation to check
            
        Returns:
            True if there are enough credits, False otherwise
        """
        credit_costs = {
            OperationType.STANDARD_WIPE: 1,
            OperationType.WIPE_WITH_DIAGNOSTICS: 2,
            OperationType.DIAGNOSTICS_ONLY: 1
        }
        
        try:
            balance_info = self.get_balance()
            required_credits = credit_costs.get(operation_type, 1)
            return balance_info.get('balance', 0) >= required_credits
        except:
            return False

    def wait_for_balance(self, min_balance: int, timeout_seconds: int = 120, poll_interval: float = 2.0) -> Dict[str, Any]:
        """
        Poll the balance endpoint until at least `min_balance` credits are available
        or until `timeout_seconds` elapse. Returns the final balance payload.
        """
        deadline = time.time() + timeout_seconds
        last_payload: Dict[str, Any] = {}
        while time.time() < deadline:
            last_payload = self.get_balance()
            if last_payload.get('balance', 0) >= min_balance:
                return last_payload
            time.sleep(poll_interval)
        raise NexusAPIError('Timed out waiting for balance update')


# Example usage
if __name__ == '__main__':
    # Initialize the API client
    api = NexusAPI(
        api_key='nxs_YOUR_API_KEY_HERE',
        base_url='http://localhost:3000'
    )
    
    try:
        # Check balance
        balance_info = api.get_balance()
        print(f"Current balance: {balance_info['balance']} credits")
        print(f"User: {balance_info['user']['email']}")
        
        # Check if we have enough credits
        if api.check_credits_before_operation(OperationType.STANDARD_WIPE):
            print("Sufficient credits for standard wipe")
            
            # Perform a standard wipe
            result = api.consume_credits(
                operation_type=OperationType.STANDARD_WIPE,
                device_serial='DELL-ABC123',
                device_model='Dell Optiplex 7090',
                metadata={
                    'location': 'Warehouse A',
                    'technician': 'John Doe'
                }
            )
            
            print(f"Operation started: {result['operationId']}")
            print(f"Credits consumed: {result['creditsConsumed']}")
            print(f"Remaining balance: {result['remainingBalance']}")
            
            # Simulate operation completion
            # In real usage, this would be called after the actual operation
            completion = api.complete_operation(
                operation_id=result['operationId'],
                status=OperationStatus.COMPLETED,
                metadata={'wipe_duration_seconds': 3600}
            )
            
            print(f"Operation completed: {completion['operation']['status']}")
        else:
            print("Insufficient credits for operation")
            # Initiate in-app purchase of credits (opens browser)
            checkout_url = api.initiate_credit_purchase('credits_100')
            print(f"Open this URL to buy credits: {checkout_url}")
            # Optionally wait for the new credits to appear (Stripe webhook + polling)
            try:
                updated = api.wait_for_balance(min_balance=1, timeout_seconds=120, poll_interval=3)
                print(f"New balance detected: {updated['balance']} credits")
            except NexusAPIError:
                print("Timed out waiting for credits; please try again later.")
            
    except InsufficientCreditsError as e:
        print(f"Not enough credits: {e}")
    except NexusAPIError as e:
        print(f"API error: {e}")
