# Crucible UI/UX Design Plan

## Executive Summary
This document outlines the comprehensive UI/UX redesign plan for Crucible, a PXE-boot testing and wiping solution for computer recycling operations. The software runs directly on diverse hardware (laptops/desktops) being tested after PXE booting into a custom Debian environment. The design prioritizes compatibility across varied hardware, speed of testing, and clear visual feedback for technicians processing enterprise equipment.

## Design Principles

### 1. **Hardware-Adaptive Design**
- Compatible with diverse screen resolutions (768p to 4K)
- Works with both mouse/keyboard and touchpad/touchscreen
- Responsive layouts that adapt to different aspect ratios
- Graceful degradation for older hardware/graphics

### 2. **Speed & Efficiency**
- Auto-advance through predictable test sequences
- Parallel test execution where hardware allows
- Minimal technician interaction required

### 3. **Visual Clarity**
- High contrast dark theme for consistency
- Color-coded status indicators (red/yellow/green)
- Readable fonts that scale with resolution
- Clear test progress and status visibility

### 4. **Deterministic Operations**
- Clear, linear workflow progression
- Minimal decision points for technicians
- Automatic error recovery where possible
- No unnecessary confirmations (wipe-all policy)

### 5. **Resilience & Reliability**
- Network-independent test execution
- Graceful handling of hardware variations
- Automatic retry mechanisms
- Clear error states with recovery guidance

## User Interface Architecture

### Component Hierarchy
```
┌─────────────────────────────────────┐
│      System Info & Asset Header     │
│    (Hardware ID, Asset Number)      │
├─────────────────────────────────────┤
│                                     │
│         Test Dashboard              │
│     (Test Grid & Progress)          │
│                                     │
├─────────────────────────────────────┤
│         Log Panel & Actions         │
│    (Live Logs & Test Controls)      │
└─────────────────────────────────────┘
```

### State Machine Flow
```
BOOT → HARDWARE_DETECTION → ASSET_ENTRY → PROFILE_SELECT → 
TEST_RUNNING → RESULTS_REVIEW → DRIVE_WIPE → ATTESTATION → SYNC
```

## Visual Design System

### Color Palette
```css
/* Primary Colors */
--crucible-bg-primary: #0a0a0a;        /* Deep black background */
--crucible-bg-secondary: #1a1a1a;      /* Raised surfaces */
--crucible-bg-tertiary: #2a2a2a;       /* Interactive elements */

/* Status Colors */
--status-idle: #6c757d;           /* Gray - waiting */
--status-running: #ffc107;        /* Amber - in progress */
--status-pass: #28a745;           /* Green - success */
--status-fail: #dc3545;           /* Red - failure */
--status-warning: #fd7e14;        /* Orange - attention needed */

/* Text Colors */
--text-primary: #ffffff;           /* Primary text */
--text-secondary: #b0b0b0;        /* Secondary text */
--text-muted: #6c757d;            /* Disabled/muted text */

/* Accent Colors */
--accent-primary: #007bff;        /* Primary actions */
--accent-secondary: #17a2b8;      /* Secondary actions */
--accent-danger: #dc3545;         /* Destructive actions */
```

### Typography
```css
/* Font Stack */
--font-primary: 'Segoe UI', 'Ubuntu', system-ui, sans-serif;
--font-mono: 'Consolas', 'Ubuntu Mono', monospace;

/* Size Scale (responsive) */
--text-xs: clamp(10px, 1vw, 12px);     /* Metadata, timestamps */
--text-sm: clamp(12px, 1.2vw, 14px);   /* Secondary information */
--text-base: clamp(14px, 1.5vw, 18px); /* Body text */
--text-lg: clamp(18px, 2vw, 24px);     /* Section headers */
--text-xl: clamp(24px, 2.5vw, 32px);   /* Page titles */
--text-2xl: clamp(32px, 3vw, 48px);    /* Status displays */
```

### Spacing System
```css
/* Consistent spacing scale */
--space-xs: 4px;
--space-sm: 8px;
--space-md: 16px;
--space-lg: 24px;
--space-xl: 32px;
--space-2xl: 48px;
```

## Component Specifications

### 1. Hardware-Adaptive Buttons
```css
.crucible-button {
    min-height: 44px;  /* Standard touch target */
    min-width: 120px;
    padding: 12px 20px;
    font-size: var(--text-base);
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.crucible-button-primary {
    background: var(--accent-primary);
    color: white;
}

.crucible-button-danger {
    background: var(--accent-danger);
    color: white;
}

/* Larger targets for touchscreen devices */
@media (pointer: coarse) {
    .crucible-button {
        min-height: 56px;
        padding: 16px 24px;
    }
}
```

### 2. Status Cards
```css
.status-card {
    background: var(--crucible-bg-secondary);
    border-radius: 8px;
    padding: 16px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.status-card.pass {
    border-color: var(--status-pass);
    box-shadow: 0 0 20px rgba(40, 167, 69, 0.3);
}

.status-card.fail {
    border-color: var(--status-fail);
    box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
}
```

### 3. Progress Indicators
```css
.progress-bar {
    height: 32px;
    background: var(--crucible-bg-tertiary);
    border-radius: 16px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    transition: width 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
```

## Screen Layouts

### 1. Hardware Detection Screen
- Auto-detection of system specifications
- Display of CPU, RAM, storage, network info
- Hardware compatibility warnings
- Auto-advance when detection complete

### 2. Asset Entry Screen
- Standard text input for asset number
- Barcode scanner support indicator
- Operator ID field
- Device condition checkboxes

### 3. Profile Selection Screen
- List of available test profiles
- Desktop/Laptop/Server profile presets
- Custom profile support
- Hardware-based profile recommendations

### 4. Test Execution Screen
- Grid view of all running tests
- Individual test progress bars
- Pass/fail indicators per test
- Expandable log viewer
- Stop/retry controls per test

### 5. Results Review Screen
- Overall device pass/fail status
- Detailed test results table
- Hardware specifications summary
- Failed test diagnostics
- Proceed to wipe or reject device

### 6. Drive Wipe Screen
- Auto-detection of all internal drives
- Wipe method selection (NIST 800-88 compliant)
- Parallel wipe progress bars
- Real-time throughput metrics
- No confirmation required (wipe-all policy)

## Interaction Patterns

### Input Methods
- **Mouse/Touchpad**: Primary navigation
- **Keyboard**: Shortcuts and data entry
- **Touch** (if available): Direct interaction
- **Barcode Scanner**: Asset number entry
- **Function Keys**: Quick actions (F1=Help, F5=Refresh, etc.)

### Feedback Mechanisms
- **Visual**: Color changes, animations
- **Haptic**: CSS transform feedback
- **Audio**: Optional success/failure sounds
- **Progress**: Real-time updates via WebSocket

### Error Handling
- **Inline Validation**: Immediate feedback on input
- **Error Toasts**: Non-blocking error messages
- **Recovery Prompts**: Clear next steps
- **Auto-Retry**: Configurable retry logic

## Responsive Breakpoints
```css
/* Legacy displays (1024x768) */
@media (max-width: 1024px) {
    /* Compact layout */
    /* Smaller margins/padding */
}

/* Standard laptop (1366x768 to 1920x1080) */
@media (min-width: 1025px) and (max-width: 1920px) {
    /* Default layout */
    /* Standard spacing */
}

/* High resolution (2K/4K displays) */
@media (min-width: 1921px) {
    /* Enhanced layout */
    /* Additional detail panels */
    /* Multi-column test results */
}
```

## Animation & Transitions

### Micro-interactions
```css
/* Button press feedback */
@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

/* Status pulse */
@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Progress sweep */
@keyframes progressSweep {
    0% { background-position: 0% 50%; }
    100% { background-position: 100% 50%; }
}
```

## Accessibility Considerations

### WCAG 2.1 AA Compliance
- **Color Contrast**: Minimum 4.5:1 for normal text, 3:1 for large text
- **Focus Indicators**: Visible focus states for keyboard navigation
- **Screen Reader Support**: ARIA labels and roles
- **Error Identification**: Clear error messages with suggestions

### Keyboard Navigation
- **Tab Order**: Logical flow through interface
- **Shortcuts**: Common actions (Enter to confirm, Esc to cancel)
- **Skip Links**: Jump to main content
- **Focus Trapping**: Modal dialogs contain focus

## Performance Targets

### Load Times
- **PXE Boot to UI**: < 30 seconds total
- **Screen Transitions**: < 200ms
- **Test Start**: < 5 seconds
- **API Responses**: < 500ms for local calls

### Resource Usage
- **Memory**: < 200MB for entire application
- **CPU**: < 5% idle, < 30% during UI operations
- **Network**: Localhost only, except for ERP sync
- **Storage**: RAM-backed, minimal disk I/O

## Implementation Phases

### Phase 1: Foundation (Week 1-2)
- Design system setup (colors, typography, spacing)
- Component library creation
- Base layout templates
- WebSocket infrastructure

### Phase 2: Core Screens (Week 2-3)
- Asset entry interface
- Profile selection
- Test execution view
- Results display

### Phase 3: Advanced Features (Week 3-4)
- Batch processing UI
- Drive wipe interface
- Error handling flows
- Performance optimizations

### Phase 4: Polish & Testing (Week 4-5)
- Animation refinements
- Cross-device testing
- Accessibility audit
- Performance tuning

## Success Metrics

### Efficiency Metrics
- **Boot to Testing**: < 45 seconds total
- **Asset Entry Time**: < 10 seconds manual, < 3 seconds barcode
- **Test Completion Rate**: > 95% without intervention
- **Devices Processed**: > 50 devices/day/technician

### Usability Metrics
- **Training Time**: < 30 minutes for new technicians
- **Error Rate**: < 2% technician errors
- **Task Completion**: > 98% successful workflows
- **Hardware Compatibility**: > 99% of x86_64 devices

## Risk Mitigation

### Technical Risks
- **Hardware Diversity**: Test on wide range of devices
- **Graphics Driver Issues**: Fallback to VESA/framebuffer
- **Network Boot Failures**: USB boot fallback option
- **Memory Constraints**: Support systems with 2GB+ RAM

### Operational Risks
- **Hardware Failures During Test**: Graceful error handling
- **Data Security**: Ensure complete wipe verification
- **Compliance**: NIST 800-88 adherence
- **Chain of Custody**: Complete audit trail

## Maintenance & Updates

### Version Control
- Semantic versioning for UI components
- Changelog for operator-visible changes
- Rollback capability for critical issues

### Documentation
- Component storybook
- Operator quick reference guide
- Administrator configuration guide
- Troubleshooting flowcharts

## Appendices

### A. Competitor Analysis
- Review of existing ITAD interfaces
- Best practices from kiosk systems
- Industrial UI patterns

### B. User Research
- Operator interviews
- Workflow observations
- Pain point analysis
- Feature requests

### C. Technical Specifications
- Browser requirements
- Hardware requirements
- Network configuration
- Security considerations
