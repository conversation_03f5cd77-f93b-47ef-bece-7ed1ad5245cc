# Nexus Communication & Client Library

This document describes how **PXE-booted agents** communicate with the single "Nexus" hub machine and how to work offline while prototyping.

> All paths are relative to the project root (`/agent`, `/server`, `docs/…`).

---

## 1. Topology Overview

```
┌────────────┐ PXE boot   eth        ┌────────────┐
│  Laptop #1 │───────────┐          │            │
├────────────┤           ├────────▶ │   Nexus    │
│  Laptop #N │───────────┘          │  FastAPI   │
└────────────┘                      └────────────┘
```

* A single **Nexus** PC (static IP) is connected to a dumb switch.
* Each wiped PC boots a custom Linux Mint image which auto-starts `agent/main.py` (GUI or CLI).
* When tests finish the agent posts JSON payloads to the Nexus REST API.

---

## 2. `agent.nexus_client.NexusClient`

Location: `agent/nexus_client.py`

Key points:

| Feature | Notes |
|---------|-------|
| Default URL | `http://127.0.0.1:8000` or value of `$NEXUS_URL` |
| Queue Dir   | `~/.crucible_nexus_queue` or `$NEXUS_QUEUE_DIR` |
| Sync & Async| `post_result()` and `post_result_async()` |
| Auto flush  | When a request finally succeeds the client re-sends any files in the queue (FIFO) |

Example (sync):
```python
from agent.nexus_client import NexusClient
client = NexusClient()          # honours $NEXUS_URL
client.post_result(result_dict) # returns (ok: bool, message: str)
```

Example (async):
```python
client = NexusClient("http://********:8000")
await client.post_result_async(result_dict)
```

### Spool File Format
Plain JSON named `<epoch_ms>.json` inside the queue dir. Safe to copy for manual inspection.

---

## 3. Server (Nexus) API

File: `server/main.py`

| Method | Path          | Purpose |
|--------|---------------|---------|
| GET    | `/`           | Health ping `{ "msg": "running" }` |
| POST   | `/result`     | Accept single `TestResult` JSON |
| GET    | `/results`    | Dump all results (demo only) |
| GET    | `/ui`         | Minimal HTML dashboard |

Run locally:
```bash
uvicorn server.main:app --host 0.0.0.0 --port 8000 --reload
```

---

## 4. Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `NEXUS_URL`          | `http://127.0.0.1:8000` | Base URL used by agents |
| `NEXUS_QUEUE_DIR`    | `~/.crucible_nexus_queue`  | Override spool directory |

Set them in the PXE kernel command-line or inside the Mint image’s `/etc/environment`.

---

## 5. Integration Points

### CLI Agent (`agent/agent.py`)
* Function `send_result()` now wraps `NexusClient.post_result_async`.
* Backup to a local JSON file if even the spool write failed (extremely rare).

### GUI Agent (`agent/gui_agent.py`)
* Same refactor – UI remains unchanged; queueing is automatic.

### Tk Drive-wipe GUI (`agent/gui/drive_wipe_gui.py`)
* Collected wipe results can call `NexusClient` the same way (TODO).

---

## 6. Offline Workflow

1. Boot the test machine without the Nexus PC connected.
2. Agents run, finish, and **quietly queue** each result.
3. When the switch/Nexus comes back online any new successful POST *also triggers a queue flush* – the back-catalogue is delivered automatically.

No operator action is required and no data is lost.

---

## 7. Roadmap / Next Steps

* Replace `TEST_RESULTS` list with PostgreSQL via SQLModel.
* Add `/api/v1/...` versioned routes while keeping current endpoints for compatibility.
* JWT or API-Key auth header – each wipe station can have its own key.
* Optional mDNS discovery (`_nexus._tcp`) to remove static IP requirement.

---

_Last updated: 2025-06-11_
