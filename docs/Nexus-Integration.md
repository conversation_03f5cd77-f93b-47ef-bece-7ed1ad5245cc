# Nexus Credit System Integration Guide

This guide describes how to integrate the Nexus desktop software with the website backend to authenticate using API keys, check credit balances, consume credits during device operations, complete or refund operations, and initiate in-app purchases via Stripe Checkout.

- API endpoints live under the Next.js App Router.
- API key Bearer auth is used for desktop integration.
- Audit logs are recorded for key operations.

Key files for reference:
- `src/app/api/credits/balance/route.js`
- `src/app/api/credits/consume/route.js`
- `src/app/api/checkout/route.js`
- `src/app/api/licenses/status/route.js`
- `src/app/api/licenses/activate/route.js`
- `lib/products.js` (catalog + operation costs)
- `docs/nexus-sdk.py` (Python SDK example)

---

## Authentication

Include an API key in the Authorization header for all desktop app requests.

- Header:
  ```
  Authorization: Bearer nxs_YOUR_API_KEY
  Content-Type: application/json
  ```
- 401 Unauthorized if the key is invalid, inactive, or expired.
- Manage keys in the website Account page.

---

## Operation Costs

Defined in `lib/products.js` and used by `POST /api/credits/consume`:
- STANDARD_WIPE: 1 credit
- WIPE_WITH_DIAGNOSTICS: 2 credits
- DIAGNOSTICS_ONLY: 1 credit

Credits are consumed FIFO by expiry date, then by oldest purchase.

---

## GET /api/credits/balance

Returns current credit balance, subscription summary, and license summary for the API-key user.

- Auth: Bearer API key
- Response 200:
  ```json
  {
    "balance": 123,
    "subscription": {
      "planType": "pro",
      "workstations": 5,
      "currentPeriodEnd": "2025-09-24T00:00:00.000Z"
    },
    "licenseSummary": {
      "hasServerLicense": true,
      "totalServerCount": 2,
      "maintenanceActive": true,
      "maintenanceExpiry": "2026-01-01T00:00:00.000Z"
    },
    "licenses": [
      {
        "id": "lic_1",
        "licenseType": "INDUSTRIAL_SERVER",
        "serverCount": 2,
        "maintenanceExpiry": null,
        "activatedAt": "2025-08-01T12:00:00.000Z"
      },
      {
        "id": "lic_2",
        "licenseType": "INDUSTRIAL_MAINTENANCE",
        "serverCount": 1,
        "maintenanceExpiry": "2026-01-01T00:00:00.000Z",
        "activatedAt": null
      }
    ],
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "companyName": "Acme ITAD"
    }
  }
  ```
- Errors: 401 Unauthorized, 500 Internal server error

---

## POST /api/credits/consume

Atomically starts an operation and consumes required credits.

- Auth: Bearer API key
- Request body:
  ```json
  {
    "operationType": "STANDARD_WIPE",
    "deviceSerial": "ABC123",
    "deviceModel": "Dell Optiplex 7090",
    "metadata": {"location": "Warehouse A", "technician": "Jane"}
  }
  ```
- Response 200:
  ```json
  {
    "success": true,
    "operationId": "op_uuid",
    "creditsConsumed": 1,
    "remainingBalance": 122,
    "operation": {
      "id": "op_uuid",
      "type": "STANDARD_WIPE",
      "status": "IN_PROGRESS",
      "deviceSerial": "ABC123",
      "deviceModel": "Dell Optiplex 7090",
      "startedAt": "2025-08-24T14:20:01.123Z"
    }
  }
  ```
- Errors: 400 Invalid operation type, 401 Unauthorized, 402 Payment Required (insufficient credits), 500 Internal server error

Notes: Logs usage with IP + user-agent for audit trail.

---

## PATCH /api/credits/consume

Completes, fails, or cancels an operation. Fail/cancel triggers a credit refund batch.

- Auth: Bearer API key
- Complete request:
  ```json
  {
    "operationId": "op_uuid",
    "status": "COMPLETED",
    "metadata": {"wipe_duration_seconds": 3600}
  }
  ```
- Fail/cancel request (refunded):
  ```json
  {
    "operationId": "op_uuid",
    "status": "FAILED",
    "errorMessage": "Drive write error",
    "metadata": {"stage": "verify"}
  }
  ```
- Response 200:
  ```json
  {
    "success": true,
    "operation": {
      "id": "op_uuid",
      "status": "FAILED",
      "creditsUsed": 1,
      "refunded": true
    }
  }
  ```
- Errors: 400 Invalid status or missing fields, 401 Unauthorized, 404 Operation not found/forbidden, 500 Internal server error

---

## POST /api/checkout (In-App Purchase)

Creates a Stripe Checkout session for credits/subscriptions/licenses. Supports cookie auth (web) or Bearer API key (desktop). For desktop, use API key.

- Auth: Bearer API key recommended
- Request example (credits):
  ```json
  {
    "type": "credits",
    "productId": "credits_100",
    "successUrl": "https://your-site.com/purchase/success",
    "cancelUrl": "https://your-site.com/purchase/cancel"
  }
  ```
- Response 200:
  ```json
  { "sessionId": "cs_test_123", "url": "https://checkout.stripe.com/c/session_..." }
  ```
- Errors: 400 Invalid type/product, 401 Authentication required, 500 Internal server error

Behavior:
- For API-key auth, logs `checkout_initiated` with metadata.
- Stripe session metadata includes `source` and `apiKeyId` for attribution.
- After successful payment, credits are added via webhook processing.
- The app should poll `GET /api/credits/balance` to refresh balance.

Products (`lib/products.js`):
- Credit packs: `credits_100`, `credits_500`, `credits_1000`
- Subscription: `pro`, `business` (enterprise: contact sales)
- License: `industrial_server`, `maintenance`

---

## GET /api/licenses/status

Provides license summary and full list for the authenticated account.

- Auth: Bearer API key
- Response 200:
  ```json
  {
    "licenseSummary": {
      "hasServerLicense": true,
      "totalServerCount": 2,
      "maintenanceActive": true,
      "maintenanceExpiry": "2026-01-01T00:00:00.000Z"
    },
    "licenses": [
      {
        "id": "lic_1",
        "licenseType": "INDUSTRIAL_SERVER",
        "serverCount": 2,
        "maintenanceExpiry": null,
        "activatedAt": "2025-08-01T12:00:00.000Z",
        "createdAt": "2025-08-01T10:00:00.000Z"
      }
    ],
    "user": {"id": "uuid", "email": "<EMAIL>", "companyName": "Acme ITAD"}
  }
  ```
- Errors: 401 Unauthorized, 500 Internal server error

## POST /api/licenses/activate

Activates a license key on the authenticated account. Idempotent; repeated calls return current state.

- Auth: Bearer API key
- Request body:
  ```json
  { "licenseKey": "ABCD-EF12-GH34-IJ56" }
  ```
- Response 200:
  ```json
  {
    "license": {
      "id": "lic_1",
      "licenseKey": "ABCD-EF12-GH34-IJ56",
      "licenseType": "INDUSTRIAL_SERVER",
      "serverCount": 1,
      "maintenanceExpiry": null,
      "activatedAt": "2025-08-01T12:00:00.000Z",
      "createdAt": "2025-08-01T10:00:00.000Z"
    }
  }
  ```
- Errors: 400 Missing/invalid licenseKey, 401 Unauthorized, 404 License not found, 500 Internal server error

---

## Recommended Client Flow

1. Store API key securely in app settings (mask in logs).
2. On dashboard, call `GET /api/credits/balance` and show `balance` and subscription.
3. Before operation, ensure sufficient credits; start via `POST /api/credits/consume`.
4. After operation, `PATCH` with final status. On FAILURE/CANCELED, server refunds credits.
5. On 402 (insufficient credits), create Checkout via `POST /api/checkout`, open returned URL, then poll balance and retry.

---

## Error Handling

- 401: Prompt for new/rotated API key.
- 402: Offer purchase flow; retry after credits added.
- 404 (PATCH): Verify you used the `operationId` from the POST response.
- 5xx: Retry with exponential backoff.

---

## Python SDK Usage (`docs/nexus-sdk.py`)

```python
from nexus_sdk import NexusAPI, OperationType, OperationStatus
api = NexusAPI(api_key='nxs_YOUR_KEY', base_url='https://your-site.com')

# Get balance
balance = api.get_balance()

# Start operation
result = api.consume_credits(
    operation_type=OperationType.STANDARD_WIPE,
    device_serial='ABC123',
    device_model='Dell Optiplex 7090'
)
op_id = result['operationId']

# Complete operation
api.complete_operation(
    operation_id=op_id,
    status=OperationStatus.COMPLETED,
    metadata={'wipe_duration_seconds': 3600}
)

# License status and activation
licenses = api.get_license_status()
print('License summary:', licenses.get('licenseSummary'))
# Optionally activate a key (from purchase email/portal)
# api.activate_license('ABCD-EF12-GH34-IJ56')

# In-app purchase of credits
checkout_url = api.initiate_credit_purchase('credits_100')
# Open checkout_url in browser, then poll api.get_balance()
```

---

## Security Best Practices

- Keep API keys secret, encrypted at rest.
- Mask keys in logs (e.g., show last 4 chars only).
- Rotate keys periodically and on compromise.
- Consider server-side rate limiting and IP allowlisting.
