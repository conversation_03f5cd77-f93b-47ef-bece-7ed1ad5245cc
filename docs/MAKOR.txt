Introduction
The Makor ERP offers multiple JSON-based API’s for integration with third-party hard drive diagnostics and wiping services. It can process XML reports sent to it, update the Makor ERP hard drive entries with successful and failed wipe statuses, and give a list of wipes assigned to hard drives. Makor ERP can also be set up to POST HDD wiping instructions assigned via Makor ERP to a service endpoint provided by the third party.
All Diagnostics API calls are made to http://{serviceURL}/api/diagnostics/{serviceName}. For example, a report would be sent to http://MRMServices/api/diagnostics/report if the service is hosted on http://MRMServices.
Basic authorization headers with an MRM username and password must be included. Typically, this will involve creating a special MRM user whose permissions exclusively allow API communication, but no conventional access to the MRM system. 
Basic authorization headers are formatted as follows:
Authorization: basic [EncodedString]
Where [EncodedString] is “username:password” (e.g. “JohnDoe:password100”) encoded in Base64 (e.g. “Sm9obkRvZTpwYXNzd29yZDEwMA==” for the previous.) Most modern programming libraries have methods to encode a string to or from Base64, and for testing purposes, https://base64encode.org can be used.

Audit Report API
This API is used to send Makor ERP detailed audit information about the unit.
The reports are expected to be in XML format. See below for XML format details. To ensure data integrity, the report must be sent as a UTF-8 or UTF-16 string encoded in Base64. UTF-8 is preferred, but UTF-16 can be used if necessary. The endpoint is a POST to http://{services}/api/diagnostics/report.
Reports can be sent using either the Asset ID (the unique identifier generated for the Unit by Makor ERP) or the unit’s serial number, as the identifying parameter.
Example JSON (Asset ID)
{
	"asset_id": "12345678",
	"asset_report": {
		"report": "PHJvb3Q...=="
	}
}

Example JSON (Serial Number)
{
	"serial_number": "A1B2C3D4",
	"asset_report": {
		"report": "PHJvb3Q...=="
	}
}
A 200 will be returned for a successfully added diagnostics entry. A 400 error indicates that the Makor ERP system received the message, but was not able to process the report. A message will be sent in the response header along with the 400 error to indicate the issue.
Report XML Format
XML Hierarchy is as follows: root/audit/components/component
The first component node must be named “System” and must contain manufacturer, model, and serial. There can only be one “System” component per report.  Beyond that, the exact configuration of components is highly flexible. If your software provides a component or a component detail not listed below, you can add it as a new component or sub-component node with an appropriate name, following the pattern below of nesting “component” child nodes in “components” nodes.
XML Report Sample	

<root>
	<audit>
		<components name="System">
			<component name="Manufacturer" type="string">TOSHIBA</component>
			<component name="Model" type="string">PORTEGE Z930</component>
			<component name="Serial" type="string">ZC097826H</component>
			<component name="AssetTag" type="string"/>
			<component name="ChassisType" type="string">Notebook</component>
		</components>
		<components name="Processors">
			<components name="Processor">
				<component name="Qty" type="string">1</component>
				<component name="Manufacturer" type="string">Intel</component>
				<component name="Model" type="string">3427U</component>
				<component name="SerialNumber" type="string">BFEBFBFF000306A9</component>
				<component name="Speed" type="string">1801</component>
				<component name="Cores" type="string">2</component>
				<component name="Type" type="string">Core i5</component>
			</components>
		</components>
		<components name="Memorys">
			<components name="Memory">
				<component name="Qty" type="string">1</component>
				<component name="Manufacturer" type="string">Samsung</component>
				<component name="Model" type="string">M471B5773CHS-CK0</component>
				<component name="SerialNumber" type="string">00000000</component>
				<component name="Speed" type="string">1600</component>
				<component name="Size" type="string">2.00 GB</component>
			</components>
			<components name="Memory">
				<component name="Qty" type="string">1</component>
				<component name="Manufacturer" type="string">Hynix/Hyundai</component>
				<component name="Model" type="string">HMT325S6CFR8C-PB</component>
				<component name="SerialNumber" type="string">2D82407F</component>
				<component name="Speed" type="string">1600</component>
				<component name="Size" type="string">2.00 GB</component>
			</components>
		</components>
		<components name="Storage_Controllers">
			<components name="Storage_Controller">
				<component name="Description" type="string">Extreme Protocol AHCI SATA Controller</component>
			</components>
		</components>
		<components name="Network_Devices">
			<components name="Network_Device">
				<component name="Description" type="string">Intel(R) 82579LM Gigabit Network Connection</component>
			</components>
			<components name="Network_Device">
				<component name="Description" type="string">Intel(R) Centrino(R) Advanced-N 6235</component>
			</components>
		</components>
		<components name="USB_Controllers">
			<components name="USB_Controller">
				<component name="Description" type="string">Intel(R) 7 Series/C216 Chipset Family USB Enhanced Host Controller</component>
			</components>
			<components name="USB_Controller">
				<component name="Description" type="string">Intel(R) USB 3.0 eXtensible Host Controller</component>
			</components>
			<components name="USB_Controller">
				<component name="Description" type="string">Intel(R) 7 Series/C216 Chipset Family USB Enhanced Host Controller</component>
			</components>
		</components>
		<components name="Multimedias">
			<components name="Multimedia">
				<component name="Description" type="string">High Definition Audio Controller</component>
			</components>
		</components>
		<components name="Storages">
			<components name="Storage">
				<component name="Manufacturer" type="string">TOSHIBA</component>
				<component name="Model" type="string">THNSNF128GMCS</component>
				<component name="SerialNumber" type="string">X26S1051T67Y</component>
				<component name="Size" type="string">128.04 GB</component>
			</components>
		</components>
	</audit>
</root>


Sample Code (C#)
string JSONString = BuildJSONString();
//The BuildJSONString() method should build one of the above strings from the diagnostics data, and transfer it as a JSON string.
HttpClient client = new HttpClient();
//The HttpClient class generates an object for communicating over Http. Note that unlike most objects that implement IDisposable, a “using” for HttpClient is not typically optimal.
Uri u = new Uri(GetServerAddress());
//GetServerAddress() should return a string, formatted like “http://www.example.com”. This string should be configurable.
client.BaseAddress = u;
HttpContent JobSpecContent = new StringContent(JSONString);
JobSpecContent.Headers.ContentType.MediaType = "application/JSON";
Task<HttpResponseMessage> responseSpec = client.PostAsync("api/diagnostics/report", JobSpecContent); 
//This commands a post to the site defined by GetServerAddress(), and its endpoint “api/diagnostics/report”. 
try
{
	while (!responseSpec.IsCompleted)
	{
		responseSpec.Wait();
	}
//This while block simply waits for a response from the system to actually arrive.
	Debug.WriteLine(responseSpec.Result, "Request Status");
//Replace the above with whatever code is appropriate to record the request status to your own logs.
}
catch (System.Net.WebException exc)
{
	//Log exception-based errors here.
}

Update HDD Status API
This call is used to update Makor ERP with the wipe status of a HDD. The endpoint is a POST to http://{services}/api/diagnostics/finished. A single hard drive, or multiple hard drives in a batch, can be updated. The HDD serial number should be used as the unique identifier for the drive. The JSON below would mark hard drives with serial numbers “8wjfcd1” and “6e4sruw” as successfully wiped, and mark the hard drive with serial number “abda632” as failed.

[  
   {  
      "serial":"8wjfcd1",
      "success":true
   },
   {  
      "serial":"abda632",
      "success":false
   },
   {  
      "serial":"6e4sruw",
      "success":true
   }
]

Progress Update
This call is used to update the progress of an incomplete HDD. The endpoint is a POST to http://{services}/api/diagnostics/progress. A single hard drive, or multiple hard drives in a batch, can be updated. The HDD serial number should be used as the unique identifier for the drive. The JSON below would mark hard drives with serial numbers “8wjfcd1” “abda632”, and “6e4sruw” as being 44% wiped, 51% wiped, and 11% wiped, respectively.

[  
   {  
      "serial":"8wjfcd1",
      "progress":44
   },
   {  
      "serial":"abda632",
      "progress":51
   },
   {  
      "serial":"6e4sruw",
      "progress":11
   }
]

POST Wipe Instructions API
When configured, this API call is performed by Makor ERP to tell the third-party diagnostics tool the wipe instruction that was selected for a HDD via the Makor ERP interface. Makor ERP will POST to a configured API endpoint, in the following format:

 [
  {
    "job_spec": {
      "standard": "dod",
      "serial": "ABCD1234"
    }
  },
  {
    "job_spec": {
      "standard": "dod_ece",
      "serial": "AAAA1234"
    }
  }
]
The values currently supported by Makor ERP are as follows:
“single_pass” (1 pass)
“dod” (DoD 5220.22-M Wipe Method, 3 pass)
“dod_ece” (DoD 5220.22-M ECE Wipe Method, 7 pass)
“hmg_lower” (HMG IS5 Baseline)
“hmg_higher” (HMG IS5 Enhanced)
Get Wipe Instructions API
When 3rd party endpoint is not available for Makor ERP to post wipe instructions, this API call can be used by the 3rd party diagnostics software to request the wipe instruction that was selected for a HDD via Makor ERP interface. The diagnostics software can request wipe commands from the Makor ERP through a GET command to any of the following endpoints. The format of the response will be the same as the one defined in “POST Wipe Instructions API” above.

http://{services}/api/diagnostics/getwipe/assetsn?Serial=abcd gets all wipes for the asset(s) with serial # “abcd”
http://{services}/api/diagnostics/getwipe/assetid?AssetID=12345 gets all wipes for drives associated to asset with Makor asset ID 1234.
http://{services}/api/diagnostics/getwipe/drivesn?Serial=8wjfcd1 gets the specific wipe for hard drive 8wjfcd1.
http://{services}/api/diagnostics/getwipe/lotnumber?LotNumber=11000 gets all wipes assigned in lot #11000.
