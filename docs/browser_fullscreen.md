Use Chrome’s built-in command-line flag.

On Linux you can launch Chrome / Chromium like this:

bash
google-chrome --start-fullscreen http://your-app-url
# or
chromium-browser --start-fullscreen http://your-app-url
• --start-fullscreen opens the window exactly as if the user had pressed F11 (toolbars and tabs are hidden, ESC exits full-screen).

• If you want a true kiosk window that the user cannot leave with F11/ESC, replace the flag with --kiosk.

You can put the command in:

A shell script that you invoke from your application.
A .desktop file if you want a launcher icon.
Selenium / Playwright options (e.g. options.add_argument("--start-fullscreen")).
Nothing needs to change in your Python/Tk code—just start Chrome with the flag when you deploy the app on the Linux kiosk.