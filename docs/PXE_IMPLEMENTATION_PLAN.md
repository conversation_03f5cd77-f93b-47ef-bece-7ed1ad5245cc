# Crucible Nexus PXE Boot Implementation Plan

This document outlines the strategy for deploying the Crucible Nexus diagnostics platform via PXE boot for the computer recycling operation.

## Overview

The implementation will allow technicians to boot recycled computers directly from the network, running the Crucible Nexus diagnostics suite without installing any software on the target machines. This approach maximizes efficiency in a high-volume computer recycling environment.

## 1. Base Linux Distribution

**Alpine Linux** will serve as our foundation due to:
- Minimal footprint (~130MB)
- RAM-based operation (no disk access needed)
- Fast boot time
- Low resource requirements
- Simple package management

## 2. Software Stack Requirements

The Crucible Nexus platform requires:

| Component | Purpose |
|-----------|---------|
| Python 3.9+ | Runtime environment |
| Tkinter | GUI framework |
| psutil | System information collection |
| httpx | API communication |
| FastAPI | Server-side REST API |
| X11/Wayland | Display server |

## 3. PXE Infrastructure Components

### 3.1 DHCP Server

The DHCP server will:
- Assign IP addresses to booting clients
- Point clients to the TFTP server 
- Specify the boot file to download

Example DHCP configuration:
```
subnet *********** netmask ************* {
  range ************0 *************;
  option routers ***********;
  option domain-name-servers ***********;
  
  # PXE boot settings
  next-server ************;  # TFTP server IP
  filename "pxelinux.0";     # Initial boot file
}
```

### 3.2 TFTP Server

The TFTP server will provide:
- Initial boot loader
- Linux kernel
- Initial RAM disk (initramfs)
- Boot configuration

Example directory structure:
```
/tftpboot/
├── pxelinux.0
├── pxelinux.cfg/
│   └── default
├── vmlinuz-alpine
└── initramfs-alpine
```

### 3.3 HTTP/NFS Server

The HTTP or NFS server will serve:
- Alpine Linux rootfs
- Crucible Nexus application code
- Any additional resources

## 4. Boot Process Flow

1. Target computer boots and requests an IP via DHCP
2. DHCP server responds with IP and points to TFTP server
3. Client downloads and executes PXE bootloader
4. Bootloader loads kernel and initramfs from TFTP
5. Alpine Linux boots into RAM
6. Init system starts X11/display server
7. Automatic login occurs
8. Crucible Nexus application launches in fullscreen mode
9. Technician performs diagnostics
10. Results are sent to central server
11. Machine reboots for next test cycle

## 5. Server Component Enhancement

The current server component (`c:\DEV\Crucible\server`) will be expanded to:

1. **Data Storage**:
   - Store diagnostic test results
   - Track asset history and status
   - Maintain technician activity logs

2. **API Endpoints**:
   - `/results` - Accept and store test results
   - `/assets` - Asset management
   - `/reports` - Generate performance reports

3. **Integration**:
   - MAKOR ERP system integration 
   - Asset tracking database connectors

4. **Web Dashboard**:
   - View test results in real-time
   - Generate reports and analytics
   - Manage assets and inventory

## 6. Implementation Steps

### 6.1 Prepare Alpine Linux Environment

```bash
# Create a working directory
mkdir -p crucible-pxe/alpine
cd crucible-pxe

# Download Alpine and extract for customization
wget https://dl-cdn.alpinelinux.org/alpine/latest-stable/releases/x86_64/alpine-minirootfs-3.17.0-x86_64.tar.gz
mkdir rootfs
tar -xzf alpine-minirootfs-3.17.0-x86_64.tar.gz -C rootfs
```

### 6.2 Create Installation Script

```bash
cat > rootfs/setup-crucible.sh << 'EOF'
#!/bin/sh
# Install dependencies
apk update
apk add python3 py3-pip xorg-server lightdm py3-tk
apk add psutil httpx

# Set up auto-login
mkdir -p /etc/lightdm
cat > /etc/lightdm/lightdm.conf << 'INNER'
[SeatDefaults]
autologin-user=crucible
autologin-user-timeout=0
INNER

# Create Crucible user
adduser -D -h /home/<USER>

# Add autostart for Crucible Nexus
mkdir -p /home/<USER>/.config/autostart
cat > /home/<USER>/.config/autostart/crucible.desktop << 'INNER'
[Desktop Entry]
Type=Application
Name=CrucibleNexus
Exec=python3 /opt/crucible/agent/main.py
Terminal=false
INNER

# Create Crucible directory
mkdir -p /opt/crucible
EOF
chmod +x rootfs/setup-crucible.sh
```

### 6.3 Package Crucible Nexus Application

```bash
# Copy application code
cp -r /path/to/Crucible/agent rootfs/opt/crucible/
cp -r /path/to/Crucible/server rootfs/opt/crucible/

# Ensure server URL is configured correctly
sed -i 's/localhost:8000/central-server-ip:8000/' rootfs/opt/crucible/agent/config.py
```

### 6.4 Create PXE Boot Files

```bash
# PXE Linux default configuration
cat > pxelinux.cfg/default << 'EOF'
DEFAULT cruciblenexus
TIMEOUT 50
PROMPT 0

LABEL cruciblenexus
KERNEL vmlinuz-alpine
APPEND initrd=initramfs-alpine alpine_repo=http://dl-cdn.alpinelinux.org/alpine/latest-stable/main modloop=http://server-ip/cruciblenexus/modloop quiet
EOF
```

### 6.5 Set Up DHCP, TFTP, and HTTP Servers

- Install and configure ISC DHCP server
- Install and configure a TFTP server
- Configure an HTTP server for additional files

## 7. Testing and Deployment

1. Test boot process in a virtual machine
2. Verify application launches correctly
3. Confirm test results are sent to the server
4. Deploy to a limited set of physical machines
5. Roll out to production environment

## 8. Maintenance Procedures

1. **Updates**:
   - Generate new PXE images when updating Crucible Nexus
   - Document version numbering and update procedures

2. **Troubleshooting**:
   - Network boot failure procedures
   - Application error handling

3. **Monitoring**:
   - Track successful boots and test completions
   - Monitor server load and response times

## 9. Future Enhancements

- **Offline Mode**: Capability to operate without server connection
- **Multiple Test Profiles**: Different profiles for various hardware types
- **Secure Wipe Integration**: Expand to support secure data wiping
- **Hardware-Specific Tests**: Tests tailored to specific brands/models

## 10. Resources and References

- Alpine Linux: [https://alpinelinux.org/](https://alpinelinux.org/)
- PXE Boot documentation: [https://wiki.archlinux.org/title/Preboot_Execution_Environment](https://wiki.archlinux.org/title/Preboot_Execution_Environment)
- Python Tkinter in X11: [https://python-gtk-3-tutorial.readthedocs.io/](https://python-gtk-3-tutorial.readthedocs.io/)
