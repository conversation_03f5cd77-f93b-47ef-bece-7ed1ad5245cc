# Authentication Implementation Plan

This plan replaces guest mode with real customer authentication and an account area for managing profile, credits, subscriptions, licenses, and transactions.

## Phase 1 — Minimal password auth (MVP)

- **Env setup**
  - Add `JWT_SECRET=<strong_random>` to `.env`.

- **API routes**
  - `src/app/api/auth/register/route.js`
    - Create user via Prisma with `hashPassword()` from `lib/auth.js`.
    - On success: set `auth-token` cookie using `generateToken(user.id)`.
  - `src/app/api/auth/login/route.js`
    - Verify credentials with `verifyPassword()`.
    - On success: set `auth-token` cookie.
  - `src/app/api/auth/logout/route.js`
    - Clear `auth-token` cookie.
  - `src/app/api/auth/me/route.js`
    - Return current user using `getUserFromToken()`.

- **UI pages**
  - `src/app/register/page.js` and `src/app/login/page.js`
    - Simple forms posting to the above routes.

- **Account area (initial)**
  - `src/app/account/page.js`
    - Server component fetching authenticated user and rendering:
      - Profile info (email, name, company)
      - Credits balance (sum of `Credit.remainingAmount`)
      - Active subscription summary
      - License summary
      - Recent `Transaction` list

- **Protect routes**
  - `src/middleware.js`
    - Enforce auth for `/account` (and other private paths later).
    - Redirect to `/login` if `auth-token` missing/invalid.

- **Checkout integration**
  - Update `src/app/api/checkout/route.js`:
    - Replace guest mode by calling `requireAuth(request)` to get the user.
    - Set Stripe Checkout metadata `userId` from the token.
    - Optional dev fallback: allow `TEST_USER_ID` only when `ALLOW_TEST_USER_ID=true` in `.env`.

- **Cookie security**
  - Set cookie flags appropriately:
    - HttpOnly
    - SameSite=Lax
    - Secure in production
    - 7-day expiry (already encoded in JWT via `generateToken()`)

- **Docs**
  - Update `README.md` with endpoints, env variables, and testing instructions.

## Phase 2 — Stripe customer linkage and UX polish

- **Schema update**
  - In `prisma/schema.prisma` add `stripeCustomerId String?` to `User`.
  - Run `npx prisma migrate dev --name add_stripe_customer_id`.

- **Customer creation/linking**
  - On first checkout, if `user.stripeCustomerId` is null:
    - Create Stripe Customer using `user.email` and minimal metadata.
    - Save `stripeCustomerId` on the user.
    - Pass `customer` to `stripe.checkout.sessions.create()` for subscriptions and invoices to attach properly.

- **Account dashboard enhancements**
  - Show detailed subscription data (status, current period, cancellation state).
  - Show credit packs with remaining amounts and expiry dates.
  - Show license keys, maintenance expiry, server count.
  - Add actions:
    - “Buy credits” (links to `/pricing` and preserves return to `/account`).
    - “Manage subscription” (Stripe Customer Portal, optional; see below).

- **Webhook logging and robustness**
  - Reduce noise from unhandled events; only log interesting ones at info/warn.
  - Improve error messages and add defensive checks for missing entities.

## Phase 3 — Quality, recovery, and productionization

- **Session lifecycle**
  - Optional refresh token or re-issue tokens on activity.
  - Always provide logout and handle expired tokens gracefully.

- **Rate limiting**
  - Add basic IP throttle for `/api/auth/login` and `/api/auth/register`.

- **Password reset and email verification (optional)**
  - Implement reset token flow or signed link delivery via email service.
  - Add verified flag on `User` if needed.

- **Stripe Customer Portal (optional)**
  - `src/app/api/stripe/portal/route.js` to create a billing portal session using `stripeCustomerId`.
  - Link from `/account` to allow users to manage payment methods and subscriptions.

- **Testing**
  - Integration tests for:
    - `register`, `login`, `logout`, `me` routes
    - `/account` access control (middleware)
    - Checkout when authenticated and webhook processing with `metadata.userId`
  - Mock Stripe SDK where appropriate.

## Implementation order (suggested)

1. Auth APIs + login/register pages + middleware + basic `/account` page.
2. Switch Checkout to `requireAuth()`; keep optional dev fallback gated by env.
3. Add `stripeCustomerId` to `User` and link/create customer on first checkout.
4. Dashboard polish and cleaner webhook logs.
5. Optional: Customer Portal, password reset, tests.

## Acceptance criteria

- Users can register, log in, and log out.
- Authenticated users can view `/account` with their profile, credits, subscriptions, licenses, and transactions.
- Checkout uses authenticated `userId` in Stripe metadata; webhooks create records for the correct user.
- (Phase 2) Users are linked to a Stripe customer; subscriptions and invoices are tied to that customer.
- Security: HttpOnly cookie, SameSite=Lax, Secure in production; basic rate limiting in place by Phase 3.
