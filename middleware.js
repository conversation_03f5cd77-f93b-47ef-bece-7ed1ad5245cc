import { NextResponse } from 'next/server';

export function middleware(request) {
  const { pathname, search } = request.nextUrl;

  // Protect /account routes
  if (pathname.startsWith('/account')) {
    const token = request.cookies.get('auth-token');
    if (!token) {
      const url = new URL('/login', request.url);
      url.searchParams.set('next', pathname + (search || ''));
      return NextResponse.redirect(url);
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/account/:path*'],
};
