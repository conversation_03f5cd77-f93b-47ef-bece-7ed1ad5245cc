# Nexus Suite Overview

This document provides a high-level, end-to-end overview of the Nexus Suite and how its three core projects work together:

- Nexus — the central hub/server that orchestrates workflows, serves boot artifacts, and records results.
- Crucible — the edge agent that runs on the custom Linux, tests hardware, and securely wipes drives.
- Nexus Foundry — the build system that produces the custom Linux images consumed by Nexus and booted by clients via PXE.

---

## Architecture at a Glance

```text
+------------------+            Publish build artifacts             +-------------------+
|  Nexus Foundry   |  ------------------------------------------>  |       Nexus       |
|  (Image Builder) |                                               |  (Hub / Orchestrator)
+------------------+                                               +-------------------+
                                                                   | - API + UI        |
                                                                   | - Artifact hosting|
                                                                   | - Job orchestration
                                                                   +---------+---------+
                                                                             |
                                                                             | PXE/iPXE/HTTP(S) boot
                                                                             v
                                                                   +---------+---------+
                                                                   |      Client       |
                                                                   | (Desktop/Laptop)  |
                                                                   | boots custom Linux|
                                                                   +---------+---------+
                                                                             |
                                                                             | Runs Crucible
                                                                             v
                                                                   +-------------------+
                                                                   |     Crucible      |
                                                                   | (Diagnostics +    |
                                                                   |  Wipe + Reporting)|
                                                                   +-------------------+
                                                                             |
                                                                             | Results/Telemetry via API
                                                                             v
                                                                   +-------------------+
                                                                   |       Nexus       |
                                                                   |  (Inventory +     |
                                                                   |   Audit trail)    |
                                                                   +-------------------+
```

---

## Components

### 1) Nexus (Hub / Orchestrator)
Location: `Nexus/`

- Core responsibilities
  - Serve boot and runtime artifacts for PXE/iPXE/HTTP(S) clients.
  - Provide APIs for job assignment, results ingestion, inventory, and audit trail.
  - Host a UI to monitor devices, sessions, wipes, and reports.
- Notable structure
  - Backend/API: `Nexus/api/app/` (Python/FastAPI-style structure)
  - Frontend/UI: `Nexus/ui/` (Next.js app; pages in `Nexus/ui/pages/`)
  - Infrastructure: `Nexus/infra/`
    - `docker-compose.yml` — run-time stack
    - `nginx.conf` — HTTP reverse proxy/static hosting
  - Artifacts bucket: `Nexus/artifacts/` — published images and netboot assets
- Typical runtime
  - Nexus runs on a server in your network.
  - Your PXE/iPXE flow points clients to Nexus-hosted kernel/initrd and/or boot scripts.
  - Clients report back to Nexus via the API while Crucible is running.

### 2) Crucible (Edge Agent)
Location: `Crucible/`

- Core responsibilities
  - Executes hardware diagnostics and collects system information.
  - Performs secure erase/wipe of attached storage devices.
  - Reports results and logs to Nexus.
  - Can present a local UI if needed (e.g., kiosk/testing workflow).
- Notable structure
  - Agent logic: `Crucible/agent/`
    - Diagnostics: `agent/diagnostics/` (e.g., `cpu.py`, `base.py`)
    - Hardware info: `agent/hardware/` (e.g., `drive_info.py`, `system_info.py`)
    - Core managers: `agent/core/` (e.g., inventory sync, device condition manager)
    - GUI: `agent/gui/` (frames, device condition UI)
  - Device rules/conditions: `Crucible/device_conditions/*.json`
  - Local web UI and templates (for on-box interactions): `Crucible/web_server/`
  - Scripts and helpers: `Crucible/scripts/` (e.g., `start.sh`, `dev.py`)
  - Packaging/runtime: `Crucible/requirements.txt`, `Crucible/pyproject.toml`, `Crucible/version.py`
- Execution
  - Crucible is baked into the custom Linux image.
  - On boot, it initializes hardware checks, orchestrates wipes, and streams results back to Nexus.

### 3) Nexus Foundry (Image Builder)
Location: `nexus-foundry/`

- Core responsibilities
  - Build reproducible custom Linux images tailored for Crucible and PXE boot.
  - Codify configuration, packages, hooks, and bootloaders.
  - Produce artifacts suitable for ISO and/or netboot (kernel, initrd, squashfs, etc.).
- Notable structure
  - Config: `nexus-foundry/config/` (apt, bootloaders, installer tweaks, etc.)
  - Containerized build script: `nexus-foundry/scripts/build_in_container.sh`
  - Make-based entry: `nexus-foundry/Makefile`
  - Documentation: `nexus-foundry/docs/DEBIAN_LIVE_BUILD.md`
- Outputs
  - Build outputs include ISO and/or netboot directories (kernel/initrd) suitable for hosting on Nexus.
  - These artifacts are published to `Nexus/artifacts/` (or the path configured in your environment).

---

## End-to-End Flow

1) Build
- Use Nexus Foundry to produce a new image release.
- Verify the image boots locally (e.g., VM) and that Crucible starts.

2) Publish
- Place resulting artifacts in Nexus’ artifact storage (`Nexus/artifacts/`) or your configured object store.
- Update iPXE/PXE menus or boot scripts to reference the new kernel/initrd and command line.

3) Boot
- Target desktops/laptops network-boot into the custom Linux via PXE/iPXE.
- The init process launches Crucible.

4) Test and Wipe
- Crucible runs diagnostics (CPU, memory, storage, etc.) and gathers system info.
- Crucible executes secure erase/wipe procedures on the appropriate drives.
- Progress, logs, and results are sent back to Nexus through the API.

5) Report and Audit
- Nexus stores the session, test outcomes, and wipe attestations for inventory and audit.
- Operators can review status via the Nexus UI and export reports as needed.

---

## Data and Interfaces

- APIs (Nexus)
  - Job orchestration: assign tasks/policies to connecting clients.
  - Ingestion: accept telemetry, results, and wipe attestations from Crucible.
  - Inventory: query devices, sessions, and history.
- Artifacts
  - Kernel/initrd and root filesystem (squashfs) generated by Nexus Foundry.
  - Hosted by Nexus and referenced by PXE/iPXE boot configurations.
- Policies and Conditions (Crucible)
  - Device- and condition-driven behavior under `Crucible/device_conditions/*.json`.
  - Determines which tests or wipes to run per device profile.

---

## Operations Overview

- Running Nexus
  - Use `Nexus/infra/docker-compose.yml` to start the API/UI and NGINX reverse proxy.
  - Configure `Nexus/infra/nginx.conf` to serve artifacts and route API/UI.

- Building Images with Nexus Foundry
  - Recommended: `nexus-foundry/scripts/build_in_container.sh` for clean, reproducible builds.
  - See `nexus-foundry/docs/DEBIAN_LIVE_BUILD.md` for build details and prerequisites.

- Updating Crucible
  - Update code/config in `Crucible/`, rebuild images with Foundry, and republish artifacts to Nexus.

- PXE/iPXE Integration
  - Point your PXE/iPXE menus to Nexus-hosted kernel/initrd and boot scripts.
  - DHCP/TFTP services can be provided by your network stack; Nexus focuses on hosting and orchestration via HTTP(S) and API.

---

## Repositories and Key Paths

- `Nexus/`
  - API: `api/app/`
  - UI: `ui/`
  - Infra: `infra/docker-compose.yml`, `infra/nginx.conf`
  - Artifacts: `artifacts/`

- `Crucible/`
  - Agent: `agent/` (diagnostics, hardware, core, gui)
  - Conditions: `device_conditions/*.json`
  - Local web UI: `web_server/`
  - Scripts: `scripts/`

- `nexus-foundry/`
  - Build config: `config/`
  - Build script: `scripts/build_in_container.sh`
  - Makefile: `Makefile`
  - Docs: `docs/DEBIAN_LIVE_BUILD.md`

---

## Glossary

- PXE/iPXE: Network boot mechanisms used to load the custom Linux environment over the network.
- Artifact: A build output (kernel, initrd, ISO, squashfs, etc.) produced by Nexus Foundry and hosted by Nexus.
- Session: A single Crucible run on a device, including diagnostics and wipe operations.
- Wipe Attestation: A recorded, verifiable outcome of a secure erase procedure.

---

## Where to Go Next

- Build an image: see `nexus-foundry/docs/DEBIAN_LIVE_BUILD.md`.
- Run Nexus locally: use `Nexus/infra/docker-compose.yml`.
- Explore Crucible capabilities: browse `Crucible/agent/diagnostics/` and `Crucible/agent/hardware/`.
- Customize device policies: edit `Crucible/device_conditions/*.json` and rebuild.
