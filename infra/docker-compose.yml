services:
  nexus-api:
    build: ../api
    container_name: nexus-api
    environment:
      - NEXUS_DB_URL=sqlite:////data/nexus.db
      - NEXUS_HOSTNAME=nexus.lan:8080
    volumes:
      - ../api:/app:z
      - ./data:/data:z
      - /var/run/docker.sock:/var/run/docker.sock:z
    ports:
      - "8000:8000"
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    restart: unless-stopped

  nexus-ui:
    image: node:18-alpine
    container_name: nexus-ui
    working_dir: /app
    environment:
      - NEXT_PUBLIC_API_BASE=/api
    volumes:
      - ../ui:/app:z
    command: sh -c "npm install && npm run dev"
    expose:
      - "3000"
    restart: unless-stopped

  nexus-nginx:
    image: nginx:alpine
    container_name: nexus-nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro,z
      - ../artifacts:/usr/share/nexus/artifacts:ro,z
    ports:
      - "8080:80"
    depends_on:
      - nexus-api
      - nexus-ui
    restart: unless-stopped
