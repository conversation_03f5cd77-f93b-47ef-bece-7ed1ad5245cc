This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.js`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

---

## Nexus Setup Guide (Stripe + Local DB)

This project integrates Stripe Checkout and webhooks with a PostgreSQL database via Prisma.

### 1) Environment variables
Create `.env` using `.env.example` as a template.

Required for local testing:
```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000
STRIPE_SECRET_KEY=sk_test_xxx
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_xxx

# Local Postgres
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres

# Guest testing (optional but recommended)
TEST_CHECKOUT_EMAIL=<EMAIL>
# Will be set after seeding a user
# TEST_USER_ID=<filled by seed script>

# Authentication
# Generate a strong random string, e.g. `openssl rand -hex 32`
JWT_SECRET=replace_with_strong_secret

# Development-only: allow checkout without login by falling back to TEST_USER_ID
# Set to "true" (lowercase) to enable; leave unset in production
ALLOW_TEST_USER_ID=true
```

### 2) Start local Postgres (Docker)
```bash
docker run --name nexus-postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 -d postgres:16
```

### 3) Prisma install and migrations
```bash
npm install
npx prisma generate
npx prisma migrate dev --name init
```

### 4) Seed a test user
This creates/updates a user and prints `TEST_USER_ID` to add to `.env`.
```bash
npm run seed:test-user
# Copy the printed TEST_USER_ID into .env
```

### 5) Stripe CLI for webhooks
Install Stripe CLI and run:
```bash
stripe login
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```
Copy the printed signing secret into `.env`:
```bash
STRIPE_WEBHOOK_SECRET=whsec_xxx
```

### 6) Run the app and test checkout
```bash
npm run dev
```
Open `http://localhost:3000/pricing` and click a plan. You should be redirected to Stripe Checkout. Use Stripe test card `4242 4242 4242 4242`.

If `TEST_USER_ID` is set, webhook events will create rows tied to that user. Inspect data:
```bash
npx prisma studio
```

### Notes
* **Guest mode**: Checkout uses a guest flow but tags metadata with `TEST_USER_ID` if present so webhooks can write to DB without a full auth system.
* **Unhandled event types**: Webhook logs unknown events and ignores them by default.
* **Switch to hosted Postgres later**: Update `DATABASE_URL` to your hosted connection string (e.g., Neon/Supabase), then run `npx prisma migrate deploy`. Migrate data with `pg_dump/pg_restore` if needed.
* **Production**: Use live Stripe keys and a prod `STRIPE_WEBHOOK_SECRET`, set the webhook endpoint in the Stripe Dashboard to your deployed URL, and ensure API routes run on the Node.js runtime (already configured).

## Authentication (Email + Password)

Phase 1 authentication is implemented with JWT stored in an HttpOnly cookie.

Endpoints:

* `POST /api/auth/register` — create an account, sets `auth-token` cookie
* `POST /api/auth/login` — sign in with email/password, sets `auth-token` cookie
* `POST /api/auth/logout` — clears `auth-token` cookie
* `GET /api/auth/me` — returns the current user

Pages:

* `/register` — registration form
* `/login` — login form (supports `?next=/path`)
* `/account` — protected account page (enforced by middleware)

Checkout now requires authentication. In development, you can allow a fallback by setting `ALLOW_TEST_USER_ID=true` and providing `TEST_USER_ID` in `.env`. When enabled, the checkout API will use `TEST_USER_ID` and `TEST_CHECKOUT_EMAIL` to proceed without logging in. Do not enable this in production.

Install dependencies for auth:

```bash
npm install bcryptjs jsonwebtoken dotenv
```

Note: The seeded test user is intended for guest checkout linkage only and does not have a usable (hashed) password for login.
