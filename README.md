# Nexus (PXE Hub) – MVP (Relay mode)

Nexus lets you host iPXE scripts and boot artifacts (kernel/initrd/ISO) and view/manage devices running Crucible. This MVP runs locally with Docker Compose.

- UI: Next.js (JavaScript only, no TypeScript)
- API: FastAPI (Python)
- Reverse proxy + static: Nginx
- DB: SQLite (file in `infra/data/`)

## Prereqs (Fedora)
- Docker Engine + Compose plugin installed
- Map `nexus.lan` in DNS or use `http://localhost:8080` for UI while developing. The iPXE script uses `nexus.lan:8080` by default (configurable via env `NEXUS_HOSTNAME`).

## Run (development)

From this folder:

```bash
cd infra
docker compose up --build
```

Services:
- UI: http://localhost:8080/
- API (proxied): http://localhost:8080/api/health
- Artifacts: http://localhost:8080/artifacts/
- iPXE script: http://localhost:8080/ipxe?mac=00:11:22:33:44:55&uuid=demo

Stop with Ctrl+C. Add `-d` to run detached.

## Configure PXE settings
1. Open the UI → “PXE Settings” card.
2. Set:
   - Kernel URL (example): `/artifacts/vmlinuz`
   - Initrd URL (example): `/artifacts/initrd.img`
   - Kernel Args (example): `boot=live fetch=http://nexus.lan:8080/artifacts/rootfs.squashfs`
3. Click Save. Visit `/ipxe?mac=...` to see the generated script.

You can serve files by placing them in `Nexus/artifacts/` on the host.

## Relay mode (high level)
- Your existing DHCP chainloads to Nexus iPXE:
  ```ipxe
  chain http://nexus.lan/ipxe?mac=${net0/mac}&uuid=${uuid}
  ```
- Nexus responds with an iPXE script pointing to your configured kernel/initrd and args.

## Project layout
```
Nexus/
  api/               # FastAPI app
  ui/                # Next.js app (JS only)
  artifacts/         # Kernel/initrd/ISO (served at /artifacts)
  infra/
    docker-compose.yml
    nginx.conf
    data/            # SQLite DB file (runtime)
```

## Development notes
- UI dev server runs in a Node container and is reverse-proxied by Nginx. Hot reload works.
- API runs with `--reload` for live code changes.
- iPXE hostname used in scripts is `NEXUS_HOSTNAME` (default `nexus.lan:8080` in compose). Change it if you prefer.
- Keep code modular: small routers/services/schemas. Avoid giant files.

## Status
- [x] Next.js UI scaffold and reverse proxy via Nginx
- [x] FastAPI scaffold with `/api/health`
- [x] Docker Compose for UI/API/Nginx and static artifacts
- [x] Artifacts hosting at `/artifacts`
- [x] iPXE script endpoint `/ipxe`
- [x] Device results ingestion API and live updates (SSE)
- [x] Profiles UI
- [x] Per-MAC overrides influencing iPXE generation
- [ ] Optional: Postgres migration path

## Smoke test
With the stack running:

- Health check
  ```bash
  curl -sS http://localhost:8080/api/health
  ```

- Post a sample result (should appear live on the dashboard “Live Results” card)
  ```bash
  curl -sS -X POST http://localhost:8080/api/results \
    -H 'Content-Type: application/json' \
    -d '{
      "asset_number":"A1",
      "mac":"00:11:22:33:44:55",
      "profile_name":"Default",
      "test_name":"Keyboard Test",
      "status":"passed"
    }'
  ```

- Set a per-MAC iPXE override and fetch script
  ```bash
  curl -sS -X PUT http://localhost:8080/api/device_overrides/00:11:22:33:44:55 \
    -H 'Content-Type: application/json' \
    -d '{
      "mac":"00:11:22:33:44:55",
      "kernel_url":"/artifacts/vmlinuz",
      "initrd_url":"/artifacts/initrd.img",
      "kernel_args":"boot=live fetch=http://nexus.lan:8080/artifacts/rootfs.squashfs"
    }'

  curl -sS "http://localhost:8080/ipxe?mac=00:11:22:33:44:55&uuid=demo"
  ```

## Next steps
- Optional: switch DB to Postgres when we need scale/reporting.
