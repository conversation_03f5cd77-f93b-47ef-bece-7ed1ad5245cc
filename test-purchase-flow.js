const { PrismaClient } = require('@prisma/client');
const Stripe = require('stripe');
require('dotenv').config();

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function testPurchaseFlow() {
  try {
    console.log('=== TESTING COMPLETE PURCHASE FLOW ===\n');
    
    // Get user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log(`✅ Found user: ${user.email} (ID: ${user.id})\n`);
    
    // Test creating a checkout session
    console.log('1. Testing checkout session creation...');
    
    const creditPack = {
      id: 'credits_100',
      name: '100 Credits',
      credits: 100,
      price: 450 // $4.50
    };
    
    try {
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: creditPack.name,
                description: `${creditPack.credits} credits for ITAD operations`,
              },
              unit_amount: creditPack.price * 100 // Convert to cents
            },
            quantity: 1,
          },
        ],
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_APP_URL}/purchase/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/purchase/cancel`,
        customer_email: user.email,
        client_reference_id: user.id,
        metadata: {
          type: 'credits',
          source: 'test',
          userId: user.id,
          productId: creditPack.id,
          creditAmount: creditPack.credits.toString(),
          userEmail: user.email
        }
      });
      
      console.log(`✅ Checkout session created: ${session.id}`);
      console.log(`   URL: ${session.url}`);
      console.log(`   Amount: $${(session.amount_total / 100).toFixed(2)}`);
      console.log(`   Metadata:`, session.metadata);
      
    } catch (error) {
      console.log(`❌ Failed to create checkout session: ${error.message}`);
      return;
    }
    
    console.log('\n2. Checking webhook endpoint...');
    
    // Test if webhook endpoint is accessible
    try {
      const response = await fetch('http://localhost:3000/api/webhooks/stripe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'stripe-signature': 'test'
        },
        body: JSON.stringify({ test: true })
      });
      
      console.log(`   Webhook endpoint status: ${response.status}`);
      
      if (response.status === 400) {
        console.log('   ✅ Webhook endpoint is accessible (signature verification failed as expected)');
      } else {
        console.log('   ⚠️  Unexpected response from webhook endpoint');
      }
      
    } catch (error) {
      console.log(`   ❌ Webhook endpoint not accessible: ${error.message}`);
      console.log('   Make sure your Next.js dev server is running on localhost:3000');
    }
    
    console.log('\n3. Checking current credit balance...');
    
    const credits = await prisma.credit.aggregate({
      where: { 
        userId: user.id,
        remainingAmount: { gt: 0 }
      },
      _sum: { remainingAmount: true }
    });
    
    console.log(`   Current balance: ${credits._sum.remainingAmount || 0} credits`);
    
    console.log('\n=== NEXT STEPS ===');
    console.log('1. Make sure your Next.js dev server is running: npm run dev');
    console.log('2. In another terminal, run: stripe login');
    console.log('3. Then run: stripe listen --forward-to localhost:3000/api/webhooks/stripe');
    console.log('4. Copy the webhook secret from the stripe listen output');
    console.log('5. Update STRIPE_WEBHOOK_SECRET in your .env file');
    console.log('6. Try making a test purchase using Stripe test card: 4242 4242 4242 4242');
    
  } catch (error) {
    console.error('Error testing purchase flow:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPurchaseFlow();
