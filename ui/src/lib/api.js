const BASE = (process.env.NEXT_PUBLIC_API_BASE || '').replace(/\/$/, '')

async function request(path, options = {}) {
  // Normalize to avoid duplicate /api and double slashes
  let normalizedPath = path
  if (typeof normalizedPath !== 'string') {
    normalizedPath = String(normalizedPath || '')
  }
  // Ensure path starts with a single leading slash
  if (normalizedPath && !normalizedPath.startsWith('/')) {
    normalizedPath = `/${normalizedPath}`
  }
  // If BASE already ends with /api and path starts with /api, drop one copy
  if (BASE.endsWith('/api') && normalizedPath.startsWith('/api/')) {
    normalizedPath = normalizedPath.replace(/^\/api/, '')
  }

  const url = `${BASE}${normalizedPath}` || normalizedPath

  const res = await fetch(url, {
    headers: { 'Content-Type': 'application/json', ...(options.headers || {}) },
    ...options,
  })
  if (!res.ok) {
    const text = await res.text()
    throw new Error(`API ${res.status}: ${text}`)
  }
  const ct = res.headers.get('content-type') || ''
  if (ct.includes('application/json')) return res.json()
  return res.text()
}

export function apiGet(path) {
  return request(path)
}

export function apiPost(path, body) {
  return request(path, { method: 'POST', body: JSON.stringify(body) })
}

export function apiPatch(path, body) {
  return request(path, { method: 'PATCH', body: JSON.stringify(body) })
}

export function apiDelete(path) {
  return request(path, { method: 'DELETE' })
}

export function apiPut(path, body) {
  return request(path, { method: 'PUT', body: JSON.stringify(body) })
}

