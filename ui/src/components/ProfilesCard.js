import { useEffect, useMemo, useState } from 'react'
import useSWR from 'swr'
import Card from './Card'
import { apiGet, apiPost, apiPatch, apiDelete } from '../lib/api'

const fetcher = (url) => apiGet(url)

export default function ProfilesCard() {
  const { data: profiles, error: profilesError, mutate } = useSWR('/api/profiles', fetcher)
  const { data: def, error: defError, mutate: mutateDef } = useSWR('/api/profiles/default', fetcher)

  const [editing, setEditing] = useState(null)
  const [form, setForm] = useState({ name: '', description: '', payload: '{"name":"","description":"","device_type":"Generic","tests":[],"test_args":{}}', version: '' })
  const [saving, setSaving] = useState(false)
  const [setAsDefault, setSetAsDefault] = useState(false)
  const [payloadError, setPayloadError] = useState('')
  const [selectedTests, setSelectedTests] = useState([])
  // RAM/CPU custom options state
  const [ramMode, setRamMode] = useState('percentage') // 'percentage' | 'absolute'
  const [ramSize, setRamSize] = useState(25) // number; % or MB depending on mode
  const [ramDuration, setRamDuration] = useState(30) // seconds
  const [cpuDuration, setCpuDuration] = useState(20) // seconds
  const [cpuIntensity, setCpuIntensity] = useState(100) // 1-100

  // Friendly test options mapped to fully-qualified function paths
  const TEST_OPTIONS = useMemo(() => ([
    {
      id: 'lcd',
      label: 'LCD Test',
      path: 'agent.tests.display_test.run_lcd_test_gui',
      desc: 'Interactive LCD color cycle test.'
    },
    {
      id: 'pointer',
      label: 'Pointing Device Test',
      path: 'agent.tests.pointing_device_test.run_pointing_device_test',
      desc: 'Mouse/touchpad movement and buttons.'
    },
    {
      id: 'keyboard',
      label: 'Keyboard Test',
      path: 'agent.tests.keyboard_test.run_keyboard_test',
      desc: 'Interactive keyboard verification.'
    },
    {
      id: 'ram',
      label: 'RAM Test (Visual)',
      path: 'agent.tests.visual_ram_test.run_visual_ram_test',
      desc: 'Visual RAM test with progress.'
    },
    {
      id: 'cpu',
      label: 'CPU Stress Test (Visual)',
      path: 'agent.tests.visual_cpu_test.visual_cpu_test',
      desc: 'Visual CPU stress with live usage.'
    },
    {
      id: 'touch',
      label: 'Touch Screen Test',
      path: 'agent.tests.touch_screen_test.run_touch_screen_test',
      desc: 'Targets, multi-touch, pinch/zoom.'
    },
    {
      id: 'wipe',
      label: 'Secure Drive Wipe',
      path: 'agent.tests.drive_wipe_test.run_secure_wipe_test',
      desc: 'Destructive secure wipe workflow.'
    }
  ]), [])

  function buildPayloadFromSelection(nextSelected) {
    const tests = nextSelected
      .map(id => TEST_OPTIONS.find(opt => opt.id === id))
      .filter(Boolean)
      .map(opt => opt.path)
    // Build test_args for CPU/RAM if selected
    const testArgs = {}
    const ramPath = 'agent.tests.visual_ram_test.run_visual_ram_test'
    const cpuPath = 'agent.tests.visual_cpu_test.visual_cpu_test'
    if (nextSelected.includes('ram')) {
      testArgs[ramPath] = {
        test_size_mode: ramMode === 'percentage' ? 'percentage' : 'absolute',
        test_size_value: Number(ramSize) || (ramMode === 'percentage' ? 25 : 1024),
        duration_seconds: Number(ramDuration) || 30,
      }
    }
    if (nextSelected.includes('cpu')) {
      testArgs[cpuPath] = {
        duration_seconds: Number(cpuDuration) || 20,
        cpu_load_intensity: Number(cpuIntensity) || 100,
      }
    }
    const obj = {
      name: form.name || '',
      description: form.description || '',
      device_type: 'Generic',
      tests,
      test_args: testArgs
    }
    return JSON.stringify(obj, null, 2)
  }

  useEffect(() => {
    if (editing && profiles) {
      const p = profiles.find(x => x.id === editing)
      if (p) {
        setForm({ name: p.name, description: p.description || '', payload: p.payload || '{}', version: p.version || '' })
        setSetAsDefault(def?.default_id === p.id)
        // Preselect tests from existing payload
        try {
          const obj = JSON.parse(p.payload || '{}')
          const paths = Array.isArray(obj?.tests) ? obj.tests : []
          const ids = TEST_OPTIONS.filter(opt => paths.includes(opt.path)).map(opt => opt.id)
          setSelectedTests(ids)
          // Prefill RAM/CPU config from test_args if present
          const ta = obj?.test_args || {}
          const ramCfg = ta['agent.tests.visual_ram_test.run_visual_ram_test']
          if (ramCfg) {
            if (ramCfg.test_size_mode === 'absolute') setRamMode('absolute'); else setRamMode('percentage')
            if (typeof ramCfg.test_size_value !== 'undefined') setRamSize(Number(ramCfg.test_size_value))
            if (typeof ramCfg.duration_seconds !== 'undefined') setRamDuration(Number(ramCfg.duration_seconds))
          }
          const cpuCfg = ta['agent.tests.visual_cpu_test.visual_cpu_test']
          if (cpuCfg) {
            if (typeof cpuCfg.duration_seconds !== 'undefined') setCpuDuration(Number(cpuCfg.duration_seconds))
            if (typeof cpuCfg.cpu_load_intensity !== 'undefined') setCpuIntensity(Number(cpuCfg.cpu_load_intensity))
          }
        } catch {
          setSelectedTests([])
        }
      }
    }
  }, [editing, profiles, def])

  // Validate JSON payload
  useEffect(() => {
    try {
      JSON.parse(form.payload || '{}')
      setPayloadError('')
    } catch (e) {
      setPayloadError(e.message)
    }
  }, [form.payload])

  // Rebuild payload when name/description change to keep JSON in sync if selection is used
  useEffect(() => {
    if (selectedTests.length > 0) {
      setForm(f => ({ ...f, payload: buildPayloadFromSelection(selectedTests) }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form.name, form.description])

  // Rebuild payload when CPU/RAM options change and relevant tests are selected
  useEffect(() => {
    if (selectedTests.includes('ram') || selectedTests.includes('cpu')) {
      setForm(f => ({ ...f, payload: buildPayloadFromSelection(selectedTests) }))
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ramMode, ramSize, ramDuration, cpuDuration, cpuIntensity])

  function toggleTest(id) {
    setSelectedTests(prev => {
      const exists = prev.includes(id)
      const next = exists ? prev.filter(x => x !== id) : [...prev, id]
      // Update payload JSON automatically
      setForm(f => ({ ...f, payload: buildPayloadFromSelection(next) }))
      return next
    })
  }

  function selectAllTests() {
    const all = TEST_OPTIONS.map(o => o.id)
    setSelectedTests(all)
    setForm(f => ({ ...f, payload: buildPayloadFromSelection(all) }))
  }

  function clearAllTests() {
    setSelectedTests([])
    setForm(f => ({ ...f, payload: buildPayloadFromSelection([]) }))
  }

  function prettyPrintPayload() {
    try {
      const obj = JSON.parse(form.payload || '{}')
      setForm(f => ({ ...f, payload: JSON.stringify(obj, null, 2) }))
    } catch {}
  }

  function exportPayload() {
    const blob = new Blob([form.payload || '{}'], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${(form.name || 'profile').replace(/\s+/g,'_').toLowerCase()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  function importPayload(e) {
    const file = e.target.files?.[0]
    if (!file) return
    const reader = new FileReader()
    reader.onload = () => {
      try {
        const text = String(reader.result || '')
        JSON.parse(text) // validate
        setForm(f => ({ ...f, payload: text }))
      } catch (err) {
        alert('Invalid JSON in file')
      }
    }
    reader.readAsText(file)
    // reset input so same file can be selected again
    e.target.value = ''
  }

  async function onCreate() {
    setSaving(true)
    try {
      const created = await apiPost('/api/profiles', form)
      if (setAsDefault && created?.id) {
        await apiPost('/api/profiles/default', { default_id: created.id })
        await mutateDef()
      }
      await mutate()
      setForm({ name: '', description: '', payload: '{"name":"","description":"","device_type":"Generic","tests":[],"test_args":{}}', version: '' })
      setSelectedTests([])
      setSetAsDefault(false)
    } finally { setSaving(false) }
  }

  async function onUpdate() {
    if (!editing) return
    setSaving(true)
    try {
      await apiPatch(`/api/profiles/${editing}`, form)
      if (setAsDefault) {
        await apiPost('/api/profiles/default', { default_id: editing })
      } else if (def?.default_id === editing) {
        // If toggled off and it was default, clear default
        await apiPost('/api/profiles/default', { default_id: null })
      }
      await mutateDef()
      await mutate()
      setEditing(null)
      setSelectedTests([])
      setSetAsDefault(false)
    } finally { setSaving(false) }
  }

  async function onDelete(id) {
    if (!confirm('Delete profile? This cannot be undone.')) return
    // If deleting the current default, clear the default first to keep state consistent
    try {
      if (def?.default_id === id) {
        await apiPost('/api/profiles/default', { default_id: null })
        await mutateDef()
      }
    } catch {}
    await apiDelete(`/api/profiles/${id}`)
    await mutate()
    setEditing(null)
  }

  async function onSetDefault(id) {
    await apiPost('/api/profiles/default', { default_id: id })
    await mutateDef()
  }

  const defaultId = def?.default_id ?? null

return (
  <Card title="Profiles">
    <div className="profiles">
      <div className="sidebar">
        {(profilesError || defError) && (
          <div className="error-banner" role="alert">
            <div className="error-title">Unable to load profiles</div>
            <div className="error-body">
              {String(profilesError || defError)}
            </div>
            <div className="error-actions">
              <button className="btn-secondary" onClick={() => { mutate(); mutateDef(); }}>Retry</button>
            </div>
          </div>
        )}
        <table className="table">
          <thead>
            <tr>
              <th>Profiles</th>
            </tr>
          </thead>
          <tbody>
            {profiles ? (
              profiles.length > 0 ? (
                profiles.map(p => (
                  <tr key={p.id} className="row-click" onClick={() => setEditing(p.id)}>
                    <td title={p.name} className="name-cell">
                      <span className="name-text">{p.name}</span>
                      {defaultId === p.id && <span className="badge-default">Default</span>}
                    </td>
                  </tr>
                ))
              ) : (
                <tr><td><span style={{color:'var(--muted)'}}>No profiles yet. Create one using the editor.</span></td></tr>
              )
            ) : (
              <tr><td>Loading...</td></tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="editor">
        <h3 style={{margin: '8px 0'}}>Editor</h3>
        <div className="form">
          <label>
            <span>Name</span>
            <input value={form.name} onChange={e => setForm({ ...form, name: e.target.value })} placeholder="e.g. Wipe Only" />
          </label>
          <label>
            <span>Description</span>
            <input value={form.description} onChange={e => setForm({ ...form, description: e.target.value })} placeholder="Optional description" />
          </label>
          <div className="tests">
            <div className="tests-header">
              <div className="tests-title-row">
                <span>Tests</span>
                <div className="tests-actions">
                  <button type="button" className="btn-chip" onClick={selectAllTests}>Select All</button>
                  <button type="button" className="btn-chip" onClick={clearAllTests}>Clear</button>
                </div>
              </div>
              <small className="tests-sub">Select tests to include in this profile. Payload JSON updates automatically.</small>
            </div>
            <div className="tests-grid">
              {TEST_OPTIONS.map(opt => (
                <label key={opt.id} className="test-item">
                  <input type="checkbox" checked={selectedTests.includes(opt.id)} onChange={() => toggleTest(opt.id)} />
                  <div className="test-meta">
                    <div className="test-title">{opt.label}</div>
                    <div className="test-desc">{opt.desc}</div>
                    <div className="test-path" title={opt.path}>{opt.path}</div>
                  </div>
                </label>
              ))}
            </div>
            {(selectedTests.includes('ram') || selectedTests.includes('cpu')) && (
              <div className="config-panels">
                {selectedTests.includes('ram') && (
                  <div className="config-card">
                    <div className="config-title">RAM Test Configuration</div>
                    <div className="config-row">
                      <label className="inline">
                        <input
                          type="radio"
                          name="ram-mode"
                          checked={ramMode === 'percentage'}
                          onChange={() => setRamMode('percentage')}
                        />
                        <span>Percentage</span>
                      </label>
                      <label className="inline" style={{marginLeft:12}}>
                        <input
                          type="radio"
                          name="ram-mode"
                          checked={ramMode === 'absolute'}
                          onChange={() => setRamMode('absolute')}
                        />
                        <span>Absolute (MB)</span>
                      </label>
                    </div>
                    <div className="config-row">
                      <label className="stack">
                        <span>Test Size ({ramMode === 'percentage' ? '%' : 'MB'})</span>
                        <input
                          type="number"
                          min={ramMode === 'percentage' ? 1 : 1}
                          max={ramMode === 'percentage' ? 50 : undefined}
                          value={ramSize}
                          onChange={e => setRamSize(e.target.valueAsNumber || 0)}
                        />
                      </label>
                      <label className="stack">
                        <span>Duration (sec)</span>
                        <input
                          type="number"
                          min={5}
                          max={3600}
                          value={ramDuration}
                          onChange={e => setRamDuration(e.target.valueAsNumber || 0)}
                        />
                      </label>
                    </div>
                  </div>
                )}
                {selectedTests.includes('cpu') && (
                  <div className="config-card">
                    <div className="config-title">CPU Test Configuration</div>
                    <div className="config-row">
                      <label className="stack">
                        <span>Duration (sec)</span>
                        <input
                          type="number"
                          min={5}
                          max={3600}
                          value={cpuDuration}
                          onChange={e => setCpuDuration(e.target.valueAsNumber || 0)}
                        />
                      </label>
                      <label className="stack">
                        <span>CPU Load Intensity (%)</span>
                        <input
                          type="number"
                          min={1}
                          max={100}
                          value={cpuIntensity}
                          onChange={e => setCpuIntensity(e.target.valueAsNumber || 0)}
                        />
                      </label>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
          <label>
            <span>Payload (JSON)</span>
            <textarea rows={12} value={form.payload} onChange={e => setForm({ ...form, payload: e.target.value })} style={{background:'#121722',color:'var(--text)',border:'1px solid var(--border)',borderRadius:8,padding:10,fontFamily:'ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace'}} />
            {payloadError ? (
              <div style={{color:'#ff6b6b', marginTop:6, fontSize:12}}>Invalid JSON: {payloadError}</div>
            ) : (
              <div style={{color:'#7ad97a', marginTop:6, fontSize:12}}>JSON OK</div>
            )}
            <div style={{marginTop:8, display:'flex', gap:8, alignItems:'center'}}>
              <button type="button" className="btn-secondary" onClick={prettyPrintPayload}>Pretty</button>
              <button type="button" className="btn-secondary" onClick={exportPayload}>Export</button>
              <label className="btn-secondary" style={{cursor:'pointer'}}>
                Import<input type="file" accept="application/json,.json" onChange={importPayload} style={{display:'none'}} />
              </label>
            </div>
          </label>
          <label>
            <span>Version</span>
            <input value={form.version} onChange={e => setForm({ ...form, version: e.target.value })} placeholder="Optional version" />
          </label>
          <label style={{display:'flex', alignItems:'center', gap:8}}>
            <input type="checkbox" checked={setAsDefault} onChange={e => setSetAsDefault(e.target.checked)} />
            <span>Set as Default</span>
          </label>
          <div className="actions">
            {!editing ? (
              <button className="btn-secondary" onClick={onCreate} disabled={saving || !form.name || !!payloadError}>{saving ? 'Saving...' : 'Create'}</button>
            ) : (
              <div style={{display:'flex', gap:8, flexWrap:'wrap'}}>
                <button className="btn-secondary" onClick={onUpdate} disabled={saving || !form.name || !!payloadError}>{saving ? 'Saving...' : 'Save'}</button>
                <button className="btn-secondary" onClick={() => setEditing(null)}>Cancel</button>
                <button className="btn-danger" onClick={() => onDelete(editing)} disabled={saving}>Delete</button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
    <style jsx>{`
      .profiles { display: grid; grid-template-columns: 320px 1fr; gap: 16px; align-items: start; }
      .sidebar { overflow-y: auto; overflow-x: hidden; }
      .table { width: 100%; min-width: 0; table-layout: fixed; }
      .row-click { cursor: pointer; }
      .row-click:hover { background: #0f1521; }
      .name-cell { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 0; }
      .error-banner { border: 1px solid #5b2b2b; background: #261218; color: #ffd1d1; padding: 10px; border-radius: 8px; margin-bottom: 10px; }
      .error-title { font-weight: 600; margin-bottom: 4px; color: #ff8f8f; }
      .error-body { font-size: 12px; opacity: 0.9; }
      .error-actions { margin-top: 8px; }
      .tests { margin: 8px 0 12px; padding: 12px; border: 1px solid var(--border); border-radius: 10px; background: #121722; }
      .tests-header { display: flex; flex-direction: column; gap: 6px; margin-bottom: 10px; }
      .tests-title-row { display: flex; align-items: center; justify-content: space-between; }
      .tests-sub { color: var(--muted); }
      .tests-actions { display: flex; gap: 6px; }
      .btn-chip { background: #0f1521; border: 1px solid var(--border); color: var(--text); padding: 4px 8px; border-radius: 999px; font-size: 12px; line-height: 1; }
      .btn-chip:hover { background: #0b101b; }
      .tests-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; }
      .test-item { display: flex; gap: 10px; align-items: flex-start; padding: 10px; border: 1px solid var(--border); border-radius: 10px; background: #0f1521; transition: background 0.15s, border-color 0.15s; }
      .test-item:hover { background: #0b101b; border-color: #2a3350; }
      .test-meta { display: flex; flex-direction: column; gap: 3px; }
      .test-title { font-weight: 600; }
      .test-desc { font-size: 12px; color: var(--muted); }
      .test-path { font-size: 11px; color: #7a88a8; font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 100%; opacity: 0.85; }
      .config-panels { margin-top: 12px; display: grid; grid-template-columns: 1fr; gap: 12px; }
      .config-card { border: 1px solid var(--border); border-radius: 10px; background: #0f1521; padding: 12px; }
      .config-title { font-weight: 600; margin-bottom: 8px; }
      .config-row { display: flex; gap: 12px; align-items: flex-end; flex-wrap: wrap; }
      .inline { display: inline-flex; align-items: center; gap: 6px; }
      .stack { display: flex; flex-direction: column; gap: 6px; min-width: 160px; }
      .stack input { background: #121722; color: var(--text); border: 1px solid var(--border); border-radius: 8px; padding: 6px 8px; }
      @media (max-width: 1200px) { .profiles { grid-template-columns: 280px 1fr; } }
      @media (max-width: 900px) { 
        .profiles { grid-template-columns: 1fr; }
        .table { min-width: 100%; }
      }
    `}</style>
  </Card>
)


}
