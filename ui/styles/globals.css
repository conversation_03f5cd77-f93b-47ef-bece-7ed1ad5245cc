/* Dark theme inspired by <PERSON><PERSON><PERSON> */
:root {
  --bg: #0f1115;
  --panel: #151922;
  --panel-2: #1b2130;
  --text: #e6e6e6;
  --muted: #a8b0c0;
  --accent: #4da3ff;
  --accent-2: #2c7be5;
  --ok: #2ecc71;
  --warn: #f39c12;
  --danger: #e74c3c;
  --border: #2a2f3a;
}

html, body, #__next { height: 100%; }
body { margin: 0; background: var(--bg); color: var(--text); font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji"; }

.app { display: flex; flex-direction: column; min-height: 100%; }
.app-header { padding: 16px 20px; background: linear-gradient(180deg, #0f1115 0%, #121621 100%); border-bottom: 1px solid var(--border); position: sticky; top: 0; z-index: 10; }
.app-header h1 { margin: 0; font-size: 20px; font-weight: 600; }
.app-header .sub { color: var(--muted); font-size: 12px; margin-top: 4px; }
.app-main { padding: 20px; max-width: 1200px; width: 100%; margin: 0 auto; }

.grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 16px; align-items: start; }

.dashboard-card { background: linear-gradient(180deg, var(--panel) 0%, var(--panel-2) 100%); border: 1px solid var(--border); border-radius: 10px; box-shadow: 0 6px 18px rgba(0,0,0,0.25); overflow: hidden; }
.dashboard-card h2 { margin: 0; padding: 14px 16px; font-size: 16px; font-weight: 600; border-bottom: 1px solid var(--border); background: rgba(255,255,255,0.02); }
.dashboard-card .card-body { padding: 16px; }

.btn-secondary { background: #1f2735; color: var(--text); border: 1px solid var(--border); border-radius: 8px; padding: 8px 12px; cursor: pointer; transition: all .15s ease; }
.btn-secondary:hover { background: #222b3c; border-color: #3a4150; }
.btn-secondary:disabled { opacity: .6; cursor: default; }

.kv { display: grid; grid-template-columns: 1fr auto; gap: 10px; padding: 6px 0; }
.kv + .kv { border-top: 1px dashed var(--border); }

.ok { color: var(--ok); }
.warn { color: var(--warn); }
.danger { color: var(--danger); }

.form { display: grid; gap: 12px; }
.form label { display: grid; gap: 6px; }
.form input { background: #121722; color: var(--text); border: 1px solid var(--border); border-radius: 8px; padding: 8px 10px; }
.form .actions { display: flex; align-items: center; gap: 6px; margin-top: 4px; }

.table { width: 100%; border-collapse: collapse; }
.table th, .table td { padding: 8px 10px; border-bottom: 1px solid var(--border); text-align: left; font-size: 14px; }
.table thead th { color: var(--muted); font-weight: 600; background: rgba(255,255,255,0.03); }
.table tbody tr:hover { background: rgba(255,255,255,0.02); }
