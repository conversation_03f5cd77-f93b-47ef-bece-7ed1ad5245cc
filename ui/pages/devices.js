import useSWR from 'swr'
import { useEffect, useMemo, useState } from 'react'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'
import { apiGet, apiPut, apiDelete } from '../src/lib/api'

const fetcher = (url) => apiGet(url)

function OverrideEditor({ mac, onClose }) {
  const { data: profiles } = useSWR('/api/profiles', fetcher)
  // Custom fetcher that treats 404 as no override
  const overrideFetcher = async (url) => {
    const res = await fetch(url)
    if (res.status === 404) return null
    if (!res.ok) throw new Error(`API ${res.status}`)
    return res.json()
  }
  const { data: existing, mutate } = useSWR(mac ? `/api/device_overrides/${encodeURIComponent(mac)}` : null, overrideFetcher)

  const [form, setForm] = useState({ mac, profile_id: '', kernel_url: '', initrd_url: '', kernel_args: '' })
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (existing) {
      setForm({
        mac,
        profile_id: existing.profile_id || '',
        kernel_url: existing.kernel_url || '',
        initrd_url: existing.initrd_url || '',
        kernel_args: existing.kernel_args || '',
      })
    }
  }, [existing, mac])

  async function onSave(e) {
    e.preventDefault()
    setSaving(true)
    setError('')
    try {
      await apiPut(`/api/device_overrides/${encodeURIComponent(mac)}`, {
        mac,
        profile_id: form.profile_id ? Number(form.profile_id) : null,
        kernel_url: form.kernel_url || null,
        initrd_url: form.initrd_url || null,
        kernel_args: form.kernel_args ?? '',
      })
      await mutate()
      onClose?.()
    } catch (err) {
      setError(String(err.message || err))
    } finally {
      setSaving(false)
    }
  }

  async function onClear() {
    setSaving(true)
    setError('')
    try {
      await apiDelete(`/api/device_overrides/${encodeURIComponent(mac)}`)
      await mutate()
      onClose?.()
    } catch (err) {
      setError(String(err.message || err))
    } finally {
      setSaving(false)
    }
  }

  return (
    <form onSubmit={onSave} className="form">
      <div className="kv"><div>MAC</div><div><code>{mac}</code></div></div>
      <label>
        <span>Profile Override (optional)</span>
        <select value={form.profile_id} onChange={e => setForm({ ...form, profile_id: e.target.value })}>
          <option value="">— None —</option>
          {profiles?.map(p => (
            <option key={p.id} value={p.id}>{p.name}</option>
          ))}
        </select>
      </label>
      <div style={{ opacity: 0.8, fontSize: 12, marginBottom: 6 }}>
        Leave kernel/initrd/args empty to use global or profile defaults.
      </div>
      <label>
        <span>Kernel URL</span>
        <input value={form.kernel_url} onChange={e => setForm({ ...form, kernel_url: e.target.value })} placeholder="/artifacts/vmlinuz or http://..." />
      </label>
      <label>
        <span>Initrd URL</span>
        <input value={form.initrd_url} onChange={e => setForm({ ...form, initrd_url: e.target.value })} placeholder="/artifacts/initrd.img" />
      </label>
      <label>
        <span>Kernel Args</span>
        <input value={form.kernel_args} onChange={e => setForm({ ...form, kernel_args: e.target.value })} placeholder="boot=live fetch=http://nexus.lan:8080/artifacts/rootfs.squashfs" />
      </label>
      {error && <div className="warn" style={{ marginBottom: 8 }}>{error}</div>}
      <div className="actions">
        <button className="btn-secondary" type="submit" disabled={saving}>{saving ? 'Saving...' : 'Save'}</button>
        <button className="btn-secondary" type="button" onClick={onClear} disabled={saving} style={{ marginLeft: 8 }}>Clear Override</button>
        <button className="btn-secondary" type="button" onClick={onClose} style={{ marginLeft: 8 }}>Close</button>
      </div>
    </form>
  )
}

export default function DevicesPage() {
  const { data: devices } = useSWR('/api/devices/recent', fetcher, { refreshInterval: 5000 })
  const [selectedMac, setSelectedMac] = useState(null)

  return (
    <Layout>
      <div className="grid">
        <Card title="Devices">
          <table className="table">
            <thead>
              <tr>
                <th>MAC</th>
                <th>UUID</th>
                <th>Serial</th>
                <th>Last Seen</th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {devices ? devices.map(d => (
                <tr key={d.id}>
                  <td>{d.mac}</td>
                  <td>{d.uuid || '-'}</td>
                  <td>{d.serial || '-'}</td>
                  <td>{new Date(d.last_seen).toLocaleString()}</td>
                  <td>
                    <button className="btn-secondary" onClick={() => setSelectedMac(d.mac)}>Override...</button>
                  </td>
                </tr>
              )) : (
                <tr><td colSpan={5}>Loading...</td></tr>
              )}
            </tbody>
          </table>
        </Card>

        {selectedMac && (
          <Card title={`Override: ${selectedMac}`}>
            <OverrideEditor mac={selectedMac} onClose={() => setSelectedMac(null)} />
          </Card>
        )}
      </div>
    </Layout>
  )
}
