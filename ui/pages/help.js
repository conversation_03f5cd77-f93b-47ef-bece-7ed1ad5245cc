import Layout from '../src/components/Layout'
import Card from '../src/components/Card'

export default function HelpPage() {
  return (
    <Layout>
      <div style={{ maxWidth: 1100, margin: '2rem auto', padding: '1rem' }}>
        <h1>Help & Getting Started</h1>
        <p>This guide walks you through using Nexus (server), Crucible (desktop app), and Purge (secure erase) together.</p>

        <div className="grid" style={{ display:'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
          <Card title="1) Configure Nexus">
            <ol className="steps">
              <li>Open <a href="/settings">Settings</a> and set the Website Base URL.</li>
              <li>Paste your API Key from your Nexus Website account (Account → API Keys).</li>
              <li>Save. The banner will disappear when configuration is complete.</li>
            </ol>
            <p className="tip">Tip: Artifacts (kernel/initrd/rootfs) are served from <code>/artifacts/</code>. Put files in <code>Nexus/artifacts/</code> on the server.</p>
          </Card>

          <Card title="2) Create Profiles">
            <ol className="steps">
              <li>Go to <a href="/profiles">Profiles</a> and create test bundles (e.g., diagnostics + wipe).</li>
              <li>Set kernel/initrd/args in <a href="/">PXE Boot Settings</a> or per-device overrides.</li>
              <li>Profiles influence the iPXE script served to devices.</li>
            </ol>
          </Card>

          <Card title="3) Connect Devices">
            <ol className="steps">
              <li>Point your DHCP/iPXE to Nexus: <code>http://nexus.lan/ipxe?mac=${'{net0/mac}'}&uuid=${'{uuid}'}</code></li>
              <li>Use <a href="/devices">Devices</a> to apply per‑MAC overrides if needed.</li>
              <li>Boot the device; it will fetch the iPXE script from Nexus and start the selected workflow.</li>
            </ol>
          </Card>

          <Card title="4) Run Crucible on the bench">
            <ol className="steps">
              <li>Install and open Crucible on the workstation.</li>
              <li>Ensure the workstation has network access to the Nexus server.</li>
              <li>Crucible will report diagnostics/wipe status back to Nexus.</li>
            </ol>
          </Card>

          <Card title="5) Purge (Secure Erase)">
            <ol className="steps">
              <li>When a wipe job starts, a credit operation is created automatically (if configured).</li>
              <li>On completion, results/verification are recorded and the operation is closed.</li>
              <li>Track activity in <a href="/live">Activity</a>.</li>
            </ol>
          </Card>

          <Card title="Troubleshooting">
            <ul className="bullets">
              <li><strong>No activity shown:</strong> Check <code>/api/health</code> and networking between devices and the Nexus server.</li>
              <li><strong>PXE boots fail:</strong> Verify kernel/initrd URLs in Settings and that files exist under <code>artifacts/</code>.</li>
              <li><strong>Credits not consumed:</strong> Confirm Website Base URL and API Key set under <a href="/settings">Settings</a>.</li>
            </ul>
          </Card>
        </div>

        <style jsx>{`
          .steps { margin: 0 0 6px 18px; }
          .steps li { margin-bottom: 8px; }
          .tip { color: var(--muted); margin-top: 8px; }
          .bullets { margin: 0 0 6px 18px; }
          .bullets li { margin-bottom: 8px; }
          code { background: #0b101b; padding: 2px 6px; border-radius: 6px; border: 1px solid var(--border); }
        `}</style>
      </div>
    </Layout>
  )
}
