import { useEffect, useRef, useState } from 'react'
import Layout from '../src/components/Layout'
import Card from '../src/components/Card'

export default function LiveResultsPage() {
  const [liveResults, setLiveResults] = useState([])
  const esRef = useRef(null)

  useEffect(() => {
    try {
      const es = new EventSource('/api/events')
      esRef.current = es
      es.onmessage = (ev) => {
        try {
          const data = JSON.parse(ev.data || '{}')
          if (data?.type === 'result_created' && data?.result) {
            setLiveResults(prev => [data.result, ...prev].slice(0, 100))
          }
        } catch {}
      }
      es.onerror = () => { /* allow auto-retry */ }
      return () => { try { esRef.current?.close() } catch {} }
    } catch {}
  }, [])

  return (
    <Layout>
      <Card title="Live Results">
        <p style={{margin:'0 0 12px', color:'var(--muted)'}}>Streaming newest results first. Leave this page open to monitor activity.</p>
        {liveResults.length === 0 ? (
          <p>No results yet. Post to <code>/api/results</code> to see live updates.</p>
        ) : (
          <div style={{overflowX:'auto'}}>
            <table className="table">
              <thead>
                <tr>
                  <th>Time</th>
                  <th>Asset</th>
                  <th>MAC</th>
                  <th>Profile</th>
                  <th>Test</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                {liveResults.map((r) => (
                  <tr key={r.id}>
                    <td>{r.created_at ? new Date(r.created_at).toLocaleTimeString() : '-'}</td>
                    <td>{r.asset_number || '-'}</td>
                    <td>{r.mac || '-'}</td>
                    <td>{r.profile_name || '-'}</td>
                    <td>{r.test_name}</td>
                    <td>{r.status}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>
    </Layout>
  )
}
