import Layout from '../src/components/Layout'
import ProfilesCard from '../src/components/ProfilesCard'

export default function ProfilesPage() {
  return (
    <Layout>
      <div className="container">
        <div className="topbar">
          <a href="/" className="btn-secondary">← Back to Dashboard</a>
          <div className="titles">
            <h2>Profiles</h2>
            <p className="subtitle">Create, edit, clone, import/export profiles and choose a default.</p>
          </div>
        </div>
        <ProfilesCard />
      </div>
      <style jsx>{`
        .container { width: 100%; padding: 0 16px; box-sizing: border-box; }
        .topbar { display: flex; align-items: center; justify-content: space-between; gap: 16px; margin: 8px 0 16px; flex-wrap: wrap; }
        .titles { display: flex; flex-direction: column; gap: 4px; margin-left: auto; }
        h2 { margin: 0; }
        .subtitle { margin: 0; color: var(--muted); }
      `}</style>
    </Layout>
  )
}
