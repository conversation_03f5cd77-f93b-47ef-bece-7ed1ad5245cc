import { useEffect, useMemo, useState } from 'react'
import { apiGet, apiPost } from '../src/lib/api'
import Card from '../src/components/Card'
import Layout from '../src/components/Layout'

export default function SettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const [baseUrl, setBaseUrl] = useState('')
  const [apiKey, setApiKey] = useState('')
  const [apiKeySet, setApiKeySet] = useState(false)
  const [showKey, setShowKey] = useState(false)

  const urlValid = useMemo(() => {
    if (!baseUrl) return true
    try {
      const u = new URL(baseUrl)
      return u.protocol === 'https:' || u.hostname === 'localhost'
    } catch { return false }
  }, [baseUrl])

  useEffect(() => {
    let mounted = true
    async function load() {
      setLoading(true)
      setError('')
      try {
        const data = await apiGet('/api/settings/website')
        if (!mounted) return
        setBaseUrl(data.base_url || '')
        setApiKey('') // never prefill key
        setApiKeySet(Boolean(data.api_key_set))
      } catch (e) {
        setError(String(e.message || e))
      } finally {
        setLoading(false)
      }
    }
    load()
    return () => { mounted = false }
  }, [])

  async function onSubmit(e) {
    e.preventDefault()
    if (!urlValid) {
      setError('Please enter a valid URL (https recommended).')
      return
    }
    setSaving(true)
    setSuccess('')
    setError('')
    try {
      const payload = { base_url: baseUrl || null }
      if (apiKey && apiKey.trim()) payload.api_key = apiKey.trim()
      const data = await apiPost('/api/settings/website', payload)
      setApiKey('') // clear after save
      setApiKeySet(Boolean(data.api_key_set))
      setSuccess('Settings saved')
    } catch (e) {
      setError(String(e.message || e))
    } finally {
      setSaving(false)
    }
  }

  return (
    <Layout>
      <div style={{ maxWidth: 1120, margin: '2rem auto', padding: '1rem' }}>
        <div style={{ display:'flex', justifyContent:'space-between', alignItems:'center', gap:12 }}>
          <div>
            <h1>Settings</h1>
            <p>Connect your Nexus Website so this hub can manage license credits automatically.</p>
          </div>
          <a className="btn-secondary" href="/">← Back to Home</a>
        </div>

      {loading ? (
        <p>Loading…</p>
      ) : (
        <div className="grid" style={{ display:'grid', gridTemplateColumns: 'minmax(640px, 1fr) 420px', gap: 24 }}>
          <Card title="Nexus Website Connection">
            <form onSubmit={onSubmit} className="form">
              {error ? (
                <div className="alert error">{error}</div>
              ) : null}
              {success ? (
                <div className="alert success">{success}</div>
              ) : null}

              <label>
                <span>Website Base URL</span>
                <input
                  id="baseUrl"
                  type="url"
                  placeholder="https://nexus.example.com"
                  value={baseUrl}
                  onChange={(e) => setBaseUrl(e.target.value)}
                  className={!urlValid ? 'invalid' : ''}
                />
                <small className="hint">No trailing slash. Example: https://nexus.example.com</small>
                {!urlValid && <small className="error">Invalid URL. Use HTTPS in production.</small>}
              </label>

              <label>
                <span>API Key</span>
                <div className="row">
                  <input
                    id="apiKey"
                    type={showKey ? 'text' : 'password'}
                    placeholder={apiKeySet ? 'Stored key is hidden. Enter a new key to replace it.' : 'Paste API key here'}
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                  />
                  <button
                    type="button"
                    className="btn-secondary toggle-btn"
                    aria-pressed={showKey}
                    title={apiKey ? (showKey ? 'Hide key' : 'Show key') : 'Enter a key to enable'}
                    disabled={!apiKey}
                    onClick={() => apiKey && setShowKey(v => !v)}
                  >{showKey ? 'Hide' : 'Show'}</button>
                </div>
                <small className="hint">{apiKeySet ? 'A key is configured but cannot be displayed. Enter a new key to replace it.' : 'No API key is configured yet.'}</small>
              </label>

              <div className="actions">
                <button className="btn-secondary" type="submit" disabled={saving || !urlValid}>{saving ? 'Saving…' : 'Save Settings'}</button>
              </div>
            </form>
          </Card>

          <Card title="Help">
            <div className="help">
              <dl className="meta">
                <dt>Status</dt>
                <dd><strong>{apiKeySet ? 'Connected (key set)' : 'Not connected'}</strong></dd>
                <dt>Website</dt>
                <dd className="mono">{baseUrl || 'Not set'}</dd>
              </dl>
              <div className="actions-row">
                <button
                  type="button"
                  className="btn-secondary"
                  disabled={!baseUrl}
                  onClick={() => { if (baseUrl) window.open(baseUrl, '_blank', 'noopener,noreferrer') }}
                >Open Website</button>
                <button
                  type="button"
                  className="btn-secondary"
                  disabled={!baseUrl}
                  onClick={async () => { try { if (baseUrl) await navigator.clipboard.writeText(baseUrl) } catch {} }}
                >Copy URL</button>
              </div>
              <div className="sep" />
              <ul className="list">
                <li>
                  <div className="q">Where do I get an API key?</div>
                  <div className="a">From your Nexus Website admin → Account → API Keys.</div>
                </li>
                <li>
                  <div className="q">What does the key do?</div>
                  <div className="a">Allows this hub to consume and complete credit operations during device wiping/diagnostics.</div>
                </li>
                <li>
                  <div className="q">Safe to rotate keys?</div>
                  <div className="a">Yes. Entering a new key here replaces the existing one.</div>
                </li>
              </ul>
            </div>
          </Card>
        </div>
      )}

        <style jsx>{`
        .form { display:flex; flex-direction:column; gap:14px; }
        label { display:flex; flex-direction:column; gap:6px; }
        input { width:100%; padding:10px; border-radius:8px; border:1px solid var(--border); background:#0f1521; color:var(--text); }
        input.invalid { border-color: #b91c1c; }
        .row { display:flex; gap:8px; align-items:stretch; }
        .row input { flex:1; }
        .toggle-btn { height: 38px; display:inline-flex; align-items:center; }
        .actions { margin-top:6px; }
        .actions-row { display:flex; gap:8px; }
        .alert { padding:10px; border-radius:8px; margin-bottom:8px; }
        .alert.error { background:#2a0b0b; color:#fca5a5; border:1px solid #7f1d1d; }
        .alert.success { background:#0b2a18; color:#86efac; border:1px solid #14532d; }
        .hint { color: var(--muted); }
        .help { display:flex; flex-direction:column; gap:16px; font-size:15px; line-height:1.7; }
        .help .meta { display:grid; grid-template-columns: auto 1fr; column-gap:12px; row-gap:6px; margin:0; }
        .help .meta dt { color: var(--muted); }
        .help .meta dd { margin:0; }
        .help .mono { font-family: ui-monospace, SFMono-Regular, Menlo, monospace; word-break: break-all; }
        .help .sep { height:1px; background: var(--border); margin: 6px 0; }
        .help .list { margin:0; padding-left: 18px; }
        .help .list li { margin-bottom:12px; }
        .help .q { font-weight:600; }
        `}</style>
      </div>
    </Layout>
  )
}
