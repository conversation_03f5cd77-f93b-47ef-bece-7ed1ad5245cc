#!/usr/bin/env python3
"""
Entry point for launching the Crucible Web UI.
"""
import os
import sys
import logging

# Ensure the project root is in the Python path
# This allows imports like `from web_server.app import app`
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from web_server.app import app, server_logger
except ImportError as e:
    print(f"Error importing web server application. Ensure you are in the project root directory and all dependencies are installed: {e}", file=sys.stderr)
    sys.exit(1)

if __name__ == '__main__':
    # Configure logging for direct execution if not already configured in app.py for __main__
    # This ensures logs are visible when running this script directly.
    if not server_logger.hasHandlers() or all(isinstance(h, logging.NullHandler) for h in server_logger.handlers):
        # Clear existing null handlers if any
        for handler in server_logger.handlers[:]:
            server_logger.removeHandler(handler)

        handler = logging.StreamHandler(sys.stdout)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        server_logger.addHandler(handler)
        server_logger.setLevel(logging.DEBUG) # Or logging.INFO
        server_logger.info("Configured basic logging for run_web_ui.py direct execution.")

    server_logger.info("Starting Crucible Web UI...")
    # Recommended to run Flask app with a production-ready WSGI server like Gunicorn or Waitress for non-debug.
    # For development, app.run() is fine.
    # The app.run in web_server/app.py under if __name__ == '__main__' already sets debug=True, host, port.
    # If this script is the primary way to run, we might want to configure host/port/debug here.
    # However, web_server.app's own __main__ block will take precedence if it's structured that way.
    # For clarity, let's explicitly call app.run here with desired settings.

    # Default host and port
    HOST = os.environ.get('FLASK_RUN_HOST', '127.0.0.1')
    PORT = int(os.environ.get('FLASK_RUN_PORT', 5000))
    DEBUG_MODE = os.environ.get('FLASK_DEBUG', '1') == '1' # Default to debug mode

    try:
        app.run(host=HOST, port=PORT, debug=DEBUG_MODE)
    except RuntimeError as e:
        if "Cannot run the application directly" in str(e):
            # This can happen if web_server.app.py is run directly elsewhere.
            # The goal here is for THIS script to be the entry point.
            server_logger.error("RuntimeError caught, possibly due to nested app.run() calls if web_server.app was already run directly.")
            server_logger.error("If web_server.app is running, this script doesn't need to call app.run().")
            server_logger.error("Otherwise, ensure web_server.app.py does not call app.run() when imported.")
        else:
            server_logger.error(f"An unexpected error occurred: {e}", exc_info=True)
    except Exception as e:
        server_logger.error(f"Failed to start the web server: {e}", exc_info=True)
        sys.exit(1)
