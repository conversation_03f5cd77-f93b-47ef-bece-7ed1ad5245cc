const { PrismaClient } = require('@prisma/client');
const Stripe = require('stripe');
require('dotenv').config();

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

async function testWebhookFlow() {
  try {
    console.log('=== TESTING WEBHOOK FLOW ===\n');
    
    // Test 1: Check Stripe configuration
    console.log('1. Checking Stripe configuration...');
    console.log(`Stripe Secret Key: ${process.env.STRIPE_SECRET_KEY ? 'Set (test key)' : 'NOT SET'}`);
    console.log(`Webhook Secret: ${process.env.STRIPE_WEBHOOK_SECRET ? 'Set' : 'NOT SET'}`);
    console.log();
    
    // Test 2: Check if we can connect to Stripe
    console.log('2. Testing Stripe connection...');
    try {
      const account = await stripe.accounts.retrieve();
      console.log(`✓ Connected to Stripe account: ${account.id}`);
    } catch (error) {
      console.log(`✗ Stripe connection failed: ${error.message}`);
    }
    console.log();
    
    // Test 3: Simulate a webhook payload
    console.log('3. Testing webhook payload processing...');
    
    // Create a mock payment intent succeeded event
    const mockPaymentIntent = {
      id: 'pi_test_123456789',
      amount: 10000, // $100.00 in cents
      currency: 'usd',
      metadata: {
        type: 'credits',
        userId: 'a21565e2-caf6-4919-b795-bfde83390dc1', // Your user ID
        creditAmount: '100',
        productId: 'credits_100',
        userEmail: '<EMAIL>'
      }
    };
    
    console.log('Mock payment intent:', JSON.stringify(mockPaymentIntent, null, 2));
    
    // Test the credit creation logic directly
    console.log('\n4. Testing credit creation logic...');
    
    const { id, amount, currency, metadata } = mockPaymentIntent;
    
    if (metadata.type === 'credits') {
      const userId = metadata.userId;
      if (!userId || userId === 'guest') {
        console.log('✗ Would skip credit DB write for guest/unknown user');
        return;
      }
      
      const creditAmount = parseInt(metadata.creditAmount);
      const price = amount / 100; // Convert from cents
      
      console.log(`Processing credit purchase:`);
      console.log(`- User ID: ${userId}`);
      console.log(`- Credit Amount: ${creditAmount}`);
      console.log(`- Price: $${price}`);
      
      try {
        await prisma.$transaction(async (tx) => {
          // Create transaction record
          const transaction = await tx.transaction.create({
            data: {
              userId,
              type: 'CREDIT_PURCHASE',
              amount: price,
              currency,
              status: 'COMPLETED',
              stripePaymentIntentId: id,
              metadata: {
                creditAmount,
                productId: metadata.productId
              }
            }
          });
          
          console.log(`✓ Created transaction record: ${transaction.id}`);
          
          // Add credits to user account
          const credit = await tx.credit.create({
            data: {
              userId,
              amount: creditAmount,
              remainingAmount: creditAmount,
              purchasePrice: price,
              transactionId: transaction.id,
              expiresAt: null // Credits never expire
            }
          });
          
          console.log(`✓ Created credit record: ${credit.id}`);
        });
        
        console.log(`✓ Successfully added ${creditAmount} credits for user ${userId}`);
        
        // Verify the credits were added
        const userCredits = await prisma.credit.aggregate({
          where: { 
            userId: userId,
            remainingAmount: { gt: 0 }
          },
          _sum: { remainingAmount: true }
        });
        
        console.log(`✓ User now has ${userCredits._sum.remainingAmount || 0} total credits`);
        
      } catch (error) {
        console.log(`✗ Error creating credits: ${error.message}`);
        console.log(error);
      }
    }
    
  } catch (error) {
    console.error('Error testing webhook flow:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testWebhookFlow();
